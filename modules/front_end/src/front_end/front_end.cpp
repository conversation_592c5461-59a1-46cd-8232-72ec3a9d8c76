/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "hyper_vision/front_end/front_end.h"

namespace robosense::front_end
{
  int FrontEnd::Init(const YAML::Node& cfg_node)
  {
    cfg_node_ = cfg_node;

    RINFO << name() << "initialized successfully. All algorithm modules are disabled by default...";

    SetEnablePostProcess(true);

    return 0;
  }

  int FrontEnd::Start()
  {
    RINFO << name() << "started successfully...";
    return 0;
  }

  int FrontEnd::Stop()
  {
    std::scoped_lock lock(slam_mutex_, post_mutex_, skeleton_mutex_, depth_mtx_);

    if (post_algo_)
    {
      try {
        post_algo_->Stop();
        post_algo_.reset();
      }
      catch (const std::exception& e) {
        RWARN << name() << "Exception when stopping Postprocess algorithm module: " << e.what();
        return -1;
      }
    }

    if (slam_algo_)
    {
      try {
        slam_algo_->Stop();
        slam_algo_.reset();
      }
      catch (const std::exception& e) {
        RWARN << name() << "Exception when stopping Slam algorithm module: " << e.what();
        return -2;
      }
    }

    if (skeleton_algo_)
    {
      try {
        skeleton_algo_->Stop();
        skeleton_algo_.reset();
      }
      catch (const std::exception& e) {
        RWARN << name() << "Exception when stopping SkeletonDetection algorithm module: " << e.what();
        return -3;
      }
    }

    if (depth_algo_)
    {
      try {
        depth_algo_->Stop();
        depth_algo_.reset();
      }
      catch (const std::exception& e) {
        RWARN << name() << "Exception when stopping DepthEstimation algorithm module: " << e.what();
        return -4;
      }
    }

    return 0;
  }

  int FrontEnd::Reset()
  {
    if (post_algo_)
      ResetPostProcess();

    if (slam_algo_)
      ResetSlam();

    if (skeleton_algo_)
      ResetSkeleton();

    if (depth_algo_)
      ResetImageDepth();

    return 0;
  }

  int FrontEnd::ResetPostProcess()
  {
    std::lock_guard<std::mutex> lock(post_mutex_);

    if (post_algo_ == nullptr)
    {
      RINFO << name() << "Postprocess algorithm module is disabled, skipping reset...";
      return 0;
    }

    RINFO << name() << "Stopping existing Postprocess algorithm module before reset...";
    try {
      post_algo_->Stop();
      post_algo_.reset();
    }
    catch (const std::exception& e) {
      RWARN << name() << "Exception when stopping Postprocess algorithm module: " << e.what();
    }

    return ResetPostProcessInternal();
  }

  int FrontEnd::ResetSlam()
  {
    std::lock_guard<std::mutex> lock(slam_mutex_);

    if (slam_algo_ == nullptr)
    {
      RINFO << name() << "Slam algorithm module is disabled, skipping reset...";
      return 0;
    }

    RINFO << name() << "Stopping existing Slam algorithm module before reset...";
    try {
      slam_algo_->Stop();
      slam_algo_.reset();
    }
    catch (const std::exception& e) {
      RWARN << name() << "Exception when stopping Slam algorithm module: " << e.what();
    }

    return ResetSlamInternal();
  }

  int FrontEnd::ResetImageDepth()
  {
    std::lock_guard<std::mutex> lock(depth_mtx_);

    if (depth_algo_ == nullptr)
    {
      RINFO << name() << "DepthEstimation algorithm module is disabled, skipping reset...";
      return 0;
    }

    RINFO << name() << "Stopping existing DepthEstimation algorithm module before reset...";
    try {
      depth_algo_->Stop();
      depth_algo_.reset();
    }
    catch (const std::exception& e) {
      RWARN << name() << "Exception when stopping DepthEstimation algorithm module: " << e.what();
    }

    return ResetImageDepthInternal();
  }

  int FrontEnd::ResetSkeleton()
  {
    std::lock_guard<std::mutex> lock(skeleton_mutex_);

    if (skeleton_algo_ == nullptr)
    {
      RINFO << name() << "SkeletonDetection algorithm module is disabled, skipping reset...";
      return 0;
    }

    RINFO << name() << "Stopping existing SkeletonDetection algorithm module before reset...";
    try {
      skeleton_algo_->Stop();
      skeleton_algo_.reset();
    }
    catch (const std::exception& e) {
      RWARN << name() << "Exception when stopping SkeletonDetection algorithm module: " << e.what();
    }

    return ResetSkeletonInternal();
  }

  int FrontEnd::SetEnablePostProcess(bool is_enable)
  {
    std::lock_guard<std::mutex> lock(post_mutex_);

    if (is_enable)
    {
      if (post_algo_ == nullptr)
      {
        int ret = ResetPostProcessInternal();
        if (ret != 0)
          return ret;

        ret = post_algo_->Start();
        if (ret != 0)
        {
          RERROR << name() << "Postprocess algorithm module start failed: ret = " << ret;
          post_algo_->Stop();
          post_algo_.reset();
          return -10;
        }
        
        RINFO << name() << "Postprocess algorithm module enabled and started successfully...";
        return 0;
      }
      else
      {
        RWARN << "post_algo_ is not nullptr when enabling Postprocess";
        return -1;
      }
    }
    else
    {
      RINFO << name() << "Disabling Postprocess...";
      if (post_algo_ != nullptr)
      {
        post_algo_->Stop();
        post_algo_.reset();
      }
      RINFO << name() << "Postprocess disabled successfully...";
      return 0;
    }
  }

  int FrontEnd::SetEnableSlam(bool is_enable)
  {
    std::lock_guard<std::mutex> lock(slam_mutex_);

    if (is_enable)
    {
      if (slam_algo_ == nullptr)
      {
        int ret = ResetSlamInternal();
        if (ret != 0)
          return ret;

        ret = slam_algo_->Start();
        if (ret != 0)
        {
          RERROR << name() << "Slam algorithm module start failed: ret = " << ret;
          slam_algo_->Stop();
          slam_algo_.reset();
          return -10;
        }
        
        RINFO << name() << "Slam algorithm module enabled and started successfully...";
        return 0;
      }
      else
      {
        RWARN << "slam_algo_ is not nullptr when enabling slam";
        return -1;
      }
    }
    else
    {
      RINFO << name() << "Disabling Slam...";
      if (slam_algo_ != nullptr)
      {
        slam_algo_->Stop();
        slam_algo_.reset();
      }
      RINFO << name() << "Slam disabled successfully...";
      return 0;
    }
  }

  int FrontEnd::EnableSlamRelocalization(bool is_enable)
  {
    std::lock_guard<std::mutex> lock(slam_mutex_);
    if (slam_algo_)
      slam_algo_->SetRelocationFlag(is_enable);

    return 0;
  }

  int FrontEnd::EnableSlamOfflineOpt(bool isEnable)
  {
    return 0;
  }

  int FrontEnd::SetEnableImageDepth(bool is_enable)
  {
    std::lock_guard<std::mutex> lock(depth_mtx_);

    if (is_enable)
    {
      if (depth_algo_ == nullptr)
      {
        int ret = ResetImageDepthInternal();
        if (ret != 0)
          return ret;
        
        ret = depth_algo_->Start();
        if (ret != 0)
        {
          RERROR << name() << "DepthEstimation algorithm module start failed: ret = " << ret;
          depth_algo_->Stop();
          depth_algo_.reset();
          return -10;
        }
        
        RINFO << name() << "DepthEstimation algorithm module enabled and started successfully...";
        return 0;
      }
      else
      {
        RWARN << "depth_algo_ is not nullptr when enabling DepthEstimation";
        return -1;
      }
    }
    else
    {
      RINFO << name() << "Disabling DepthEstimation...";
      if (depth_algo_ != nullptr)
      {
        depth_algo_->Stop();
        depth_algo_.reset();
      }
      RINFO << name() << "DepthEstimation disabled successfully...";
      return 0;
    }
  }

  int FrontEnd::SetEnableSkeleton(bool is_enable)
  {
    std::lock_guard<std::mutex> lock(skeleton_mutex_);

    if (is_enable)
    {
      if (skeleton_algo_ == nullptr)
      {
        int ret = ResetSkeletonInternal();
        if (ret != 0)
          return ret;
        
        ret = skeleton_algo_->Start();
        if (ret != 0)
        {
          RERROR << name() << "SkeletonDetection algorithm module start failed: ret = " << ret;
          skeleton_algo_->Stop();
          skeleton_algo_.reset();
          return -10;
        }
        
        RINFO << name() << "SkeletonDetection algorithm module enabled and started successfully...";
        return 0;
      }
      else
      {
        RWARN << "skeleton_algo_ is not nullptr when enabling SkeletonDetection";
        return -1;
      }
    }
    else
    {
      RINFO << name() << "Disabling SkeletonDetection...";
      if (skeleton_algo_ != nullptr)
      {
        skeleton_algo_->Stop();
        skeleton_algo_.reset();
      }
      RINFO << name() << "SkeletonDetection disabled successfully...";
      return 0;
    }
  }

  void FrontEnd::AddData(const shared_ptr<robosense::common::MotionFrame>& msg_ptr)
  {
    std::scoped_lock lock(slam_mutex_, post_mutex_);

    if (post_algo_)
      post_algo_->AddData(msg_ptr);

    if (slam_algo_)
      slam_algo_->AddData(msg_ptr);
  }

  void FrontEnd::AddData(const shared_ptr<robosense::common::DepthFrame>& msg_ptr)
  {
    std::scoped_lock lock(slam_mutex_, post_mutex_, skeleton_mutex_, depth_mtx_);

    if (post_algo_)
      post_algo_->AddData(msg_ptr);

    if (slam_algo_)
      slam_algo_->AddData(msg_ptr);

    if (skeleton_algo_)
      skeleton_algo_->AddData(msg_ptr);

    if (depth_algo_)
      depth_algo_->AddData(msg_ptr);
  }

  void FrontEnd::AddDataImage(const shared_ptr<robosense::common::ImageFrame>& msg_ptr)
  {
    std::scoped_lock lock(slam_mutex_, post_mutex_);
    if (post_algo_)
      post_algo_->AddData(msg_ptr);

    if (slam_algo_)
      slam_algo_->AddData(msg_ptr);
  }

  void FrontEnd::AddDataImageLeft(const std::shared_ptr<robosense::common::ImageFrame>& msg_ptr)
  {
    std::scoped_lock scoped_lock(skeleton_mutex_, depth_mtx_);

    if (skeleton_algo_)
      skeleton_algo_->AddDataImageLeft(msg_ptr);

    if (depth_algo_)
      depth_algo_->AddDataImageLeft(msg_ptr);
  }

  void FrontEnd::AddDataImageRight(const std::shared_ptr<robosense::common::ImageFrame>& msg_ptr)
  {
    std::scoped_lock scoped_lock(skeleton_mutex_, depth_mtx_);

    if (skeleton_algo_)
      skeleton_algo_->AddDataImageRight(msg_ptr);

    if (depth_algo_)
      depth_algo_->AddDataImageRight(msg_ptr);
  }

  void FrontEnd::SetCallback(const std::function<void(const postprocess::PostprocessOutputMsg::Ptr&)>& callback)
  {
    std::lock_guard<std::mutex> lock(post_mutex_);
    post_callback_ = callback;

    if (post_algo_)
      post_algo_->SetCallback(post_callback_);
  }

  void FrontEnd::SetCallback(const function<void(const slam::SlamOutputMsg::Ptr&)>& callback)
  {
    std::lock_guard<std::mutex> lock(slam_mutex_);
    slam_callback_ = callback;

    if (slam_algo_)
      slam_algo_->SetCallback(slam_callback_);
  }

  void FrontEnd::SetCallback(const std::function<void(const imagedepth::ImageDepthOutputMsg::Ptr&)>& callback)
  {
    std::lock_guard<std::mutex> lock(depth_mtx_);
    depth_callback_ = callback;

    if (depth_algo_)
      depth_algo_->SetCallback(depth_callback_);
  }

  void FrontEnd::SetCallback(const std::function<void(const skeleton::SkeletonOutputMsg::Ptr&)>& callback)
  {
    std::lock_guard<std::mutex> lock(skeleton_mutex_);
    skeleton_callback_ = callback;

    if (skeleton_algo_)
      skeleton_algo_->SetCallback(skeleton_callback_);
  }

  int FrontEnd::ResetPostProcessInternal()
  {
    // 注意：调用此函数时应该已经持有 post_mutex_ 锁

    postprocess::MultiSensorPostprocess::Ptr new_post_algo;
    try {
      new_post_algo.reset(new postprocess::MultiSensorPostprocess());
    }
    catch (const std::exception& e) {
      RERROR << name() << "Failed to create new postprocess algorithm module: " << e.what();
      post_algo_.reset();
      return -1;
    }

    int ret = new_post_algo->Init(cfg_node_);
    if (ret != 0)
    {
      RERROR << name() << "Postprocess algorithm module initialize failed: ret = " << ret;
      post_algo_.reset();
      return -2;
    }

    post_algo_ = new_post_algo;

    if (post_callback_)
      post_algo_->SetCallback(post_callback_);

    RINFO << name() << "Postprocess algorithm module initialized successfully...";

    return 0;
  }

  int FrontEnd::ResetSlamInternal()
  {
    // 注意：调用此函数时应该已经持有 slam_mutex_ 锁

    slam::Slam::Ptr new_slam_algo;
    try {
      new_slam_algo.reset(new slam::Slam());
    }
    catch (const std::exception& e) {
      RERROR << name() << "Failed to create new slam algorithm module: " << e.what();
      slam_algo_.reset();
      return -1;
    }

    int ret = new_slam_algo->Init(cfg_node_);
    if (ret != 0)
    {
      RERROR << name() << "Slam algorithm module initialize failed: ret = " << ret;
      slam_algo_.reset();
      return -2;
    }

    slam_algo_ = new_slam_algo;

    if (slam_callback_)
      slam_algo_->SetCallback(slam_callback_);

    RINFO << name() << "Slam algorithm module initialized successfully...";

    return 0;
  }

  int FrontEnd::ResetSkeletonInternal()
  {
    // 注意：调用此函数时应该已经持有 skeleton_mutex_ 锁

    skeleton::SkeletonDetection::Ptr new_skeleton_algo;
    try {
      new_skeleton_algo.reset(new skeleton::SkeletonDetection());
    }
    catch (const std::exception& e) {
      RERROR << name() << "Failed to create new SkeletonDetection algorithm module: " << e.what();
      skeleton_algo_.reset();
      return -1;
    }

    int ret = new_skeleton_algo->Init(cfg_node_);
    if (ret != 0)
    {
      RERROR << name() << "SkeletonDetection algorithm module initialize failed: ret = " << ret;
      skeleton_algo_.reset();
      return -2;
    }

    skeleton_algo_ = new_skeleton_algo;

    if (skeleton_callback_)
      skeleton_algo_->SetCallback(skeleton_callback_);

    RINFO << name() << "SkeletonDetection algorithm module initialized successfully...";

    return 0;
  }

  int FrontEnd::ResetImageDepthInternal()
  {
    // 注意：调用此函数时应该已经持有 depth_mtx_ 锁

    imagedepth::ImageDepth::Ptr new_depth_algo;
    try {
      new_depth_algo.reset(new imagedepth::ImageDepth());
    }
    catch (const std::exception& e) {
      RERROR << name() << "Failed to create new DepthEstimation algorithm module: " << e.what();
      depth_algo_.reset();
      return -1;
    }

    int ret = new_depth_algo->Init(cfg_node_);
    if (ret != 0)
    {
      RERROR << name() << "DepthEstimation algorithm module initialize failed: ret = " << ret;
      depth_algo_.reset();
      return -2;
    }

    depth_algo_ = new_depth_algo;

    if (depth_callback_)
      depth_algo_->SetCallback(depth_callback_);

    RINFO << name() << "DepthEstimation algorithm module initialized successfully...";

    return 0;
  }

} // namespace robosense::front_end
