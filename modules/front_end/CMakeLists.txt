cmake_minimum_required(VERSION 3.5)

project(front_end)

# 启用位置无关代码（对动态库或跨平台静态库有利）
set(CMAKE_POSITION_INDEPENDENT_CODE ON)

# 平台特定设置
if(WIN32)
    add_compile_options(/W4)   # 启用第 4 级警告
    set(CUSTOM_FLANN_DIR  "C:\\Program Files (x86)\\flann\\include")
elseif(UNIX AND NOT APPLE)
    add_compile_options(-Wall -Wextra -pedantic)  # 启用常见警告
endif()

add_definitions(-DDEBUG_LEVEL=0)

set(SRCS "")
set(SUB_DIR "include" "src")

foreach (dir ${SUB_DIR})
    file(GLOB_RECURSE tmp_srcs ${dir}/*.cpp ${dir}/*.h)
    list(APPEND SRCS ${tmp_srcs})
endforeach ()

add_library(front_end STATIC ${SRCS})

target_include_directories(front_end PUBLIC
        include
        ${PCL_INCLUDE_DIRS}
        ${CUSTOM_FLANN_DIR}
)

target_link_libraries(front_end PUBLIC
        common
        slam
        postprocess
        image_depth
        skeleton_detection
        ${PCL_LIBRARIES}
)

target_compile_definitions(front_end PRIVATE MODULE_NAME="front_end")
