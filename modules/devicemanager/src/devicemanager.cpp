#include "hyper_vision/devicemanager/devicemanager.h"

#define TIMESTAMP_NS                                                           \
  (std::chrono::time_point_cast<std::chrono::nanoseconds>(                     \
       std::chrono::system_clock::now())                                       \
       .time_since_epoch()                                                     \
       .count())
#define TIMESTAMP_US                                                           \
  (std::chrono::time_point_cast<std::chrono::microseconds>(                    \
       std::chrono::system_clock::now())                                       \
       .time_since_epoch()                                                     \
       .count())
#define TIMESTAMP_MS                                                           \
  (std::chrono::time_point_cast<std::chrono::milliseconds>(                    \
       std::chrono::system_clock::now())                                       \
       .time_since_epoch()                                                     \
       .count())
#define TIMESTAMP_S                                                            \
  (std::chrono::time_point_cast<std::chrono::seconds>(                         \
       std::chrono::system_clock::now())                                       \
       .time_since_epoch()                                                     \
       .count())

namespace robosense {
namespace device {

DeviceManager::DeviceManager() {
  _usb_ctx = nullptr;
  _kill_handler_thread = 1;
  _start = false;
  _inited = false;
  _event_cb = nullptr;
  _input_image_format = robosense::lidar::frame_format::FRAME_FORMAT_NV12;
  _stopProcessingThreads = false;

  // 启动消息处理线程
  _pointCloudProcessingThread =
      std::thread(&DeviceManager::processPointCloudQueue, this);
  _imageProcessingThread = std::thread(&DeviceManager::processImageQueue, this);
  _imuProcessingThread = std::thread(&DeviceManager::processImuQueue, this);
}

DeviceManager::~DeviceManager() { stop(); }

bool DeviceManager::init(int input_image_format, int input_image_fps,
                         int input_imu_fps, const bool isEnableDebug) {
  if (_inited) {
    return true;
  }

  if (input_image_format >= robosense::lidar::frame_format::FRAME_FORMAT_ANY &&
      input_image_format <=
          robosense::lidar::frame_format::FRAME_FORMAT_YUV422) {
    _input_image_format =
        static_cast<robosense::lidar::frame_format>(input_image_format);
  }
  _input_image_fps = input_image_fps;
  _input_imu_fps = input_imu_fps;

  _is_stoping_ = false;
  _enable_debug = isEnableDebug;
  RINFO << "DeviceManager: _enable_debug = " << _enable_debug;

  int res;
  if (!_usb_ctx) {
    int res = libusb_init(&_usb_ctx);
    if (res < 0) {
      _usb_ctx = nullptr;
      RERROR << "DeviceManager: Initial USB Context Failed: res = " << res;
      return false;
    }
  }

  auto thread_usb_event = [this](void *ptr) {
    struct timeval tv = {0, 100000};
    while (!_kill_handler_thread) {
      libusb_handle_events_timeout_completed(_usb_ctx, &tv,
                                             &_kill_handler_thread);
    }
  };

  _kill_handler_thread = 0;
  try {
    _usb_thread = std::thread(thread_usb_event, this);
  } catch (const std::system_error &e) {
    _kill_handler_thread = 0;
    RERROR << "DeviceManager: Create USB Event Thread Failed !";
    return false;
  }

  _start = true;
  try {
    _m_thread = std::thread(&DeviceManager::hotplugWorkThread, this);
  } catch (const std::system_error &e) {
    _start = false;
    RERROR << "DeviceManager: Create Device Hotplug Thread Failed !";
    return false;
  }

  _inited = true;

  return true;
}

int DeviceManager::stop() {
  if (!_inited) {
    return 0;
  }

  if (_start) {
    _start = false;
    if (_m_thread.joinable()) {
      _m_thread.join();
    }
  }

  {
    std::lock_guard<std::mutex> lg(_is_stoping_mtx_);
    _is_stoping_ = true;
  }

  int ret = closeDevices();
  if (ret != 0) {
    RERROR << "DeviceManager: Close All Devices Failed !";
    return -1;
  } else {
    RINFO << "DeviceManager: Close All Device Successed !";
  }

  if (!_kill_handler_thread) {
    _kill_handler_thread = 1;
    if (_usb_thread.joinable()) {
      _usb_thread.join();
    }
  }

  if (_usb_ctx) {
    libusb_exit(_usb_ctx);
    _usb_ctx = nullptr;
  }

  // 释放排队线程
  if (_stopProcessingThreads == false) {
    _stopProcessingThreads = true;
    _pointCloudQueueCond.notify_all();
    if (_pointCloudProcessingThread.joinable()) {
      _pointCloudProcessingThread.join();
    }
    _imageQueueCond.notify_all();
    if (_imageProcessingThread.joinable()) {
      _imageProcessingThread.join();
    }
    _imuQueueCond.notify_all();
    if (_imuProcessingThread.joinable()) {
      _imuProcessingThread.join();
    }
  }

  _inited = false;

  return 0;
}

int DeviceManager::openDevice(const std::string &device_uuid) {
  AINFO << "DeviceManager: Start Open Device: device_uuid = " << device_uuid;
  {
    std::lock_guard<std::mutex> lg(_devices_map_mtx);
    auto iterMap = _devices_map.find(device_uuid);
    if (iterMap == _devices_map.end()) {
      RERROR << "DeviceManager: device uuid = " << device_uuid
             << " Not Attach !";
      return -1;
    } else if (iterMap->second.driver_ptr != nullptr) {
      RINFO << "DeviceManager: device uuid = " << device_uuid
            << " Already Open, Not Need Open Again !";
      return 0;
    }

    // 增加帧率Monitor
    if (_device_freq_monitor_mapper.find(device_uuid) ==
        _device_freq_monitor_mapper.end()) {
      robosense::device::DeviceFreqMonitor::Ptr monitorPtr;
      try {
        monitorPtr.reset(new robosense::device::DeviceFreqMonitor());
      } catch (...) {
        RERROR << "DeviceManager: Malloc Device Frequence Monitor Failed !";
        return -2;
      }
      monitorPtr->initTimestampBuffers(_input_image_fps, 10, _input_imu_fps);
      _device_freq_monitor_mapper.insert({device_uuid, monitorPtr});
    } else {
      _device_freq_monitor_mapper[device_uuid]->resetTimestampBuffers();
    }

    RS_DEVICE_DRIVER_PTR driver_ptr;
    try {
      driver_ptr.reset(new RS_DEVICE_DRIVER());
    } catch (...) {
      RERROR << "DeviceManager: device uuid = " << device_uuid
             << " Malloc Device Driver Failed !";
      return -3;
    }
    std::weak_ptr<DeviceManager> weak_this = shared_from_this();

    const std::string &regDeviceUUID = device_uuid;
    const auto &get_pc_cb =
        std::bind(&DeviceManager::localGetPointCloudCallback, this);
    const auto &put_pc_cb =
        [weak_this, regDeviceUUID](
            const std::shared_ptr<PointCloudT<RsPointXYZIRT>> &msgPtr) {
          // AERROR << "RUN HERE";
          auto share_this = weak_this.lock();
          if (!share_this || !msgPtr) {
            return;
          }
          // AERROR << "RUN HERE";
          share_this->localRunPointCloudCallback(msgPtr, regDeviceUUID);
        };
    driver_ptr->regPointCloudCallback(get_pc_cb, put_pc_cb);

    const auto &get_image_cb =
        std::bind(&DeviceManager::localGetImageDataCallback, this);
    const auto &put_image_cb =
        [weak_this, regDeviceUUID](
            const std::shared_ptr<robosense::lidar::ImageData> &msgPtr) {
          // AERROR << "RUN HERE";
          auto share_this = weak_this.lock();
          if (!share_this || !msgPtr) {
            return;
          }
          // AERROR << "RUN HERE";
          share_this->localRunImageCallback(msgPtr, regDeviceUUID);
        };
    driver_ptr->regImageDataCallback(get_image_cb, put_image_cb);

    const auto &get_imu_cb =
        std::bind(&DeviceManager::localGetImuDataCallback, this);
    const auto &put_imu_cb =
        [weak_this, regDeviceUUID](
            const std::shared_ptr<robosense::lidar::ImuData> &msgPtr) {
          // AERROR << "RUN HERE";
          auto share_this = weak_this.lock();
          if (!share_this || !msgPtr) {
            return;
          }
          // AERROR << "RUN HERE";
          share_this->localRunImuCallback(msgPtr, regDeviceUUID);
        };
    driver_ptr->regImuDataCallback(get_imu_cb, put_imu_cb);

    const auto &exception_cb =
        [weak_this, regDeviceUUID](const robosense::lidar::Error &error) {
          // AERROR << "RUN HERE";
          auto share_this = weak_this.lock();
          if (!share_this) {
            return;
          }
          // AERROR << "RUN HERE";
          share_this->localRunExceptionCallback(error, regDeviceUUID);
        };
    driver_ptr->regExceptionCallback(exception_cb);

    robosense::lidar::RSDriverParam driverParam;
    driverParam.input_type = robosense::lidar::InputType::USB;
    driverParam.lidar_type = robosense::lidar::RS_AC1;
    driverParam.input_param.enable_image = true; // enable image output
    driverParam.input_param.device_uuid = iterMap->second.uuid;
    driverParam.input_param.image_format = _input_image_format;
    driverParam.input_param.image_fps = _input_image_fps;
    driverParam.input_param.imu_fps = _input_imu_fps;
    RINFO << "device_uuid = " << device_uuid
          << ", _input_image_format = " << static_cast<int>(_input_image_format)
          << ", _input_imu_fps = " << _input_imu_fps
          << ", _input_image_fps = " << _input_image_fps;

    bool isSuccess = driver_ptr->init(driverParam);
    if (!isSuccess) {
      RERROR << "DeviceManager: device uuid = " << device_uuid
             << " Inital Failed !";
      return -4;
    }

    isSuccess = driver_ptr->start();
    if (!isSuccess) {
      RERROR << "DeviceManager: device uuid = " << device_uuid
             << " Start Failed !";
      return -7;
    }

    iterMap->second.is_pause = false;
    iterMap->second.driver_ptr = driver_ptr;
  }

  int ret;
  std::string device_info;
  ret = queryDeviceInfo(device_uuid, device_info);
  if (!ret) {
    RINFO << "DeviceManager: device uuid = " << device_uuid
          << ", device info = " << device_info;
  } else {
    RERROR << "DeviceManager: device uuid = " << device_uuid
           << " Query Device Info Failed !";
    return -5;
  }

  std::string calib_info;
  ret = queryDeviceCalibInfo(device_uuid, calib_info);
  if (!ret) {
    RINFO << "DeviceManager: device uuid = " << device_uuid
          << ", device calib info = " << calib_info;
  } else {
    RERROR << "DeviceManager: device uuid = " << device_uuid
           << " Query Device Calib Info Failed !";
    return -6;
  }

  return 0;
}

int DeviceManager::closeDevice(const std::string &device_uuid,
                               const bool isMetux) {
  RINFO << "DeviceManager: Start Close Device: device uuid = " << device_uuid;
  if (isMetux) {
    RS_DEVICE_DRIVER_PTR driver_ptr;
    {
      std::lock_guard<std::mutex> lg(_devices_map_mtx);
      auto iterMap = _devices_map.find(device_uuid);
      if (iterMap == _devices_map.end()) {
        RERROR << "DeviceManager: Device uuid = " << device_uuid
               << " Already Detach !";
        return 0;
      } else if (iterMap->second.driver_ptr == nullptr) {
        RINFO << "DeviceManager: Device uuid = " << device_uuid
              << " Not Open, So Not Need Close !";
        return 0;
      }
      driver_ptr = iterMap->second.driver_ptr;
    }

    if (driver_ptr != nullptr) {
      driver_ptr->stop();
      driver_ptr.reset();
    }

    {
      std::lock_guard<std::mutex> lg(_devices_map_mtx);
      auto iterMap = _devices_map.find(device_uuid);
      if (iterMap != _devices_map.end()) {
        iterMap->second.driver_ptr.reset();
      }
    }
  } else {
    auto iterMap = _devices_map.find(device_uuid);
    if (iterMap == _devices_map.end()) {
      RERROR << "DeviceManager: Device uuid = " << device_uuid
             << " Already Detach !";
      return 0;
    } else if (iterMap->second.driver_ptr == nullptr) {
      RINFO << "DeviceManager: Device uuid = " << device_uuid
            << " Not Open, So Not Need Close !";
      return 0;
    }
    iterMap->second.driver_ptr->stop();
    iterMap->second.driver_ptr.reset();
  }
  return 0;
}

int DeviceManager::pauseDevice(const std::string &device_uuid,
                               const bool isPauseOp) {
#if ENABLE_DEVICE_OP_PAUSE
  std::lock_guard<std::mutex> lg(_devices_map_mtx);
  auto iterMap = _devices_map.find(device_uuid);
  if (iterMap == _devices_map.end()) {
    RERROR << "DeviceManager: Device uuid = " << device_uuid
           << " Not Attach, So Not Need " << (isPauseOp ? "Pause" : "Play")
           << " !";
    return -1;
  }
  iterMap->second.is_pause = isPauseOp;

  return 0;
#else
  RERROR << "DeviceManager: Not Support Op Pause Now !";
  return 0;
#endif // ENABLE_DEVICE_OP_PAUSE
}

int DeviceManager::closeDevices() {
  std::lock_guard<std::mutex> lg(_devices_map_mtx);
  while (!_devices_map.empty()) {
    const std::string &device_uuid = _devices_map.begin()->second.uuid;
    int ret = closeDevice(device_uuid, false);
    if (ret != 0) {
      RERROR << "DeviceManager: device uuid = " << device_uuid
             << " Close Failed: ret = " << ret;
      return -1;
    }
    _devices_map.erase(_devices_map.begin());
  }
  _devices_map.clear();

  return 0;
}

int DeviceManager::queryDeviceInfo(const std::string &device_uuid,
                                   std::string &devInfo) {
  uint8_t req_data[1024];
  std::vector<uint8_t> receive;
  int timeout[] = {1000, 100, 1000};
  int ret = 0;
  bool status;

  devInfo.clear();
  {
    std::lock_guard<std::mutex> lg(_devices_map_mtx);
    auto iterMap = _devices_map.find(device_uuid);
    if (iterMap != _devices_map.end()) {
      if (iterMap->second.device_info.empty() == false &&
          iterMap->second.is_device_info_need_update == false) {
        devInfo = iterMap->second.device_info;
        return 0;
      }
    } else {
      RERROR << "Open device first !";
      return -1;
    }
  }

  std::this_thread::sleep_for(std::chrono::milliseconds(500));

  {
    std::lock_guard<std::mutex> lg(_devices_map_mtx);
    auto iterMap = _devices_map.find(device_uuid);
    if (iterMap != _devices_map.end()) {
      if (iterMap->second.driver_ptr) {
        status = iterMap->second.driver_ptr->get_start();
        iterMap->second.driver_ptr->stop_recv();

        req_data[0] = 0x22;
        req_data[1] = 0x00;
        auto hid_resp = iterMap->second.driver_ptr->usb_transfer(
            req_data, 2, timeout, receive);
        if (hid_resp != HID_RESP_HEAD_ERROR && hid_resp != HID_RESP_CRC_ERROR) {
          devInfo = std::string(receive.begin() + 2, receive.end());
          iterMap->second.device_info = devInfo;
        } else {
          RERROR << "DeviceManager: device uuid = " << device_uuid
                 << " Can Not Query Device Info !";
          ret = -1;
        }
      } else {
        RERROR << "DeviceManager: device uuid = " << device_uuid
               << " Can Not Query Device Info !";
        ret = -1;
      }

      if (iterMap->second.driver_ptr) {
        if (status) {
          iterMap->second.driver_ptr->set_start(false);
          iterMap->second.driver_ptr->start_recv();
        } else {
          iterMap->second.driver_ptr->start_recv();
        }
      }
    }
  }
  // 更新缓存
  {
    std::lock_guard<std::mutex> lg(_devices_map_mtx);
    auto iterMap = _devices_map.find(device_uuid);
    if (iterMap != _devices_map.end()) {
      if (iterMap->second.is_device_info_need_update == true) {
        iterMap->second.device_info = devInfo;
        iterMap->second.is_device_info_need_update = false;
      }
    }
  }

  return ret;
}

int DeviceManager::queryDeviceCalibInfo(const std::string &device_uuid,
                                        std::string &calibInfo) {
  std::vector<uint8_t> receive;
  int timeout[] = {1000, 100, 1000};
  int ret = 0;
  bool status;

  calibInfo.clear();
  {
    std::lock_guard<std::mutex> lg(_devices_map_mtx);
    auto iterMap = _devices_map.find(device_uuid);
    if (iterMap != _devices_map.end()) {
      if (iterMap->second.device_calib_info.empty() == false &&
          iterMap->second.is_device_calib_info_need_update == false) {
        calibInfo = iterMap->second.device_calib_info;
        return 0;
      }
    } else {
      RERROR << "Open device first !";
      return -1;
    }
  }

  std::this_thread::sleep_for(std::chrono::milliseconds(500));
  {
    std::lock_guard<std::mutex> lg(_devices_map_mtx);
    auto iterMap = _devices_map.find(device_uuid);
    if (iterMap != _devices_map.end()) {
      if (iterMap->second.driver_ptr) {
        iterMap->second.driver_ptr->stop_recv();
        status = iterMap->second.driver_ptr->get_start();
        iterMap->second.driver_ptr->stop_recv();
        auto buildCommand = [](uint8_t header1, uint8_t header2,
                               const std::string &cmd) {
          std::vector<uint8_t> req;
          req.reserve(2 + cmd.size());
          req.push_back(header1);
          req.push_back(header2);
          req.insert(req.end(), cmd.begin(), cmd.end());
          return req;
        };
        std::vector<uint8_t> req_data =
            buildCommand(0x31, 0x07, strCalibReadCmd);
        auto hid_resp = iterMap->second.driver_ptr->usb_transfer(
            const_cast<uint8_t *>(req_data.data()), req_data.size(), timeout,
            receive);
        if (hid_resp != HID_RESP_HEAD_ERROR && hid_resp != HID_RESP_CRC_ERROR) {
          calibInfo = std::string(receive.begin() + 2, receive.end());
          iterMap->second.device_calib_info = calibInfo;
        } else {
          RERROR << "DeviceManager: device uuid = " << device_uuid
                 << " Can Not Query Device Calib Info !";
          ret = -1;
        }
      } else {
        RERROR << "DeviceManager: device uuid = " << device_uuid
               << " Can Not Query Device Calib Info !";
        ret = -1;
      }

      if (iterMap->second.driver_ptr) {
        if (status) {
          iterMap->second.driver_ptr->set_start(false);
          iterMap->second.driver_ptr->start_recv();
        } else {
          iterMap->second.driver_ptr->start_recv();
        }
      }
    }
  }
  // 更新缓存
  {
    std::lock_guard<std::mutex> lg(_devices_map_mtx);
    auto iterMap = _devices_map.find(device_uuid);
    if (iterMap != _devices_map.end()) {
      if (iterMap->second.is_device_calib_info_need_update == true) {
        iterMap->second.device_calib_info = calibInfo;
        iterMap->second.is_device_calib_info_need_update = false;
      }
    }
  }

  std::this_thread::sleep_for(std::chrono::milliseconds(5000));

  return ret;
}

int DeviceManager::writeDeviceCalibInfo(const std::string &device_uuid,
                                        const std::string &calib_json) {
  std::vector<uint8_t> receive;
  int timeout[] = {1000, 100, 1000};

  std::this_thread::sleep_for(std::chrono::milliseconds(500));
  {
    std::lock_guard<std::mutex> lg(_devices_map_mtx);
    auto iterMap = _devices_map.find(device_uuid);
    if (iterMap != _devices_map.end()) {
      if (iterMap->second.driver_ptr) {
        auto ptr = iterMap->second.driver_ptr;
        auto buildCommand = [](uint8_t header1, uint8_t header2,
                               const std::string &cmd) {
          std::vector<uint8_t> req;
          req.reserve(2 + cmd.size());
          req.push_back(header1);
          req.push_back(header2);
          req.insert(req.end(), cmd.begin(), cmd.end());
          return req;
        };
        std::vector<uint8_t> req_data = buildCommand(0x31, 0x06, calib_json);
        auto hid_resp =
            ptr->usb_transfer(const_cast<uint8_t *>(req_data.data()),
                              req_data.size(), timeout, receive);
        if (hid_resp != HID_RESP_HEAD_ERROR && hid_resp != HID_RESP_CRC_ERROR) {
          iterMap->second.is_device_calib_info_need_update = true;
          RINFO << "Write Device Calib Info Success !";
        } else {
          RERROR << "Write Device Calib Info Failed !";
          return -2;
        }
      }
    } else {
      RERROR << "DeviceManager: device uuid = " << device_uuid
             << " Not Attach, So Can Not Write Device Calib Info !";
      return -3;
    }
  }
  std::this_thread::sleep_for(std::chrono::milliseconds(5000));

  return 0;
}

int DeviceManager::startOtaDevice(const std::string &device_uuid,
                                  const std::string &ota_bin_file_path) {
  int ret = 0;

  if (device_uuid.empty()) {
    RERROR << "uuid is empty !";
    return -1;
  } else if (ota_bin_file_path.empty()) {
    RERROR << "ota_bin_file_path is empty !";
    return -2;
  }

  std::filesystem::path ota_bin_path(ota_bin_file_path);
  if (!std::filesystem::exists(ota_bin_path)) {
    RERROR << "ota_bin_file_path = " << ota_bin_file_path << " not exist !";
    return -3;
  }

  _cancel_transfer = false;
  _device_ota_finished = false;
  _device_ota_success = false;
  _ota_uuid = device_uuid;

  try {
    ota_thread_ptr_.reset(new std::thread(
        std::bind(&DeviceManager::otaProcess, this, ota_bin_file_path)));
  } catch (...) {
    return -1;
  }
  std::this_thread::sleep_for(std::chrono::milliseconds(500));

  return 0;
}

bool DeviceManager::otaProcess(const std::string &upload_transfer_file_path) {
  int ret;

  RINFO << "Start OTA: " << upload_transfer_file_path;
  auto startTime = std::chrono::system_clock::now();
  auto endTime = startTime;
  auto duration =
      std::chrono::duration_cast<std::chrono::seconds>(endTime - startTime);
  std::string binString = otaBinFileToString(upload_transfer_file_path, 100);

  if (binString.empty()) {
    RERROR << "OTA Upload Bin File Path Is Empty: "
           << upload_transfer_file_path;
    _device_ota_finished = true;
    return false;
  }

  if (binString.find(sensorBinFileKey) != std::string::npos) {
    RINFO << "UpgradeOpType: OTA";
    std::string before_ota_device_info;
    if (!queryDeviceInfo(_ota_uuid, before_ota_device_info)) {
      RINFO << "Before Upgrade Device Info: " << before_ota_device_info;
    }
    std::string before_ota_device_calib_info;
    if (!queryDeviceCalibInfo(_ota_uuid, before_ota_device_calib_info)) {
      RINFO << "Before Upgrade Device Calib Info: "
            << before_ota_device_calib_info;
    }
  } else {
    RERROR << "OTA Bin File Is Invalid: " << upload_transfer_file_path;
    _device_ota_finished = true;
    return false;
  }

  if (!otaUpload(upload_transfer_file_path)) {
    RERROR << "Upgrade: transfer file failed: " << upload_transfer_file_path;
    _device_ota_finished = true;
    return false;
  }

  RINFO << "Upgrade: transfer file successful: " << upload_transfer_file_path;

  int reopen_count = 0;
  bool is_reopen_status = false;
  auto buildCommand = [](uint8_t header1, uint8_t header2,
                         const std::string &cmd) {
    std::vector<uint8_t> req;
    req.reserve(2 + cmd.size());
    req.push_back(header1);
    req.push_back(header2);
    req.insert(req.end(), cmd.begin(), cmd.end());
    return req;
  };

  {
    std::lock_guard<std::mutex> lg(_devices_map_mtx);
    RS_DEVICE_DRIVER_PTR ptr = nullptr;
    auto iterMap = _devices_map.find(_ota_uuid);
    if (iterMap != _devices_map.end()) {
      ptr = iterMap->second.driver_ptr;
    } else {
      RERROR << "DeviceManager: device uuid = " << _ota_uuid
             << " Not Attach, So Can Not Start Ota !";
      return false;
    }

    if (ptr) {
      ptr->send_cmd(HID_REQ_IMU_UPLOAD_STOP);
      ptr->stop_recv();

      const auto otaReq = buildCommand(0x31, 0x05, strOtaCmd);
      std::vector<uint8_t> receive;
      int timeout[] = {1000, 100, 1000};
      ret = ptr->usb_transfer(const_cast<uint8_t *>(otaReq.data()),
                              otaReq.size(), timeout, receive);
      if (ret == HID_RESP_HEAD_ERROR && ret == HID_RESP_CRC_ERROR) {
        RERROR << " usb_transfer error !";
        return false;
      }

      const auto rebootReq = buildCommand(0x31, 0x05, strRebootCmd);
      ret = ptr->usb_transfer(const_cast<uint8_t *>(rebootReq.data()),
                              rebootReq.size(), timeout, receive);
      if (ret == HID_RESP_HEAD_ERROR && ret == HID_RESP_CRC_ERROR) {
        RWARN << "Reboot command may not be acknowledged !";
      }

      ptr->start_recv();
      ptr->set_recv(false);
    }
  }

  std::this_thread::sleep_for(
      std::chrono::milliseconds(RS_UPGRADE_SLEEP_TH_MS));

  auto timeoutTime = std::chrono::system_clock::now() +
                     std::chrono::milliseconds(RS_UPGRADE_SYS_TIMEOUT_TH_MS);
  while (std::chrono::system_clock::now() < timeoutTime) {
    if (_cancel_transfer) {
      _device_ota_finished = true;
      return false;
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    RINFO << "Waiting for reboot...";
    if (otaGetDevType(_ota_uuid)) {
      RINFO << "Reboot sucessfully";
      _device_ota_success = true;
      break;
    }
  }

  // 打印统计信息
  endTime = std::chrono::system_clock::now();
  duration =
      std::chrono::duration_cast<std::chrono::seconds>(endTime - startTime);
  if (!queryDeviceInfo(_ota_uuid, _after_ota_device_info)) {
    RINFO << "After Upgrade Device Infos: " << _after_ota_device_info;
    RINFO << "Upgrade Cost Time = " << std::to_string(duration.count());
  }
  _device_ota_finished = true;

  RINFO << "Finish OTA: " << upload_transfer_file_path;

  return _device_ota_success == true;
}

std::string DeviceManager::otaBinFileToString(const std::string &filePath,
                                              size_t numBytes) {
  std::ifstream file(filePath, std::ios::binary);
  if (!file) {
    return "";
  }

  std::vector<char> buffer(numBytes);

  file.read(buffer.data(), numBytes);

  if (file.fail() && !file.eof()) {
    return "";
  }

  std::streamsize bytesRead = file.gcount();

  file.close();

  return std::string(buffer.data(), bytesRead);
}

bool DeviceManager::otaUpload(const std::string &path) {
  AINFO << "upload Transfer file Path: " << path;

  std::filesystem::path _m_path(path);
  if (std::filesystem::exists(_m_path)) {
    if (std::filesystem::is_directory(_m_path)) {
      try {
        for (const auto &entry : std::filesystem::directory_iterator(path)) {
          if (entry.is_regular_file()) {
            if (!otaFileTransfer(entry.path()))
              return false;
          }
        }
      } catch (const std::filesystem::filesystem_error &e) {
        RERROR << "otaUpload: Error accessing the directory: "
               << std::string(e.what());
      }
    } else if (std::filesystem::is_regular_file(_m_path)) {
      if (!otaFileTransfer(_m_path))
        return false;
    } else {
      RERROR << "otaUpload: Is neither a file nor a directory!";
    }
  } else {
    RERROR << "otaUpload: Path is not exist!";
    return false;
  }
  return true;
}

bool DeviceManager::otaFileTransfer(const std::filesystem::path &path) {
  int transfer_length = 0;
  int len;
  uint8_t req_data[1024];
  std::vector<uint8_t> receive;
  std::string file_name;
  size_t file_size;
  int timeout[] = {1000, 100, 1000};

  file_name = path.filename().string();
  file_size = std::filesystem::file_size(path);

  RINFO << "hid_transfer: file_name = " << file_name
        << ", file_size = " << std::to_string(file_size);

  {
    std::lock_guard<std::mutex> lg(_devices_map_mtx);
    RS_DEVICE_DRIVER_PTR ptr = nullptr;
    auto iterMap = _devices_map.find(_ota_uuid);
    if (iterMap != _devices_map.end()) {
      ptr = iterMap->second.driver_ptr;
    } else {
      RERROR << "DeviceManager: device uuid = " << _ota_uuid
             << " Not Attach, So Can Not Start Ota !";
      return false;
    }

    if (ptr) {
      ptr->send_cmd(HID_REQ_IMU_UPLOAD_STOP);
      ptr->stop_recv();

      req_data[0] = 0x34;
      req_data[1] = 0x00;
      req_data[2] = file_size & 0xFF;
      req_data[3] = (file_size >> 8) & 0xFF;
      req_data[4] = (file_size >> 16) & 0xFF;
      req_data[5] = (file_size >> 24) & 0xFF;
      std::string md5Hash = md5::calculateFileMd5(path.string());
      memcpy(req_data + 6, md5Hash.data(), md5Hash.size());
      memcpy(req_data + 6 + md5Hash.size(), file_name.data(), file_name.size());
      len = 6 + md5Hash.size() + file_name.size();
      auto hid_resp = ptr->usb_transfer(req_data, len, timeout, receive);
      RINFO << "hid_resp:" << hid_resp;
      if (hid_resp == HID_RESP_DOWNLOAD_FILE_EXIST) {
        return true;
      } else if (hid_resp != HID_RESP_DOWNLOAD_START) {
        RERROR << "hid_transfer: Hid resp download start failed!";
        return false;
      }

      size_t TransferedSize = 0;
      size_t BlockCnt = 1;
      size_t SendDataSize = 1000;

      std::ifstream file(path.string(), std::ios::binary);
      if (!file) {
        RERROR << "hid_transfer: Failed to open file: " << path.string();
      }
      file.seekg(TransferedSize, std::ios::beg);
      std::vector<uint8_t> file_data(SendDataSize);

      timeout[0] = -1;
      while (
          file.read(reinterpret_cast<char *>(file_data.data()), SendDataSize) ||
          file.gcount() > 0) {
        if (_cancel_transfer) {
          RERROR << "hid_transfer: files transfer cancel!";
          return false;
        }
        file_data.resize(file.gcount());

        req_data[0] = 0x36;
        req_data[1] = 0x00;
        req_data[2] = BlockCnt;
        memcpy(req_data + 3, file_data.data(), file_data.size());
        len = 3 + file_data.size();
        if (ptr->usb_transfer(req_data, len, timeout, receive) !=
            HID_RESP_DOWNLOAD_DATA) {
          RERROR << "hid_transfer: Hid resp download data failed!";
          return false;
        }

        TransferedSize += file_data.size();
        BlockCnt++;
        file_data.resize(SendDataSize);
      }

      timeout[2] = 3000;
      req_data[0] = 0x37;
      req_data[1] = 0x00;
      memcpy(req_data + 2, md5Hash.data(), md5Hash.size());
      len = 2 + md5Hash.size();
      if (ptr->usb_transfer(req_data, len, timeout, receive) !=
          HID_RESP_DOWNLOAD_STOP) {
        RERROR << "hid_transfer: Hid resp download stop failed!";
        return false;
      }

      ptr->start_recv();
    }
  }
  return true;
}

bool DeviceManager::otaGetDevType(std::string &uuid) {
  std::lock_guard<std::mutex> lg(_devices_map_mtx);
  for (auto iterMap = _devices_map.begin(); iterMap != _devices_map.end();
       ++iterMap) {
    const DeviceInfoItem &deviceItem = iterMap->second;
    if (uuid == deviceItem.uuid) {
      return deviceItem.is_usb_mode;
    }
  }

  return false;
}

int DeviceManager::cancleOtaDevice(const std::string &device_uuid) {
  if (_ota_uuid == device_uuid) {
    if (!_device_ota_finished) {
      _cancel_transfer = true;
    }
  }

  return 0;
}

int DeviceManager::finishOtaDevice(const std::string &device_uuid) {
  if (_ota_uuid == device_uuid) {
    if (ota_thread_ptr_) {
      if (ota_thread_ptr_->joinable()) {
        ota_thread_ptr_->join();
      }
      ota_thread_ptr_.reset();
    }

    RINFO << "DeviceManager: Ota Finish Successed !";

    // 正常结束时
    if (!_after_ota_device_info.empty()) {
      std::lock_guard<std::mutex> lg(_devices_map_mtx);
      auto iterMap = _devices_map.find(device_uuid);
      if (iterMap != _devices_map.end()) {
        iterMap->second.device_info = _after_ota_device_info;
      }
    }
  }
  return 0;
}

int DeviceManager::checkOtaFinish(const std::string &device_uuid,
                                  bool &status) {
  if (_ota_uuid == device_uuid) {
    status = _device_ota_finished;
    RINFO << "DeviceManager: OtaManager Check Ota Finish Status: " << status
          << ", ota_uuid = " << device_uuid;
    return 0;
  }

  return -1;
}

int DeviceManager::checkOtaSuccess(const std::string &device_uuid,
                                   bool &status) {
  if (_ota_uuid == device_uuid) {
    status = _device_ota_success;
    RINFO << "DeviceManager: OtaManager Check Ota Success Status: " << status
          << ", ota_uuid = " << device_uuid;
    return 0;
  }

  return -1;
}

std::set<std::string> DeviceManager::getDevices() {
  std::lock_guard<std::mutex> lg(_devices_map_mtx);
  std::set<std::string> uuids;
  for (auto iterMap = _devices_map.begin(); iterMap != _devices_map.end();
       ++iterMap) {
    const DeviceInfoItem &deviceItem = iterMap->second;
    if (deviceItem.is_attach) {
      uuids.insert(iterMap->first);
    }
  }
  return uuids;
}

bool DeviceManager::getDeviceType(std::string &uuid, bool &is_usb_mode) {
  std::lock_guard<std::mutex> lg(_devices_map_mtx);
  for (auto iterMap = _devices_map.begin(); iterMap != _devices_map.end();
       ++iterMap) {
    const DeviceInfoItem &deviceItem = iterMap->second;
    if (uuid == deviceItem.uuid) {
      is_usb_mode = deviceItem.is_usb_mode;
      return true;
    }
  }

  return false;
}

void DeviceManager::regDeviceEventCallback(
    const RS_DEVICE_EVENT_CALLBACK &event_cb) {
  if (event_cb) {
    _event_cb = event_cb;
  }
}

void DeviceManager::regPointCloudCallback(
    const RS_DEVICE_POINTCLOUD_CALLBACK &pc_cb) {
  if (pc_cb) {
    _pc_cb = pc_cb;
  }
}

void DeviceManager::regImageDataCallback(
    const RS_DEVICE_IMAGE_CALLBACK &image_cb) {
  if (image_cb) {
    _image_cb = image_cb;
  }
}

void DeviceManager::regImuDataCallback(const RS_DEVICE_IMU_CALLBACK &imu_cb) {
  if (imu_cb) {
    _imu_cb = imu_cb;
  }
}

void DeviceManager::getDeviceFreqInfo(const std::string &uuid,
                                      float &image_freq, float &depth_freq,
                                      float &imu_freq) {
  image_freq = 0;
  depth_freq = 0;
  imu_freq = 0;
  if (_device_freq_monitor_mapper.find(uuid) !=
      _device_freq_monitor_mapper.end()) {
    _device_freq_monitor_mapper[uuid]->getDeviceFreqInfo(image_freq, depth_freq,
                                                         imu_freq);
  }
}

std::shared_ptr<PointCloudT<RsPointXYZIRT>>
DeviceManager::localGetPointCloudCallback() {
  std::shared_ptr<PointCloudT<RsPointXYZIRT>> pointCloudMsgPtr;
  try {
    pointCloudMsgPtr.reset(new PointCloudT<RsPointXYZIRT>());
  } catch (...) {
    return nullptr;
  }
  return pointCloudMsgPtr;
}

std::shared_ptr<robosense::lidar::ImageData>
DeviceManager::localGetImageDataCallback() {
  std::shared_ptr<robosense::lidar::ImageData> imageDataMsgPtr;
  try {
    imageDataMsgPtr.reset(new robosense::lidar::ImageData());
  } catch (...) {
    return nullptr;
  }
  return imageDataMsgPtr;
}

std::shared_ptr<robosense::lidar::ImuData>
DeviceManager::localGetImuDataCallback() {
  std::shared_ptr<robosense::lidar::ImuData> imuDataMsgPtr;
  try {
    imuDataMsgPtr.reset(new robosense::lidar::ImuData());
  } catch (...) {
    return nullptr;
  }
  return imuDataMsgPtr;
}

void DeviceManager::localRunPointCloudCallback(
    const std::shared_ptr<PointCloudT<RsPointXYZIRT>> &msgPtr,
    const std::string &uuid) {
#if ENABLE_DEVICEMANAGER_DEBUG
  RINFO << "DeviceManager: localRunPointCloudCallback 1";
#endif // ENABLE_DEVICEMANAGER_DEBUG
  if (_is_stoping_.load()) {
    return;
  }

#if ENABLE_DEVICE_OP_PAUSE
  {
#if ENABLE_DEVICEMANAGER_DEBUG
    RINFO << "DeviceManager: localRunPointCloudCallback 2";
#endif // ENABLE_DEVICEMANAGER_DEBUG
    std::lock_guard<std::mutex> lg(_devices_map_mtx);
    if (!(_devices_map.find(uuid) != _devices_map.end() &&
          !_devices_map[uuid].is_pause)) {
      return;
    }
  }
#endif // ENABLE_DEVICE_OP_PAUSE

  if (msgPtr) {
    // 更新帧率统计
    const uint64_t timestampNs = TIMESTAMP_NS;
    if (_device_freq_monitor_mapper[uuid]) {
      _device_freq_monitor_mapper[uuid]->updateDepthTimestamp(timestampNs);
    }

    // 加入缓冲区
    std::lock_guard<std::mutex> lg(_pointCloudQueueMutex);
    _pointCloudQueue.push(std::make_pair(msgPtr, uuid));
    _pointCloudQueueCond.notify_one(); // 通知等待的线程
#if ENABLE_DEVICEMANAGER_DEBUG
    RINFO << "DeviceManager: localRunPointCloudCallback 3";
#endif // ENABLE_DEVICEMANAGER_DEBUG
  }
}

void DeviceManager::localRunImageCallback(
    const std::shared_ptr<robosense::lidar::ImageData> &msgPtr,
    const std::string &uuid) {
#if ENABLE_DEVICEMANAGER_DEBUG
  RINFO << "DeviceManager: localRunImageCallback 1";
#endif // ENABLE_DEVICEMANAGER_DEBUG
  if (_is_stoping_.load()) {
    return;
  }

#if ENABLE_DEVICE_OP_PAUSE
  {
#if ENABLE_DEVICEMANAGER_DEBUG
    RINFO << "DeviceManager: localRunImageCallback 2";
#endif // ENABLE_DEVICEMANAGER_DEBUG
    std::lock_guard<std::mutex> lg(_devices_map_mtx);
    if (!(_devices_map.find(uuid) != _devices_map.end() &&
          !_devices_map[uuid].is_pause)) {
      return;
    }
  }
#endif // ENABLE_DEVICE_OP_PAUSE

  if (msgPtr) {
    // 更新帧率统计
    const uint64_t timestampNs = TIMESTAMP_NS;
    if (_device_freq_monitor_mapper[uuid]) {
      _device_freq_monitor_mapper[uuid]->updateImageTimestamp(timestampNs);
    }

    // 加入缓冲区
    std::lock_guard<std::mutex> lg(_imageQueueMutex);
    _imageQueue.push(std::make_pair(msgPtr, uuid));
    _imageQueueCond.notify_one(); // 通知等待的线程
#if ENABLE_DEVICEMANAGER_DEBUG
    RINFO << "DeviceManager: localRunImageCallback 3";
#endif // ENABLE_DEVICEMANAGER_DEBUG
  }
}

void DeviceManager::localRunImuCallback(
    const std::shared_ptr<robosense::lidar::ImuData> &msgPtr,
    const std::string &uuid) {
#if ENABLE_DEVICEMANAGER_DEBUG
  RINFO << "DeviceManager: localRunImuCallback 1";
#endif // ENABLE_DEVICEMANAGER_DEBUG
  if (_is_stoping_.load()) {
    return;
  }

#if ENABLE_DEVICE_OP_PAUSE
  {
#if ENABLE_DEVICEMANAGER_DEBUG
    RINFO << "DeviceManager: localRunImuCallback 2";
#endif // ENABLE_DEVICEMANAGER_DEBUG
    std::lock_guard<std::mutex> lg(_devices_map_mtx);
    if (!(_devices_map.find(uuid) != _devices_map.end() &&
          !_devices_map[uuid].is_pause)) {
      return;
    }
  }
#endif // ENABLE_DEVICE_OP_PAUSE

  if (msgPtr) {
    // 更新帧率统计
    const uint64_t timestampNs = TIMESTAMP_NS;
    if (_device_freq_monitor_mapper[uuid]) {
      _device_freq_monitor_mapper[uuid]->updateImuTimestamp(timestampNs);
    }

    // 加入缓冲区
    std::lock_guard<std::mutex> lg(_imuQueueMutex);
    _imuQueue.push(std::make_pair(msgPtr, uuid));
    _imuQueueCond.notify_one(); // 通知等待的线程
#if ENABLE_DEVICEMANAGER_DEBUG
    RINFO << "DeviceManager: localRunImuCallback 3";
#endif // ENABLE_DEVICEMANAGER_DEBUG
  }
}

void DeviceManager::localRunExceptionCallback(
    const robosense::lidar::Error &error, const std::string &uuid) {
#if ENABLE_DEVICEMANAGER_DEBUG
  RINFO << "DeviceManager: localRunExceptionCallback 1";
#endif // ENABLE_DEVICEMANAGER_DEBUG
  if (_is_stoping_.load()) {
    return;
  }
#if ENABLE_DEVICEMANAGER_DEBUG
  RINFO << "DeviceManager: localRunExceptionCallback 2";
#endif // ENABLE_DEVICEMANAGER_DEBUG
  switch (error.error_code) {
  case robosense::lidar::ERRCODE_DEVICE_DISCONNECTED: {
    // 物理断开时
    {
      std::lock_guard<std::mutex> lg(_devices_map_mtx);
      auto iterMap = _devices_map.find(uuid);
      if (iterMap != _devices_map.end() &&
          _devices_disconnect_map.find(uuid) == _devices_disconnect_map.end()) {
        _devices_disconnect_map.insert({iterMap->first, iterMap->second});
      }
    }
    RINFO << "RS_DRIVER: Sensor Exception: USB Device Disconnect: uuid = "
          << uuid;
    break;
  }
  default: {
    RERROR << "RS_DRIVER: Sensor Exception: " << error.toString();
    break;
  }
  }
}

int DeviceManager::findDevices(
    std::map<std::string, DeviceInfoItem> &devices_map) {
#if ENABLE_DEVICEMANAGER_DEBUG
  RINFO << "DeviceManager: findDevices 1";
#endif // ENABLE_DEVICEMANAGER_DEBUG
  devices_map.clear();
  libusb_device **usb_dev_list;
  ssize_t num_usb_devices = libusb_get_device_list(_usb_ctx, &usb_dev_list);
  if (num_usb_devices < 0) {
    RERROR << "Get USB Device List Failed: ret = " << num_usb_devices;
    return -1;
  }

  for (int i = 0; i < num_usb_devices; i++) {
    libusb_device *device = usb_dev_list[i];
    libusb_device_descriptor desc;
    if (libusb_get_device_descriptor(device, &desc) != LIBUSB_SUCCESS) {
      RERROR << "Get USB Device Descriptor Failed !";
      continue;
    }
    if (desc.idVendor == VENDOR_ID && desc.idProduct == PRODUCT_ID ||
        desc.idVendor == VENDOR_ID_RNDIS &&
            desc.idProduct == PRODUCT_ID_RNDIS) {
      libusb_device_handle *handle;
      std::string uuid("0");
      std::string compare("0");
      if (libusb_open(device, &handle) == 0) {
        unsigned char serial[256];
        int ret = libusb_get_string_descriptor_ascii(handle, desc.iSerialNumber,
                                                     serial, sizeof(serial));
        if (ret > 0) {
          uuid = std::string(reinterpret_cast<char *>(serial));
        }
        libusb_close(handle);

        // RINFO << "findDevice ===> " << uuid
        //       << ", i_sn = " << desc.iSerialNumber;

        // 获取uuid 成功
        if (uuid != compare && devices_map.find(uuid) == devices_map.end()) {
          DeviceInfoItem dev_i;
          // dev_i.dev = device;
          dev_i.is_attach = true;
          dev_i.uuid = uuid;
          dev_i.i_sn = desc.iSerialNumber;
          dev_i.driver_ptr = nullptr;
          if (desc.idVendor == VENDOR_ID && desc.idProduct == PRODUCT_ID) {
            dev_i.is_usb_mode = true;
          } else {
            dev_i.is_usb_mode = false;
          }
          dev_i.ac_device_type =
              common::RS_AC_DEVICE_TYPE::RS_AC_DEVICE_AC1; // 设备类型，后面可能涉及判断

          // libusb_ref_device(device);
          // 未识别AC类型的，不上报给界面
          if (dev_i.ac_device_type != common::RS_AC_DEVICE_TYPE::RS_AC_DEVICE_UNKNOWN) {
            devices_map.insert({uuid, dev_i});
          }
        }
      }
#ifdef _WIN32
      else {
        RWARN << "Open USB Device Failed, Maybe This USB Device Busy !";
      }
#endif // _WIN32
    }
  }

  // Windows场景时USB不能重复打开，所以需要强制加入已经打开的USB设备
#ifdef _WIN32
  {
    std::lock_guard<std::mutex> lg(_devices_map_mtx);
    for (auto iterMap = _devices_map.begin(); iterMap != _devices_map.end();
         ++iterMap) {
      if (iterMap->second.driver_ptr != nullptr &&
          devices_map.find(iterMap->first) == devices_map.end()) {
        devices_map.insert({iterMap->first, iterMap->second});
      }
    }
  }
#endif // _WIN32

  // 释放设备
  libusb_free_device_list(usb_dev_list, 1);

#if ENABLE_DEVICEMANAGER_DEBUG
  RINFO << "DeviceManager: findDevices 2";
#endif // ENABLE_DEVICEMANAGER_DEBUG

  return 0;
}

void DeviceManager::hotplugWorkThread() {
  std::vector<DeviceInfoItem> attachDevices;
  std::map<std::string, DeviceInfoItem> detachDevices;

  while (_start) {
#if ENABLE_DEVICEMANAGER_DEBUG
    RINFO << "DeviceManager::hotplugWorkThread";
#endif // ENABLE_DEVICEMANAGER_DEBUG
    // Step1: Search Device(s)
    std::map<std::string, DeviceInfoItem> deviceItems;
    int ret = findDevices(deviceItems);
    if (ret != 0) {
      RERROR << "find Devices Failed: ret = " << ret;
      std::this_thread::sleep_for(std::chrono::milliseconds(100));
      continue;
    }

#if ENABLE_DEVICEMANAGER_DEBUG
    RINFO << "deviceItems size = " << deviceItems.size();
#endif // ENABLE_DEVICEMANAGER_DEBUG
    {
      std::lock_guard<std::mutex> lg(_devices_map_mtx);
      // Step2: 获取不存在的设备
      for (auto iterMap = _devices_map.begin();
           iterMap != _devices_map.end();) {
        auto iterMap2 = deviceItems.find(iterMap->first);
        if (iterMap2 == deviceItems.end()) {
#if ENABLE_DEVICEMANAGER_DEBUG
          RINFO << "detach(A): uuid = " << iterMap->first
                << ", _devices_map size = " << _devices_map.size();
#endif // ENABLE_DEVICEMANAGER_DEBUG
          if (detachDevices.find(iterMap->first) == detachDevices.end()) {
            detachDevices.insert({iterMap->first, iterMap->second});
          }
          iterMap = _devices_map.erase(iterMap);
        } else {
          ++iterMap;
        }
      }

      // Step3: 获取新的设备
      for (auto iterMap = deviceItems.begin(); iterMap != deviceItems.end();
           ++iterMap) {
        auto iterMap2 = _devices_map.find(iterMap->second.uuid);
        if (iterMap2 == _devices_map.end()) {
          _devices_map.insert({iterMap->first, iterMap->second});
          attachDevices.push_back(iterMap->second);
#if ENABLE_DEVICEMANAGER_DEBUG
          RINFO << "attach: uuid = " << iterMap->first
                << ", _devices_map size = " << _devices_map.size();
#endif // ENABLE_DEVICEMANAGER_DEBUG
        }
      }

      // Step4: 再次检查Windows物理断开的设备
      for (auto iterMap = _devices_disconnect_map.begin();
           iterMap != _devices_disconnect_map.end(); ++iterMap) {
        const std::string &uuid = iterMap->first;
        auto iterMap2 = _devices_map.find(uuid);
        if (iterMap2 != _devices_map.end()) {
          _devices_map.erase(iterMap2);
#if ENABLE_DEVICEMANAGER_DEBUG
          RINFO << "detach(B): uuid = " << iterMap->first
                << ", _devices_map size = " << _devices_map.size();
#endif // ENABLE_DEVICEMANAGER_DEBUG
        }
        if (detachDevices.find(iterMap->first) == detachDevices.end()) {
          detachDevices.insert({iterMap->first, iterMap->second});
        }
#if ENABLE_DEVICEMANAGER_DEBUG
        RINFO << "detach(C): uuid = " << iterMap->first
              << ", _devices_map size = " << _devices_map.size();
#endif // ENABLE_DEVICEMANAGER_DEBUG
      }
      _devices_disconnect_map.clear();
    }
#if ENABLE_DEVICEMANAGER_DEBUG
    RINFO << "Update Device Check Status: detachDevices size = "
          << detachDevices.size()
          << ", attachDevices size = " << attachDevices.size()
          << ", _devices_map size = " << _devices_map.size();
#endif // ENABLE_DEVICEMANAGER_DEBUG

    // Step4: 通知设备事件
    if (_event_cb) {
      // Attach 事件
      for (const auto &item : attachDevices) {
        robosense::common::DeviceEvent_t event;
        event.event_type = robosense::common::DEVICE_EVENT_ATTACH;
        event.uuid_size = std::min(item.uuid.size(), sizeof(event.uuid));
        memcpy(event.uuid, item.uuid.c_str(), event.uuid_size);
        event.ac_device_type = item.ac_device_type;
        _event_cb(event);
      }
      // Detach 事件
      for (const auto &item : detachDevices) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        robosense::common::DeviceEvent_t event;
        event.event_type = robosense::common::DEVICE_EVENT_DETACH;
        event.uuid_size = std::min(item.second.uuid.size(), sizeof(event.uuid));
        memcpy(event.uuid, item.second.uuid.c_str(), event.uuid_size);
        event.ac_device_type = item.second.ac_device_type;
        _event_cb(event);
      }
    }

    attachDevices.clear();
    if (!detachDevices.empty()) {
      std::lock_guard<std::mutex> lg(_is_stoping_mtx_);
      if (!_is_stoping_.load()) {
        _is_stoping_ = true;
        detachDevices.clear();
        _is_stoping_ = false;
      } else {
        detachDevices.clear();
      }
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
  }
}

void DeviceManager::processPointCloudQueue() {
  while (!_stopProcessingThreads) {
    std::pair<std::shared_ptr<PointCloudT<RsPointXYZIRT>>, std::string> msgPair;
    {
      std::unique_lock<std::mutex> lock(_pointCloudQueueMutex);
      _pointCloudQueueCond.wait(lock, [this] {
        return !_pointCloudQueue.empty() || _stopProcessingThreads;
      });
      if (_stopProcessingThreads) {
        break;
      }
      msgPair = _pointCloudQueue.front();
      _pointCloudQueue.pop();
    }
    if (msgPair.first && _pc_cb) {
      _pc_cb(msgPair.first, msgPair.second);
    }
  }
}

void DeviceManager::processImageQueue() {
  while (!_stopProcessingThreads) {
    std::pair<std::shared_ptr<robosense::lidar::ImageData>, std::string>
        msgPair;
    {
      std::unique_lock<std::mutex> lock(_imageQueueMutex);
      _imageQueueCond.wait(lock, [this] {
        return !_imageQueue.empty() || _stopProcessingThreads;
      });
      if (_stopProcessingThreads) {
        break;
      }
      msgPair = _imageQueue.front();
      _imageQueue.pop();
    }
    if (msgPair.first && _image_cb) {
      _image_cb(msgPair.first, msgPair.second);
    }
  }
}

void DeviceManager::processImuQueue() {
  while (!_stopProcessingThreads) {
    std::pair<std::shared_ptr<robosense::lidar::ImuData>, std::string> msgPair;
    {
      std::unique_lock<std::mutex> lock(_imuQueueMutex);
      _imuQueueCond.wait(lock, [this] {
        return !_imuQueue.empty() || _stopProcessingThreads;
      });
      if (_stopProcessingThreads) {
        break;
      }
      msgPair = _imuQueue.front();
      _imuQueue.pop();
    }
    if (msgPair.first && _imu_cb) {
      _imu_cb(msgPair.first, msgPair.second);
    }
  }
}

// ============================================================//
// md5
// ============================================================//
void md5::MD5Encode(uint8_t *output, uint32_t *input, uint32_t len) {
  unsigned int i = 0, j = 0;
  while (j < len) {
    output[j] = input[i] & 0xFF;
    output[j + 1] = (input[i] >> 8) & 0xFF;
    output[j + 2] = (input[i] >> 16) & 0xFF;
    output[j + 3] = (input[i] >> 24) & 0xFF;
    i++;
    j += 4;
  }
}

uint8_t md5::PADDING[] = {0x80, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                          0,    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                          0,    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                          0,    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};

void md5::MD5Decode(uint32_t *output, uint8_t *input, uint32_t len) {
  uint32_t i = 0, j = 0;
  while (j < len) {
    output[i] = (input[j]) | (input[j + 1] << 8) | (input[j + 2] << 16) |
                (input[j + 3] << 24);
    i++;
    j += 4;
  }
}

void md5::MD5Transform(uint32_t state[4], uint8_t block[64]) {
  uint32_t a = state[0];
  uint32_t b = state[1];
  uint32_t c = state[2];
  uint32_t d = state[3];
  uint32_t x[64];

  MD5Decode(x, block, 64);

  MD5_FF(a, b, c, d, x[0], 7, 0xd76aa478);
  MD5_FF(d, a, b, c, x[1], 12, 0xe8c7b756);
  MD5_FF(c, d, a, b, x[2], 17, 0x242070db);
  MD5_FF(b, c, d, a, x[3], 22, 0xc1bdceee);
  MD5_FF(a, b, c, d, x[4], 7, 0xf57c0faf);
  MD5_FF(d, a, b, c, x[5], 12, 0x4787c62a);
  MD5_FF(c, d, a, b, x[6], 17, 0xa8304613);
  MD5_FF(b, c, d, a, x[7], 22, 0xfd469501);
  MD5_FF(a, b, c, d, x[8], 7, 0x698098d8);
  MD5_FF(d, a, b, c, x[9], 12, 0x8b44f7af);
  MD5_FF(c, d, a, b, x[10], 17, 0xffff5bb1);
  MD5_FF(b, c, d, a, x[11], 22, 0x895cd7be);
  MD5_FF(a, b, c, d, x[12], 7, 0x6b901122);
  MD5_FF(d, a, b, c, x[13], 12, 0xfd987193);
  MD5_FF(c, d, a, b, x[14], 17, 0xa679438e);
  MD5_FF(b, c, d, a, x[15], 22, 0x49b40821);

  MD5_GG(a, b, c, d, x[1], 5, 0xf61e2562);
  MD5_GG(d, a, b, c, x[6], 9, 0xc040b340);
  MD5_GG(c, d, a, b, x[11], 14, 0x265e5a51);
  MD5_GG(b, c, d, a, x[0], 20, 0xe9b6c7aa);
  MD5_GG(a, b, c, d, x[5], 5, 0xd62f105d);
  MD5_GG(d, a, b, c, x[10], 9, 0x2441453);
  MD5_GG(c, d, a, b, x[15], 14, 0xd8a1e681);
  MD5_GG(b, c, d, a, x[4], 20, 0xe7d3fbc8);
  MD5_GG(a, b, c, d, x[9], 5, 0x21e1cde6);
  MD5_GG(d, a, b, c, x[14], 9, 0xc33707d6);
  MD5_GG(c, d, a, b, x[3], 14, 0xf4d50d87);
  MD5_GG(b, c, d, a, x[8], 20, 0x455a14ed);
  MD5_GG(a, b, c, d, x[13], 5, 0xa9e3e905);
  MD5_GG(d, a, b, c, x[2], 9, 0xfcefa3f8);
  MD5_GG(c, d, a, b, x[7], 14, 0x676f02d9);
  MD5_GG(b, c, d, a, x[12], 20, 0x8d2a4c8a);

  MD5_HH(a, b, c, d, x[5], 4, 0xfffa3942);
  MD5_HH(d, a, b, c, x[8], 11, 0x8771f681);
  MD5_HH(c, d, a, b, x[11], 16, 0x6d9d6122);
  MD5_HH(b, c, d, a, x[14], 23, 0xfde5380c);
  MD5_HH(a, b, c, d, x[1], 4, 0xa4beea44);
  MD5_HH(d, a, b, c, x[4], 11, 0x4bdecfa9);
  MD5_HH(c, d, a, b, x[7], 16, 0xf6bb4b60);
  MD5_HH(b, c, d, a, x[10], 23, 0xbebfbc70);
  MD5_HH(a, b, c, d, x[13], 4, 0x289b7ec6);
  MD5_HH(d, a, b, c, x[0], 11, 0xeaa127fa);
  MD5_HH(c, d, a, b, x[3], 16, 0xd4ef3085);
  MD5_HH(b, c, d, a, x[6], 23, 0x4881d05);
  MD5_HH(a, b, c, d, x[9], 4, 0xd9d4d039);
  MD5_HH(d, a, b, c, x[12], 11, 0xe6db99e5);
  MD5_HH(c, d, a, b, x[15], 16, 0x1fa27cf8);
  MD5_HH(b, c, d, a, x[2], 23, 0xc4ac5665);

  MD5_II(a, b, c, d, x[0], 6, 0xf4292244);
  MD5_II(d, a, b, c, x[7], 10, 0x432aff97);
  MD5_II(c, d, a, b, x[14], 15, 0xab9423a7);
  MD5_II(b, c, d, a, x[5], 21, 0xfc93a039);
  MD5_II(a, b, c, d, x[12], 6, 0x655b59c3);
  MD5_II(d, a, b, c, x[3], 10, 0x8f0ccc92);
  MD5_II(c, d, a, b, x[10], 15, 0xffeff47d);
  MD5_II(b, c, d, a, x[1], 21, 0x85845dd1);
  MD5_II(a, b, c, d, x[8], 6, 0x6fa87e4f);
  MD5_II(d, a, b, c, x[15], 10, 0xfe2ce6e0);
  MD5_II(c, d, a, b, x[6], 15, 0xa3014314);
  MD5_II(b, c, d, a, x[13], 21, 0x4e0811a1);
  MD5_II(a, b, c, d, x[4], 6, 0xf7537e82);
  MD5_II(d, a, b, c, x[11], 10, 0xbd3af235);
  MD5_II(c, d, a, b, x[2], 15, 0x2ad7d2bb);
  MD5_II(b, c, d, a, x[9], 21, 0xeb86d391);
  state[0] += a;
  state[1] += b;
  state[2] += c;
  state[3] += d;
}

void md5::MD5Init(MD5_CTX *context) {
  context->count[0] = 0;
  context->count[1] = 0;
  context->state[0] = 0x67452301;
  context->state[1] = 0xEFCDAB89;
  context->state[2] = 0x98BADCFE;
  context->state[3] = 0x10325476;
}
void md5::MD5Update(MD5_CTX *context, uint8_t *input, uint32_t inputlen) {
  uint32_t i = 0, index = 0, partlen = 0;
  index = (context->count[0] >> 3) & 0x3F;
  partlen = 64 - index;
  context->count[0] += inputlen << 3;
  if (context->count[0] < (inputlen << 3)) {
    context->count[1]++;
  }
  context->count[1] += inputlen >> 29;

  if (inputlen >= partlen) {
    memcpy(&context->buffer[index], input, partlen);
    MD5Transform(context->state, context->buffer);
    for (i = partlen; i + 64 <= inputlen; i += 64) {
      MD5Transform(context->state, &input[i]);
    }
    index = 0;
  } else {
    i = 0;
  }

  memcpy(&context->buffer[index], &input[i], inputlen - i);
}

void md5::MD5Final(MD5_CTX *context, uint8_t digest[16]) {
  uint32_t index = 0, padlen = 0;
  uint8_t bits[8];
  index = (context->count[0] >> 3) & 0x3F;
  padlen = (index < 56) ? (56 - index) : (120 - index);
  MD5Encode(bits, context->count, 8);
  MD5Update(context, PADDING, padlen);
  MD5Update(context, bits, 8);
  MD5Encode(digest, context->state, 16);
}

std::string md5::calculateFileMd5(const std::string &filename) {
  std::string result;
  bool ret = true;
  std::ifstream file(filename, std::ios::binary);
  if (!file) {
    // AClog::log_e(LOG_TAG, "Failed to open file: %s\n", filename.c_str());
    RERROR << "md5: Failed to open file: " << filename;
    ret = false;
  }

  MD5_CTX md5;
  uint8_t decrypt[16];

  MD5Init(&md5);
  char buffer[1024];

  while (file.read(buffer, sizeof(buffer)) || file.gcount() > 0) {
    MD5Update(&md5, reinterpret_cast<unsigned char *>(buffer), file.gcount());
  }
  MD5Final(&md5, decrypt);

  if (ret) {
    // std::ostringstream oss;
    // for (int i = 0; i < 16; i++)
    // {
    //     oss << std::hex << std::setw(2) << std::setfill('0') <<
    //     static_cast<int>(decrypt[i]);
    // }
    // result = oss.str();

    char res[33];
    char temp[3];
    res[0] = '\0';

    for (int i = 0; i < 16; i++) {
      snprintf(temp, sizeof(temp), "%02x", decrypt[i]);
      strcat(res, temp);
    }
    result = res;
  } else {
    ret = "";
  }

  return result;
}

} // namespace device
} // namespace robosense