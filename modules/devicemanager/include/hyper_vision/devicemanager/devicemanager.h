#ifndef SENSORMANAGER_H
#define SENSORMANAGER_H

#include "hyper_vision/common/common.h"
#include "libusb/libusb.h"
#include "rs_driver/api/lidar_driver.hpp"
#include "rs_driver/msg/pcl_point_cloud_msg.hpp"
#include <atomic>
#include <boost/circular_buffer.hpp>
#include <condition_variable>
#include <filesystem>
#include <fstream>
#include <functional>
#include <iostream>
#include <map>
#include <memory>
#include <mutex>
#include <queue>
#include <set>
#include <string>
#include <thread>
#include <vector>
#define ENABLE_DEVICEMANAGER_DEBUG (0)
#define ENABLE_DEVICE_OP_PAUSE (0)

namespace robosense {
namespace device {

typedef enum hid_req {
  HID_REQ_IMU_UPLOAD_START_100HZ = 0, /**< Request to start the operation. */
  HID_REQ_IMU_UPLOAD_START_200HZ,     /**< Request to start the operation. */
  HID_REQ_IMU_UPLOAD_STOP,            /**< Request to stop the operation. */
  HID_REQ_SYNC,                       /**< Request to synchronize. */
  HID_REQ_DELAY_RESP,                 /**< Request for a delayed response. */
  HID_REQ_RNDIS_MODE,
  HID_REQ_USB_MODE,
  HID_REQ_DOWNLOAD_CANCEL
} hid_req_t;

typedef enum hid_resp {
  HID_RESP_ERROR = 0, /**< Response indicating an error. */
  HID_RESP_HEAD_ERROR,
  HID_RESP_CRC_ERROR,
  HID_RESP_START_STOP, /**< Response for start/stop operation. */
  HID_RESP_SYNC,       /**< Response for synchronization. */
  HID_RESP_DELAY,      /**< Response for a delay request. */
  HID_RESP_IMU,        /**< Response containing IMU data. */
  HID_RESP_DOWNLOAD_START,
  HID_RESP_DOWNLOAD_DATA,
  HID_RESP_DOWNLOAD_STOP,
  HID_RESP_DOWNLOAD_FILE_EXIST,
  HID_RESP_OTHER
} hid_resp_t;

// ============================================================//
// md5
// ============================================================//
class md5 {
public:
  typedef struct {
    uint32_t count[2];
    uint32_t state[4];
    uint8_t buffer[64];
  } MD5_CTX;

  static uint8_t PADDING[];

  md5(){};
  ~md5(){};
  static void MD5Encode(uint8_t *output, uint32_t *input, uint32_t len);
  static void MD5Decode(uint32_t *output, uint8_t *input, uint32_t len);
  static void MD5Transform(uint32_t state[4], uint8_t block[64]);
  static void MD5Init(MD5_CTX *context);
  static void MD5Update(MD5_CTX *context, uint8_t *input, uint32_t inputlen);
  static void MD5Final(MD5_CTX *context, uint8_t digest[16]);

  static std::string calculateFileMd5(const std::string &filename);

#define MD5_F(x, y, z) ((x & y) | (~x & z))
#define MD5_G(x, y, z) ((x & z) | (y & ~z))
#define MD5_H(x, y, z) (x ^ y ^ z)
#define MD5_I(x, y, z) (y ^ (x | ~z))
#define ROTATE_LEFT(x, n) ((x << n) | (x >> (32 - n)))
#define MD5_FF(a, b, c, d, x, s, ac)                                           \
  {                                                                            \
    a += MD5_F(b, c, d) + x + ac;                                              \
    a = ROTATE_LEFT(a, s);                                                     \
    a += b;                                                                    \
  }
#define MD5_GG(a, b, c, d, x, s, ac)                                           \
  {                                                                            \
    a += MD5_G(b, c, d) + x + ac;                                              \
    a = ROTATE_LEFT(a, s);                                                     \
    a += b;                                                                    \
  }
#define MD5_HH(a, b, c, d, x, s, ac)                                           \
  {                                                                            \
    a += MD5_H(b, c, d) + x + ac;                                              \
    a = ROTATE_LEFT(a, s);                                                     \
    a += b;                                                                    \
  }
#define MD5_II(a, b, c, d, x, s, ac)                                           \
  {                                                                            \
    a += MD5_I(b, c, d) + x + ac;                                              \
    a = ROTATE_LEFT(a, s);                                                     \
    a += b;                                                                    \
  }
};

class DeviceFreqMonitor {
public:
  using Ptr = std::shared_ptr<DeviceFreqMonitor>;
  using ConstPtr = std::shared_ptr<DeviceFreqMonitor>;

public:
  DeviceFreqMonitor() = default;
  ~DeviceFreqMonitor() = default;

public:
  void initTimestampBuffers(const int image_input_fps,
                            const int depth_input_fps,
                            const int imu_input_fps) {
    image_timestamp_buffer_ = boost::circular_buffer<double>(image_input_fps);
    imu_timestamp_buffer_ = boost::circular_buffer<double>(imu_input_fps);
    depth_timestamp_buffer_ = boost::circular_buffer<double>(depth_input_fps);
  }

  void resetTimestampBuffers() {

    image_timestamp_buffer_.clear();
    imu_timestamp_buffer_.clear();
    depth_timestamp_buffer_.clear();
  }

  void updateImageTimestamp(const uint64_t timestampNs) {
    std::lock_guard<std::mutex> lg(image_timestamp_buffer_mtx_);
    image_timestamp_buffer_.push_back(timestampNs);
  }

  void updateImuTimestamp(const uint64_t timestampNs) {
    std::lock_guard<std::mutex> lg(imu_timestamp_buffer_mtx_);
    imu_timestamp_buffer_.push_back(timestampNs);
  }

  void updateDepthTimestamp(const uint64_t timestampNs) {
    std::lock_guard<std::mutex> lg(depth_timestamp_buffer_mtx_);
    depth_timestamp_buffer_.push_back(timestampNs);
  }

  void getDeviceFreqInfo(float &image_feq, float &depth_freq, float &imu_freq) {
    image_feq = 0;
    depth_freq = 0;
    imu_freq = 0;

    {
      std::lock_guard<std::mutex> lg(image_timestamp_buffer_mtx_);
      int image_cnt = image_timestamp_buffer_.size();
      if (image_cnt > 2) {
        image_feq =
            (image_cnt - 1) * 1e9 /
            (image_timestamp_buffer_.back() - image_timestamp_buffer_.front());
      }
    }

    {
      std::lock_guard<std::mutex> lg(imu_timestamp_buffer_mtx_);
      int imu_cnt = imu_timestamp_buffer_.size();
      if (imu_cnt > 2) {
        imu_freq =
            (imu_cnt - 1) * 1e9 /
            (imu_timestamp_buffer_.back() - imu_timestamp_buffer_.front());
      }
    }

    {
      std::lock_guard<std::mutex> lg(depth_timestamp_buffer_mtx_);
      int depth_cnt = depth_timestamp_buffer_.size();
      if (depth_cnt > 2) {
        depth_freq =
            (depth_cnt - 1) * 1e9 /
            (depth_timestamp_buffer_.back() - depth_timestamp_buffer_.front());
      }
    }
  }

private:
  std::mutex image_timestamp_buffer_mtx_;
  boost::circular_buffer<double> image_timestamp_buffer_;
  std::mutex imu_timestamp_buffer_mtx_;
  boost::circular_buffer<double> imu_timestamp_buffer_;
  std::mutex depth_timestamp_buffer_mtx_;
  boost::circular_buffer<double> depth_timestamp_buffer_;
};

class DeviceManager : public std::enable_shared_from_this<DeviceManager> {
public:
  using Ptr = std::shared_ptr<DeviceManager>;
  using ConstPtr = std::shared_ptr<const DeviceManager>;

public:
  using RS_DEVICE_EVENT_CALLBACK =
      std::function<void(const robosense::common::DeviceEvent &deviceEvent)>;

  using RS_DEVICE_POINTCLOUD_CALLBACK =
      std::function<void(const std::shared_ptr<PointCloudT<RsPointXYZIRT>> &,
                         const std::string &)>;

  using RS_DEVICE_IMAGE_CALLBACK =
      std::function<void(const std::shared_ptr<robosense::lidar::ImageData> &,
                         const std::string &)>;

  using RS_DEVICE_IMU_CALLBACK = std::function<void(
      const std::shared_ptr<robosense::lidar::ImuData> &, const std::string &)>;

private:
  using RS_DEVICE_DRIVER =
      robosense::lidar::LidarDriver<PointCloudT<RsPointXYZIRT>>;
  using RS_DEVICE_DRIVER_PTR = std::shared_ptr<RS_DEVICE_DRIVER>;

public:
  class DeviceInfoItem {
  public:
    using Ptr = std::shared_ptr<DeviceInfoItem>;
    using ConstPtr = std::shared_ptr<const DeviceInfoItem>;

  public:
    DeviceInfoItem() {
      uuid.clear();
      is_attach = false;
      is_usb_mode = false;
      i_sn = 0;
      driver_ptr = nullptr;
      device_info.clear();
      device_calib_info.clear();
      is_pause = true;
      is_device_info_need_update = false;
      is_device_calib_info_need_update = false;
      ac_device_type = common::RS_AC_DEVICE_TYPE::RS_AC_DEVICE_UNKNOWN;
    }

    ~DeviceInfoItem() = default;

  public:
    std::string uuid;
    bool is_attach;
    bool is_usb_mode;
    uint8_t i_sn;
    RS_DEVICE_DRIVER_PTR driver_ptr;
    std::string device_info;
    std::string device_calib_info;
    bool is_pause;
    bool is_device_info_need_update;
    bool is_device_calib_info_need_update;
    common::RS_AC_DEVICE_TYPE ac_device_type;
  };

public:
  DeviceManager();

  ~DeviceManager();

  bool init(int input_image_format = 1, int input_image_fps = 30,
            int input_imu_fps = 200, const bool isEnableDebug = false);

  int stop();

  int openDevice(const std::string &device_uuid);

  int closeDevice(const std::string &device_uuid, const bool isMetux = true);

  int pauseDevice(const std::string &device_uuid, const bool isPauseOp);

  int closeDevices();

  int queryDeviceInfo(const std::string &device_uuid, std::string &devInfo);

  int queryDeviceCalibInfo(const std::string &uuid, std::string &calibInfo);

  int writeDeviceCalibInfo(const std::string &uuid,
                           const std::string &clib_json);

  int startOtaDevice(const std::string &device_uuid,
                     const std::string &ota_bin_file_path);

  int cancleOtaDevice(const std::string &device_uuid);

  int finishOtaDevice(const std::string &device_uuid);

  int checkOtaFinish(const std::string &device_uuid, bool &status);

  int checkOtaSuccess(const std::string &device_uuid, bool &status);

  std::set<std::string> getDevices();

  bool getDeviceType(std::string &uuid, bool &is_usb_mode);

  void regDeviceEventCallback(const RS_DEVICE_EVENT_CALLBACK &event_cb);

  void regPointCloudCallback(const RS_DEVICE_POINTCLOUD_CALLBACK &pc_cb);

  void regImageDataCallback(const RS_DEVICE_IMAGE_CALLBACK &image_cb);

  void regImuDataCallback(const RS_DEVICE_IMU_CALLBACK &imu_cb);

  void getDeviceFreqInfo(const std::string &uuid, float &image_freq,
                         float &depth_freq, float &imu_freq);

private:
  int findDevices(std::map<std::string, DeviceInfoItem> &devices_map);
  std::shared_ptr<PointCloudT<RsPointXYZIRT>> localGetPointCloudCallback();
  std::shared_ptr<robosense::lidar::ImageData> localGetImageDataCallback();
  std::shared_ptr<robosense::lidar::ImuData> localGetImuDataCallback();
  void localRunPointCloudCallback(
      const std::shared_ptr<PointCloudT<RsPointXYZIRT>> &msgPtr,
      const std::string &uuid);
  void localRunImageCallback(
      const std::shared_ptr<robosense::lidar::ImageData> &msgPtr,
      const std::string &uuid);
  void
  localRunImuCallback(const std::shared_ptr<robosense::lidar::ImuData> &msgPtr,
                      const std::string &uuid);
  void localRunExceptionCallback(const robosense::lidar::Error &error,
                                 const std::string &uuid);
  void hotplugWorkThread();
  void processPointCloudQueue();
  void processImageQueue();
  void processImuQueue();

  std::string otaBinFileToString(const std::string &filePath, size_t numBytes);
  bool otaProcess(const std::string &upload_transfer_file_path);
  bool otaUpload(const std::string &path);
  bool otaFileTransfer(const std::filesystem::path &path);
  bool otaGetDevType(std::string &uuid);

private:
  std::thread _m_thread;
  std::thread _usb_thread;
  libusb_context *_usb_ctx;
  int _kill_handler_thread;
  bool _start;
  bool _inited;
  RS_DEVICE_EVENT_CALLBACK _event_cb;
  RS_DEVICE_POINTCLOUD_CALLBACK _pc_cb;
  RS_DEVICE_IMAGE_CALLBACK _image_cb;
  RS_DEVICE_IMU_CALLBACK _imu_cb;

  std::mutex _devices_map_mtx;
  std::map<std::string, DeviceInfoItem> _devices_map;
  std::map<std::string, DeviceInfoItem> _devices_disconnect_map;

  bool _enable_debug;
  std::mutex _is_stoping_mtx_;
  std::atomic_bool _is_stoping_ = false;

  robosense::lidar::frame_format _input_image_format;
  int _input_image_fps;
  int _input_imu_fps;

private:
  bool _cancel_transfer = false;
  bool _device_ota_finished = false;
  bool _device_ota_success = false;
  std::shared_ptr<std::thread> ota_thread_ptr_;
  std::string _after_ota_device_info;
  std::string _ota_uuid;
  const std::string strOtaCmd = "/customer/ota.sh";
  const std::string strRebootCmd = "/sbin/reboot";

  const std::string sensorBinFileKey = "upgrade_bin_version";
  const std::string lidarBinFileKey = "upgrade_lidar_bin";

  const std::string strCalibReadCmd =
      "{\"owner\":\"external\", \"key\":[\"CamCX\", \"CamCY\", "
      "\"CamDistCoeff1\", \"CamDistCoeff2\", \"CamDistCoeff3\", "
      "\"CamDistCoeff4\", \"CamDistCoeff5\", \"CamDistCoeff6\", "
      "\"CamDistCoeff7\", \"CamDistCoeff8\", \"CamFX\", \"CamFY\", "
      "\"CamToLidarQW\", \"CamToLidarQX\", \"CamToLidarQY\", \"CamToLidarQZ\", "
      "\"CamToLidarTX\", \"CamToLidarTY\", \"CamToLidarTZ\", \"IMUToLidarQW\", "
      "\"IMUToLidarQX\", \"IMUToLidarQY\", \"IMUToLidarQZ\", \"IMUToLidarTX\", "
      "\"IMUToLidarTY\", \"IMUToLidarTZ\"]}";

  const uint32_t RS_UPGRADE_SYS_TIMEOUT_TH_MS = 120000;
  const uint32_t RS_UPGRADE_APP_TIMEOUT_TH_MS = 20000;
  const uint32_t RS_UPGRADE_SLEEP_TH_MS = 5000;

private:
  const uint32_t VENDOR_ID = 0x3840;
  const uint32_t PRODUCT_ID = 0x1010;
  const uint32_t VENDOR_ID_RNDIS = 0x0525;
  const uint32_t PRODUCT_ID_RNDIS = 0xa4a2;

private:
  // 读取帧率监控
  std::map<std::string, DeviceFreqMonitor::Ptr> _device_freq_monitor_mapper;

private:
  // 消息处理队列
  std::queue<
      std::pair<std::shared_ptr<PointCloudT<RsPointXYZIRT>>, std::string>>
      _pointCloudQueue;
  std::queue<
      std::pair<std::shared_ptr<robosense::lidar::ImageData>, std::string>>
      _imageQueue;
  std::queue<std::pair<std::shared_ptr<robosense::lidar::ImuData>, std::string>>
      _imuQueue;

  // 队列互斥锁
  std::mutex _pointCloudQueueMutex;
  std::mutex _imageQueueMutex;
  std::mutex _imuQueueMutex;

  // 条件变量
  std::condition_variable _pointCloudQueueCond;
  std::condition_variable _imageQueueCond;
  std::condition_variable _imuQueueCond;

  // 线程停止标志
  std::atomic_bool _stopProcessingThreads;

  // 消息处理线程
  std::thread _pointCloudProcessingThread;
  std::thread _imageProcessingThread;
  std::thread _imuProcessingThread;
};

} // namespace device
} // namespace robosense

#endif // SENSORMANAGER_H