#========================
# libs
#========================

set(CMAKE_POSITION_INDEPENDENT_CODE ON)

set(CUR_SRCS "")
set(CUR_CU_SRCS)
set(CUR_INCLUDES "include")

set(CUR_SUB_DIR "")
LIST(APPEND CUR_SUB_DIR include)
LIST(APPEND CUR_SUB_DIR src)

foreach (dir ${CUR_SUB_DIR})
file(GLOB_RECURSE tmp_srcs ${dir}/*.cpp ${dir}/*.h)
list(APPEND CUR_SRCS ${tmp_srcs})
endforeach ()

add_library(${CUR_LIB} STATIC ${CUR_SRCS})

target_include_directories(${CUR_LIB}
        PUBLIC
        ${CUR_INCLUDES}
        ${YAML_CPP_LIBRARY_DIR}
        )

target_link_libraries(${CUR_LIB}
        PUBLIC
        common
        codec 
        device
        nlohmann_json::nlo<PERSON>_json
        ${YAML_CPP_LIBRARIES}
        )
target_compile_definitions(${CUR_LIB} PRIVATE MODULE_NAME="driver")
