/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
/***
 * Author: hsw
 * Date: 2024-12-24
 *
 */

#ifndef HYPER_VISION_DRIVER_SUPER_SENSOR_H_
#define HYPER_VISION_DRIVER_SUPER_SENSOR_H_

#include <yaml-cpp/yaml.h>

#include "hyper_vision/common/common.h"
#ifdef _WIN32
#pragma push_marco("ACTYPE_MACRO")
#ifdef ERROR_BUSY
#undef ERROR_BUSY
#endif // ERROR_BUSY
#ifdef ERROR_TIMEOUT
#undef ERROR_TIMEOUT
#endif // ERROR_TIMEOUT
#ifdef ERROR_NOT_SUPPORTED
#undef ERROR_NOT_SUPPORTED
#endif // ERROR_NOT_SUPPORTED
#endif // _WIN32
#include "hyper_vision/codec/colorcodec.h"
#include "hyper_vision/codec/jpegcoder.h"
#include "hyper_vision/devicemanager/devicemanager.h"


// 是否开启NV12_TO_RGB 调试
#define ENABLE_NV12_TO_RGB_DEBUG (0)
#define ENABLE_SINGLE_DEVICE_MODE (0)

namespace robosense {
namespace driver {

class SuperSensor {

private:
  enum RS_IMAGE_OUTPUT_TYPE {
    RS_IMAGE_OUTPUT_NV12 = 1,
    RS_IMAGE_OUTPUT_RGB8 = 2,
  };

  enum RS_DEVICE_DATA_TYPE {
    RS_DEVICE_DATA_POINTCLOUD = 1,
    RS_DEVICE_DATA_IMAGE = 2,
    RS_DEVICE_DATA_IMU = 3,
  };

public:
  using Ptr = std::shared_ptr<SuperSensor>;

  SuperSensor() = default;

  ~SuperSensor() { Stop(); }

  int Init(const YAML::Node &cfg_node);

  int Start();

  int Stop();

  // 获取Imu数据的回调函数
  void SetCallback(
      const std::function<
          void(const std::shared_ptr<robosense::common::MotionFrame> &msg_ptr)>
          &callback);

  // 获取点云的回调函数
  void SetCallback(
      const std::function<
          void(const std::shared_ptr<robosense::common::DepthFrame> &msg_ptr)>
          &callback);

  // 获取AC1图像的回调函数
  void SetCallbackImage(
      const std::function<
          void(const std::shared_ptr<robosense::common::ImageFrame> &msg_ptr)>
          &callback);

  // 获取AC2左图像的回调函数
  void SetCallbackImageLeft(
      const std::function<
          void(const std::shared_ptr<robosense::common::ImageFrame> &msg_ptr)>
          &callback);

  // 获取AC2右图像的回调函数
  void SetCallbackImageRight(
      const std::function<
          void(const std::shared_ptr<robosense::common::ImageFrame> &msg_ptr)>
          &callback);

  // 获取设备: attach/dettach事件
  void SetCallback(
      const std::function<void(const robosense::common::DeviceEvent_t &)>
          callback);

  // 操作设备: 打开/关闭
  int OperatorDevice(
      const robosense::common::DeviceOperator_t &device_operator);

  // 设备进行OTA
  // ota_result_value:
  // 只对DEVICE_OTA_OPERATOR_CHECK_FINISH/DEVICE_OTA_OPERATOR_CHECK_SUCCESS有效
  int OperatorDeviceOta(
      const robosense::common::DeviceOtaOperator_t &device_ota_operator,
      bool &ota_result_value);

  // 查询设备信息
  int QueryDeviceInfo(const std::string &device_uuid, std::string &device_info);

  // 获取当前识别到的所有设备
  std::set<std::string> GetDeviceUuids() {
    if (device_manager_ptr_) {
      return device_manager_ptr_->getDevices();
    } else {
      return std::set<std::string>{};
    }
  }

  // 获取AC1的帧率信息
  int GetACDataFrequenceInfo(const std::string &uuid, float &device_image_freq,
                             float &device_depth_freq, float &device_imu_freq);

  // 保存AC数据到文件
  int SaveACDataToFile(const std::string &save_dir_path,
                       const uint32_t save_operator_type);

  // 从硬件导出标定信息到指定文件
  int ExportCalibFileFromHardware(const std::string &uuid,
                                  const std::string &export_calib_dir_path);

  // 写标定信息到硬件
  int WriteCalibFileToHardware(const std::string &uuid,
                               const std::string &calib_file_path);

private:
  const std::string name() const { return "SuperSensor"; }

  void Core();

  int OpenDevice(const std::string &uuid);

  int CloseDevice(const std::string &uuid);

  int PauseDevice(const std::string &uuid, const bool isPauseOp);

  int StartDeviceOta(const std::string &uuid,
                     const std::string &ota_bin_file_path);

  int CancleDeviceOta(const std::string &uuid);

  int FinishDeviceOta(const std::string &uuid);

  int CheckDeviceOtaFinish(const std::string &uuid, bool &ota_result_value);

  int CheckDeviceOtaSuccess(const std::string &uuid, bool &ota_result_value);

#if defined(ENABLE_USE_CUDA)
  int InitCuda();
  int ReleaseCuda();
#endif // ENABLE_USE_CUDA

  void device_plug_handle(const robosense::common::DeviceEvent_t &deviceEvent);

  void image_handle(const std::shared_ptr<robosense::lidar::ImageData> &msgPtr,
                    const std::string &uuid);

  void rgb_handle(const std::shared_ptr<robosense::lidar::ImageData> &msgPtr,
                  const std::string &uuid);

  void nv12_handle(const std::shared_ptr<robosense::lidar::ImageData> &msgPtr,
                   const std::string &uuid);

  void depth_handle(const std::shared_ptr<PointCloudT<RsPointXYZIRT>> &msgPtr,
                    const std::string &uuid);

  void imu_handle(const std::shared_ptr<robosense::lidar::ImuData> &msgPtr,
                  const std::string &uuid);

  void to_img_output_type(
      const std::shared_ptr<robosense::lidar::ImageData> &msgPtr,
      std::shared_ptr<robosense::common::ImageFrame> &copyImagePtr);
  void nv12_to_img_output_type(
      const std::shared_ptr<robosense::lidar::ImageData> &msgPtr,
      std::shared_ptr<robosense::common::ImageFrame> &copyImagePtr);
  void rgb24_to_img_output_type(
      const std::shared_ptr<robosense::lidar::ImageData> &msgPtr,
      std::shared_ptr<robosense::common::ImageFrame> &copyImagePtr);
  void bgr24_to_img_output_type(
      const std::shared_ptr<robosense::lidar::ImageData> &msgPtr,
      std::shared_ptr<robosense::common::ImageFrame> &copyImagePtr);
  void yuv422_to_img_output_type(
      const std::shared_ptr<robosense::lidar::ImageData> &msgPtr,
      std::shared_ptr<robosense::common::ImageFrame> &copyImagePtr);

  int saveDepthDataToFile(
      const std::string &save_dir_path,
      const std::shared_ptr<robosense::common::DepthFrame> &depthFramePtr);

  int saveImageDataToFile(
      const std::string &save_dir_path,
      const std::shared_ptr<robosense::common::ImageFrame> &imageFramePtr);

  int saveImuDataToFile(
      const std::string &save_dir_path,
      const std::shared_ptr<robosense::common::MotionFrame> &imuFramePtr);

  std::string currentTimeString() {
    const std::string time_format = "%Y-%m-%d-%H-%M-%S";
    auto time_point = std::chrono::system_clock::now();
    std::time_t time_c = std::chrono::system_clock::to_time_t(time_point);
    std::ostringstream time_stream;
    time_stream << std::put_time(std::localtime(&time_c), time_format.c_str());
    return time_stream.str();
  }

  template <typename T>
  std::string toString(const T value, const int percision = 13) {
    std::ostringstream str;
    str << std::fixed << std::setprecision(percision) << value;

    return str.str();
  }

  template <typename T> T fromString(const std::string &strValue) {
    std::istringstream str(strValue);
    T value;
    str >> value;
    return value;
  }

private:
  std::mutex mx_cb_;
  std::function<void(
      const std::shared_ptr<robosense::common::MotionFrame> &msg_ptr)>
      motion_frame_cb_2_;
  std::function<void(
      const std::shared_ptr<robosense::common::DepthFrame> &msg_ptr)>
      depth_frame_cb_2_;
  std::function<void(
      const std::shared_ptr<robosense::common::ImageFrame> &msg_ptr)>
      image_frame_cb_2_;
  std::function<void(
      const std::shared_ptr<robosense::common::ImageFrame> &msg_ptr)>
      left_image_frame_cb_2_;
  std::function<void(
      const std::shared_ptr<robosense::common::ImageFrame> &msg_ptr)>
      right_image_frame_cb_2_;

  std::function<void(const robosense::common::DeviceEvent_t &)>
      device_event_cb_;

  std::unique_ptr<std::thread> thread_ptr_ = nullptr;
  bool run_flag_ = false;

private:
  robosense::device::DeviceManager::Ptr device_manager_ptr_;

  std::vector<uint8_t> rgb_buf_;
  robosense::lidar::frame_format image_input_type_;
  int image_input_fps_;
  int imu_input_fps_;
  RS_IMAGE_OUTPUT_TYPE image_output_type_;

#if ENABLE_SINGLE_DEVICE_MODE
  std::mutex device_uuid_mtx_;
  std::string current_device_uuid_;
#endif // ENABLE_SINGLE_DEVICE_MODE

  std::mutex stat_mapper_mtx_;
  bool output_stat_info_ = false;
  uint32_t output_cycle_th_s_ = 1;
  std::map<RS_DEVICE_DATA_TYPE, uint64_t> stat_mapper_;
#if defined(ENABLE_USE_CUDA)
  unsigned char *pRgbBuf_ = nullptr;
  unsigned char *pYuvBuf_ = nullptr;
  unsigned char *pOutBuf_ = nullptr;
#endif // ENABLE_USE_CUDA
private:
  uint32_t image_seq_ = 0;
  uint32_t pc_seq_ = 0;
  uint32_t imu_seq_ = 0;

private:
  const uint32_t image_width_ = 1920;
  const uint32_t image_height_ = 1080;
  const uint32_t image_rgb_size_ = image_width_ * image_height_ * 3;
  const uint32_t image_nv12_size_ = image_width_ * image_height_ * 3 / 2;

private:
  std::mutex image_last_msg_mtx_;
  std::shared_ptr<robosense::common::ImageFrame> image_last_frame_;
  std::mutex depth_last_msg_mtx_;
  std::shared_ptr<robosense::common::DepthFrame> depth_last_frame_;
  std::mutex imu_last_msg_mtx_;
  std::shared_ptr<robosense::common::MotionFrame> motion_last_frame_;

  robosense::jpeg::JpegCoder::Ptr jpeg_coder_ptr_;
  robosense::jpeg::JpegCodesConfig jpeg_coder_config_;
  std::vector<unsigned char> jpeg_buffer_;
};

} // namespace driver
} // namespace robosense

#ifdef _WIN32
#pragma pop_macro("ACTYPE_MACRO")
#endif // _WIN32

#endif // HYPER_VISION_DRIVER_SUPER_SENSOR_H_
