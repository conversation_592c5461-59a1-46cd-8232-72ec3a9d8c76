/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef HYPER_VISION_DRIVER_SUPER_SENSOR_DUMMY_H_
#define HYPER_VISION_DRIVER_SUPER_SENSOR_DUMMY_H_

#include <mutex>
#include <thread>

#include <yaml-cpp/yaml.h>

#include "hyper_vision/common/common.h"
#ifdef _WIN32
#pragma push_marco("ACTYPE_MACRO")
  #ifdef ERROR_BUSY 
    #undef ERROR_BUSY 
  #endif // ERROR_BUSY
  #ifdef ERROR_TIMEOUT 
    #undef ERROR_TIMEOUT
  #endif // ERROR_TIMEOUT
  #ifdef ERROR_NOT_SUPPORTED
    #undef ERROR_NOT_SUPPORTED 
  #endif // ERROR_NOT_SUPPORTED
#endif // _WIN32

namespace robosense {
namespace driver {

class SuperSensorDummy {
 public:
  using Ptr = std::shared_ptr<SuperSensorDummy>;

  SuperSensorDummy() = default;

  ~SuperSensorDummy() { Stop(); }

  void Init(const YAML::Node &cfg_node);

  void Start();

  void Stop();

  void SetCallback(const std::function<void(const std::shared_ptr<robosense::common::MotionFrame> &msg_ptr)> &callback);

  void SetCallback(const std::function<void(const std::shared_ptr<robosense::common::DepthFrame> &msg_ptr)> &callback);

  void SetCallback(const std::function<void(const std::shared_ptr<robosense::common::ImageFrame> &msg_ptr)> &callback);

 private:
  const std::string name() const { return "SuperSensorDummy"; }

  void Core();

 private:
  std::mutex mx_cb_;
  std::function<void(const std::shared_ptr<robosense::common::MotionFrame> &msg_ptr)> motion_frame_cb_;
  std::function<void(const std::shared_ptr<robosense::common::DepthFrame> &msg_ptr)> depth_frame_cb_;
  std::function<void(const std::shared_ptr<robosense::common::ImageFrame> &msg_ptr)> image_frame_cb_;

  std::unique_ptr<std::thread> thread_ptr_ = nullptr;
  bool run_flag_ = false;
};

} // namespace driver
} // namespace robosense

#ifdef _WIN32
#pragma pop_macro("ACTYPE_MACRO")
#endif // _WIN32 

#endif // HYPER_VISION_DRIVER_SUPER_SENSOR_DUMMY_H_
