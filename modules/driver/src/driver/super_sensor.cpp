/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "hyper_vision/codec/jpegcoder.h"
#include "hyper_vision/driver/super_sensor.h"
#include <filesystem>
#include <nlohmann/json.hpp>
#include <pcl/io/pcd_io.h>

#define ENABLE_POINTER_IS_SHARED_PTR (0)

#define TIMESTAMP_NS                                                           \
  (std::chrono::time_point_cast<std::chrono::nanoseconds>(                     \
       std::chrono::system_clock::now())                                       \
       .time_since_epoch()                                                     \
       .count())
#define TIMESTAMP_US                                                           \
  (std::chrono::time_point_cast<std::chrono::microseconds>(                    \
       std::chrono::system_clock::now())                                       \
       .time_since_epoch()                                                     \
       .count())
#define TIMESTAMP_MS                                                           \
  (std::chrono::time_point_cast<std::chrono::milliseconds>(                    \
       std::chrono::system_clock::now())                                       \
       .time_since_epoch()                                                     \
       .count())
#define TIMESTAMP_S                                                            \
  (std::chrono::time_point_cast<std::chrono::seconds>(                         \
       std::chrono::system_clock::now())                                       \
       .time_since_epoch()                                                     \
       .count())

namespace robosense {
namespace driver {

int SuperSensor::Init(const YAML::Node &cfg_node) {
  RINFO << name() << ": get cfg is " << cfg_node;
  image_output_type_ = static_cast<RS_IMAGE_OUTPUT_TYPE>(
      cfg_node["super_sensor"]["image_output_format"].as<int>());
  output_stat_info_ = cfg_node["super_sensor"]["output_stat_info"].as<bool>();
  output_cycle_th_s_ =
      cfg_node["super_sensor"]["output_cycle_th_s"].as<uint32_t>();

  image_input_type_ = robosense::lidar::frame_format::FRAME_FORMAT_NV12;
  int value = static_cast<int>(image_input_type_);
  Utils::yamlRead(cfg_node["super_sensor"], "image_input_format", value);
  if (value >= robosense::lidar::frame_format::FRAME_FORMAT_ANY &&
      value <= robosense::lidar::frame_format::FRAME_FORMAT_YUV422) {
    image_input_type_ = static_cast<robosense::lidar::frame_format>(value);
  }

  image_input_fps_ = 30;
  int image_input_fps;
  Utils::yamlRead(cfg_node["super_sensor"], "image_input_fps", image_input_fps);
  if (image_input_fps != 30 && image_input_fps != 15 && image_input_fps != 10) {
    uint32_t diff_30 = std::abs(image_input_fps - 30);
    uint32_t diff_15 = std::abs(image_input_fps - 15);
    uint32_t diff_10 = std::abs(image_input_fps - 10);

    uint32_t min_diff = diff_30;
    int new_image_input_fps = 30;
    if (diff_15 < min_diff) {
      new_image_input_fps = 15;
      min_diff = diff_15;
    }
    if (diff_10 < min_diff) {
      new_image_input_fps = 10;
      min_diff = diff_10;
    }
    RWARN << name()
          << ": image_input_fps must 30/15/10: setting image_input_fps = "
          << image_input_fps
          << " => fix new_image_input_fps = " << new_image_input_fps;
    image_input_fps = new_image_input_fps;
  }
  image_input_fps_ = image_input_fps;

  imu_input_fps_ = 200;
  int imu_input_fps;
  Utils::yamlRead(cfg_node["super_sensor"], "imu_input_fps", imu_input_fps);
  if (!(imu_input_fps == 100 || imu_input_fps == 200)) {
    int new_imu_input_fps =
        (std::abs(imu_input_fps - 100) < std::abs(imu_input_fps - 200) ? 100
                                                                       : 200);
    RWARN << name()
          << ": imu_input_fps must 100/200Hz: setting imu_input_fps = "
          << imu_input_fps
          << " => fix new_imu_input_fps = " << new_imu_input_fps;
    imu_input_fps = new_imu_input_fps;
  }
  imu_input_fps_ = imu_input_fps;

  RINFO << name() << ": image_width_ = " << image_width_
        << ", image_height_ = " << image_height_
        << ", image_rgb_size_ = " << image_rgb_size_
        << ", image_nv12_size_ = " << image_nv12_size_
        << ", image_input_type_ = " << static_cast<int>(image_input_type_)
        << ", image_ouput_type_ = " << static_cast<int>(image_output_type_)
        << ", image_input_fps_ = " << image_input_fps_
        << ", imu_input_fps_ = " << imu_input_fps_;

  stat_mapper_.insert({RS_DEVICE_DATA_TYPE::RS_DEVICE_DATA_POINTCLOUD, 0});
  stat_mapper_.insert({RS_DEVICE_DATA_TYPE::RS_DEVICE_DATA_IMAGE, 0});
  stat_mapper_.insert({RS_DEVICE_DATA_TYPE::RS_DEVICE_DATA_IMU, 0});

#if defined(ENABLE_USE_CUDA)
  RINFO << name() << ": enable cuda !";
  if (static_cast<int>(image_output_type_) &
      static_cast<int>(RS_IMAGE_OUTPUT_RGB8)) {
    int ret = InitCuda();
    if (ret != 0) {
      RERROR << name() << ": Initial Cuda Failed !";
      return -1;
    }
  }
#else
  RWARN << name() << ": disable cuda !";
#endif // ENABLE_USE_CUDA
  try {
    device_manager_ptr_.reset(new robosense::device::DeviceManager());
  } catch (...) {
    RWARN << name() << ": Malloc DeviceManager Failed !";
    return -2;
  }

  // register callback(s)
  device_manager_ptr_->regDeviceEventCallback(
      std::bind(&SuperSensor::device_plug_handle, this, std::placeholders::_1));
  device_manager_ptr_->regImageDataCallback(
      std::bind(&SuperSensor::image_handle, this, std::placeholders::_1,
                std::placeholders::_2));
  device_manager_ptr_->regImuDataCallback(std::bind(&SuperSensor::imu_handle,
                                                    this, std::placeholders::_1,
                                                    std::placeholders::_2));
  device_manager_ptr_->regPointCloudCallback(
      std::bind(&SuperSensor::depth_handle, this, std::placeholders::_1,
                std::placeholders::_2));

  bool isSuccess = device_manager_ptr_->init(image_input_type_,
                                             image_input_fps_, imu_input_fps_);
  if (!isSuccess) {
    RERROR << name() << ": Inital DeviceManager Failed: ret = " << isSuccess;
    return -3;
  }

  RDEBUG << name() << ": init succeed!";
  return 0;
}

int SuperSensor::Start() {
  if (output_stat_info_) {
    run_flag_ = true;
    if (thread_ptr_ == nullptr) {
      thread_ptr_ = std::make_unique<std::thread>(&SuperSensor::Core, this);
    }
  }
  return 0;
}

int SuperSensor::Stop() {
  // 关闭设备
  if (device_manager_ptr_ != nullptr) {
    int ret = device_manager_ptr_->stop();
    if (ret != 0) {
      RERROR << name() << ": DeviceManager Close All Devices: ret = " << ret;
    }
  }

  // 退出线程
  if (thread_ptr_ != nullptr) {
    run_flag_ = false;
    if (thread_ptr_->joinable()) {
      thread_ptr_->join();
    }
    thread_ptr_.reset(nullptr);
  }

#if defined(ENABLE_USE_CUDA)
  ReleaseCuda();
#endif // ENABLE_USE_CUDA
  RINFO << name() << ": stoped!";
  return 0;
}

void SuperSensor::SetCallback(
    const std::function<
        void(const std::shared_ptr<robosense::common::MotionFrame> &msg_ptr)>
        &callback) {
  std::lock_guard<std::mutex> lg(mx_cb_);
  motion_frame_cb_2_ = callback;
}

void SuperSensor::SetCallback(
    const std::function<
        void(const std::shared_ptr<robosense::common::DepthFrame> &msg_ptr)>
        &callback) {
  std::lock_guard<std::mutex> lg(mx_cb_);
  depth_frame_cb_2_ = callback;
}

void SuperSensor::SetCallbackImage(
    const std::function<
        void(const std::shared_ptr<robosense::common::ImageFrame> &msg_ptr)>
        &callback) {
  std::lock_guard<std::mutex> lg(mx_cb_);
  image_frame_cb_2_ = callback;
}

void SuperSensor::SetCallbackImageLeft(
    const std::function<
        void(const std::shared_ptr<robosense::common::ImageFrame> &msg_ptr)>
        &callback) {
  std::lock_guard<std::mutex> lg(mx_cb_);
  left_image_frame_cb_2_ = callback;
}

void SuperSensor::SetCallbackImageRight(
    const std::function<
        void(const std::shared_ptr<robosense::common::ImageFrame> &msg_ptr)>
        &callback) {
  std::lock_guard<std::mutex> lg(mx_cb_);
  right_image_frame_cb_2_ = callback;
}

void SuperSensor::SetCallback(
    const std::function<void(const robosense::common::DeviceEvent_t &)>
        callback) {
  std::lock_guard<std::mutex> lg(mx_cb_);
  device_event_cb_ = callback;
}

int SuperSensor::OperatorDevice(
    const robosense::common::DeviceOperator_t &device_operator) {
  int ret = 0;
  std::string uuid;
  switch (device_operator.operation_type) {
  case robosense::common::DEVICE_OPERATOR_CLOSE: {
    uuid = std::string(device_operator.uuid, device_operator.uuid_size);
    ret = CloseDevice(uuid);
    if (ret != 0) {
      RERROR << name() << ": Close Device uuid = " << uuid
             << " Failed: ret = " << ret;
      return -1;
    } else {
      RINFO << name() << ": Close Device uuid = " << uuid << " Successed !";
    }
    break;
  }
  case robosense::common::DEVICE_OPERATOR_OPEN: {
    uuid = std::string(device_operator.uuid, device_operator.uuid_size);
    ret = OpenDevice(uuid);
    if (ret != 0) {
      RERROR << name() << ": Open Device uuid = " << uuid
             << " Failed: ret = " << ret;
      return -2;
    } else {
      RINFO << name() << ": Open Device uuid = " << uuid << " Successed !";
    }
    break;
  }
  case robosense::common::DEVICE_OPERATOR_PAUSE:
  case robosense::common::DEVICE_OPERATOR_PLAY: {
    uuid = std::string(device_operator.uuid, device_operator.uuid_size);
    ret = PauseDevice(uuid, device_operator.operation_type ==
                                robosense::common::DEVICE_OPERATOR_PAUSE);
    if (ret != 0) {
      RERROR << name() << ": Pause Device uuid = " << uuid
             << " Failed: ret = " << ret;
      return -3;
    } else {
      RINFO << name() << ": Pause Device uuid = " << uuid << " Successed !";
    }
    break;
  }
  default: {
    RERROR << name() << ": Not Support Device Operator: "
           << static_cast<int>(device_operator.operation_type);
    return -3;
  }
  }

  return 0;
}

int SuperSensor::GetACDataFrequenceInfo(const std::string &uuid,
                                        float &device_image_freq,
                                        float &device_depth_freq,
                                        float &device_imu_freq) {
  device_image_freq = 0;
  device_depth_freq = 0;
  device_imu_freq = 0;
  if (device_manager_ptr_) {
    device_manager_ptr_->getDeviceFreqInfo(uuid, device_image_freq,
                                           device_depth_freq, device_imu_freq);
  }

  return 0;
}

int SuperSensor::SaveACDataToFile(const std::string &save_dir_path,
                                  const uint32_t save_operator_type) {

  std::filesystem::path base_save_dir_path(save_dir_path);
  if (!std::filesystem::exists(base_save_dir_path)) {
    try {
      std::filesystem::create_directories(base_save_dir_path);
    } catch (...) {
      RERROR << name()
             << ": ACData Save Base Directory Path Not Exist: "
                "base_save_dir_path = "
             << base_save_dir_path.string() << " And Create Failed !";
      return -1;
    }
  }
  const std::string &currentTimestamp = currentTimeString();
  std::filesystem::path save_timestamp_dir_path =
      base_save_dir_path / currentTimestamp;
  if (std::filesystem::exists(save_timestamp_dir_path)) {
    try {
      std::filesystem::remove_all(save_timestamp_dir_path);
    } catch (...) {
      RERROR << name() << ": Remove Exist ACData Save Directory Failed !";
      return -2;
    }
  }
  try {
    std::filesystem::create_directory(save_timestamp_dir_path);
  } catch (...) {
    RERROR << name()
           << ": ACData Save Directory: " << save_timestamp_dir_path.string()
           << " Create Failed !";
    return -3;
  }

  // save image
  if (save_operator_type &
      static_cast<uint32_t>(
          robosense::common::SaveACDataOperatorType::SAVE_AC_DATA_IMAGE)) {
    std::shared_ptr<robosense::common::ImageFrame> save_image_frame_ptr;
    {
      std::lock_guard<std::mutex> lg(image_last_msg_mtx_);
      save_image_frame_ptr = image_last_frame_;
    }

    int ret = saveImageDataToFile(save_timestamp_dir_path.string(),
                                  save_image_frame_ptr);
    if (ret != 0) {
      RERROR << name() << ": Save Image To File: " << save_dir_path
             << " Failed: ret = " << ret;
      return -4;
    }
  }

  // save depth
  if (save_operator_type &
      static_cast<uint32_t>(
          robosense::common::SaveACDataOperatorType::SAVE_AC_DATA_DEPTH)) {
    std::shared_ptr<robosense::common::DepthFrame> save_depth_frame_ptr;
    {
      std::lock_guard<std::mutex> lg(depth_last_msg_mtx_);
      save_depth_frame_ptr = depth_last_frame_;
    }

    int ret = saveDepthDataToFile(save_timestamp_dir_path.string(),
                                  save_depth_frame_ptr);
    if (ret != 0) {
      RERROR << name() << ": Save Depth To File: " << save_dir_path
             << " Failed: ret = " << ret;
      return -5;
    }
  }

  // save imu
  if (save_operator_type &
      static_cast<uint32_t>(
          robosense::common::SaveACDataOperatorType::SAVE_AC_DATA_IMU)) {
    std::shared_ptr<robosense::common::MotionFrame> save_motion_frame_ptr;
    {
      std::lock_guard<std::mutex> lg(imu_last_msg_mtx_);
      save_motion_frame_ptr = motion_last_frame_;
    }

    int ret = saveImuDataToFile(save_timestamp_dir_path.string(),
                                save_motion_frame_ptr);
    if (ret != 0) {
      RERROR << name() << ": Save Imu To File: " << save_dir_path
             << " Failed: ret = " << ret;
      return -6;
    }
  }

  return 0;
}

int SuperSensor::ExportCalibFileFromHardware(
    const std::string &uuid, const std::string &export_calib_dir_path) {
  if (device_manager_ptr_) {

    std::string calib_info;
    int ret = device_manager_ptr_->queryDeviceCalibInfo(uuid, calib_info);
    if (ret != 0) {
      RERROR << name()
             << ": Query Device Calibration Information Failed: ret = " << ret;
      return -1;
    }

    const auto &saveDirPath = std::filesystem::path(export_calib_dir_path);
    if (!std::filesystem::exists(saveDirPath)) {
      try {
        bool isSuccess = std::filesystem::create_directories(saveDirPath);
        if (!isSuccess) {
          RERROR
              << name()
              << ": Create Device Calibration Information Save Directory(A): "
              << saveDirPath.string() << " Failed !";
          return -2;
        } else {
          RINFO << name()
                << ": Create Device Calibration Information Save Directory: "
                << saveDirPath.string() << " Successed !";
        }
      } catch (...) {
        RERROR << name()
               << ": Create Device Calibration Information Save Directory(B): "
               << saveDirPath.string() << " Failed !";
        return -3;
      }
    }

    nlohmann::json calib_json;
    try {
      calib_json = nlohmann::json::parse(calib_info);
    } catch (...) {
      RERROR << name() << ": Parse Json From Calib_info = " << calib_info
             << " Failed !";
      return -4;
    }

    // 全部替换为大写字母
    std::string device_id = uuid;
    std::transform(device_id.begin(), device_id.end(), device_id.begin(),
                   [](unsigned char c) { return std::toupper(c); });

    YAML::Node calib_node;
    // DEVICE_ID
    calib_node["DEVICE_ID"] = device_id;

    // Body
    {
      YAML::Node calib_body_node;
      calib_body_node["base_link"] = "base_link";
      calib_body_node["frame"] = "sensor";

      // 定义的默认值
      YAML::Node default_extrinsic_translate_node;
      YAML::Node default_extrinsic_quaternion_node;
      default_extrinsic_translate_node["x"] = 0.0;
      default_extrinsic_translate_node["y"] = 0.0;
      default_extrinsic_translate_node["z"] = 0.0;

      default_extrinsic_quaternion_node["x"] = 0.0;
      default_extrinsic_quaternion_node["y"] = 0.0;
      default_extrinsic_quaternion_node["z"] = 0.0;
      default_extrinsic_quaternion_node["w"] = 1.0;

      YAML::Node calib_body_extrinsic_node;
      calib_body_extrinsic_node["translation"] =
          default_extrinsic_translate_node;
      calib_body_extrinsic_node["quaternion"] =
          default_extrinsic_quaternion_node;
      calib_body_node["extrinsic"] = calib_body_extrinsic_node;

      calib_node["Body"] = calib_body_node;
    }

    // Sensor
    {
      YAML::Node calib_sensor_node;

      // Lidar
      {
        YAML::Node calib_sensor_lidar_node;
        {
          calib_sensor_lidar_node["base_link"] = "sensor";
          calib_sensor_lidar_node["frame"] = "lidar";

          // 定义的默认值
          YAML::Node default_extrinsic_translate_node;
          YAML::Node default_extrinsic_quaternion_node;
          default_extrinsic_translate_node["x"] = 0.0;
          default_extrinsic_translate_node["y"] = 0.0;
          default_extrinsic_translate_node["z"] = 0.0;

          default_extrinsic_quaternion_node["x"] = 0.0;
          default_extrinsic_quaternion_node["y"] = 0.0;
          default_extrinsic_quaternion_node["z"] = 0.0;
          default_extrinsic_quaternion_node["w"] = 1.0;

          YAML::Node calib_sensor_lidar_extrinsic_node;
          calib_sensor_lidar_extrinsic_node["translation"] =
              default_extrinsic_translate_node;
          calib_sensor_lidar_extrinsic_node["quaternion"] =
              default_extrinsic_quaternion_node;
          calib_sensor_lidar_node["extrinsic"] =
              calib_sensor_lidar_extrinsic_node;
        }
        calib_sensor_node["Lidar"] = calib_sensor_lidar_node;
      }

      // Camera
      {
        YAML::Node calib_sensor_camera_node;
        calib_sensor_camera_node["base_link"] = "sensor";
        calib_sensor_camera_node["frame"] = "camera";

        // 定义的默认值
        YAML::Node default_extrinsic_translate_node;
        YAML::Node default_extrinsic_quaternion_node;
        default_extrinsic_translate_node["x"] = 0.0;
        default_extrinsic_translate_node["y"] = 0.0;
        default_extrinsic_translate_node["z"] = 0.0;

        default_extrinsic_quaternion_node["x"] = 0.0;
        default_extrinsic_quaternion_node["y"] = 0.0;
        default_extrinsic_quaternion_node["z"] = 0.0;
        default_extrinsic_quaternion_node["w"] = 1.0;

        YAML::Node camera_extrinsic_translate_node =
            default_extrinsic_translate_node;
        YAML::Node camera_extrinsic_quaternion_node =
            default_extrinsic_quaternion_node;

        // 3x3
        YAML::Node camera_int_matrix_node(YAML::NodeType::Sequence);
        camera_int_matrix_node.push_back(0.0);
        camera_int_matrix_node.push_back(0.0);
        camera_int_matrix_node.push_back(0.0);
        camera_int_matrix_node.push_back(0.0);
        camera_int_matrix_node.push_back(0.0);
        camera_int_matrix_node.push_back(0.0);
        camera_int_matrix_node.push_back(0.0);
        camera_int_matrix_node.push_back(0.0);
        camera_int_matrix_node.push_back(1.0);

        // 1 * 8
        YAML::Node camera_dist_coeff_node(YAML::NodeType::Sequence);
        camera_dist_coeff_node.push_back(0.0);
        camera_dist_coeff_node.push_back(0.0);
        camera_dist_coeff_node.push_back(0.0);
        camera_dist_coeff_node.push_back(0.0);
        camera_dist_coeff_node.push_back(0.0);
        camera_dist_coeff_node.push_back(0.0);
        camera_dist_coeff_node.push_back(0.0);
        camera_dist_coeff_node.push_back(0.0);

        for (auto iterMap = calib_json.begin(); iterMap != calib_json.end();
             ++iterMap) {
          const std::string &key = iterMap.key();
          const double value =
              fromString<double>(calib_json[key].template get<std::string>());

          if (key == "CamFX") {
            camera_int_matrix_node[0] = value;
          } else if (key == "CamFY") {
            camera_int_matrix_node[4] = value;
          } else if (key == "CamCX") {
            camera_int_matrix_node[2] = value;
          } else if (key == "CamCY") {
            camera_int_matrix_node[5] = value;
          } else if (key == "CamDistCoeff1") {
            camera_dist_coeff_node[0] = value;
          } else if (key == "CamDistCoeff2") {
            camera_dist_coeff_node[1] = value;
          } else if (key == "CamDistCoeff3") {
            camera_dist_coeff_node[2] = value;
          } else if (key == "CamDistCoeff4") {
            camera_dist_coeff_node[3] = value;
          } else if (key == "CamDistCoeff5") {
            camera_dist_coeff_node[4] = value;
          } else if (key == "CamDistCoeff6") {
            camera_dist_coeff_node[5] = value;
          } else if (key == "CamDistCoeff7") {
            camera_dist_coeff_node[6] = value;
          } else if (key == "CamDistCoeff8") {
            camera_dist_coeff_node[7] = value;
          } else if (key == "CamToLidarTX") {
            camera_extrinsic_translate_node["x"] = value;
          } else if (key == "CamToLidarTY") {
            camera_extrinsic_translate_node["y"] = value;
          } else if (key == "CamToLidarTZ") {
            camera_extrinsic_translate_node["z"] = value;
          } else if (key == "CamToLidarQX") {
            camera_extrinsic_quaternion_node["x"] = value;
          } else if (key == "CamToLidarQY") {
            camera_extrinsic_quaternion_node["y"] = value;
          } else if (key == "CamToLidarQZ") {
            camera_extrinsic_quaternion_node["z"] = value;
          } else if (key == "CamToLidarQW") {
            camera_extrinsic_quaternion_node["w"] = value;
          }
        }
        YAML::Node calib_sensor_camera_extrinsic_node;
        calib_sensor_camera_extrinsic_node["translation"] =
            camera_extrinsic_translate_node;
        calib_sensor_camera_extrinsic_node["quaternion"] =
            camera_extrinsic_quaternion_node;

        calib_sensor_camera_node["extrinsic"] =
            calib_sensor_camera_extrinsic_node;

        YAML::Node calib_sensor_camera_intrinsic_node;
        calib_sensor_camera_intrinsic_node["model"] = "Pinhole";
        calib_sensor_camera_intrinsic_node["int_matrix"] =
            camera_int_matrix_node;
        calib_sensor_camera_intrinsic_node["dist_coeff"] =
            camera_dist_coeff_node;

        YAML::Node calib_sensor_camera_image_size_node(
            YAML::NodeType::Sequence);
        calib_sensor_camera_image_size_node.push_back(1920);
        calib_sensor_camera_image_size_node.push_back(1080);

        calib_sensor_camera_intrinsic_node["image_size"] =
            calib_sensor_camera_image_size_node;

        calib_sensor_camera_node["intrinsic"] =
            calib_sensor_camera_intrinsic_node;

        calib_sensor_node["Camera"] = calib_sensor_camera_node;
      }

      // IMU
      {
        YAML::Node calib_sensor_imu_node;

        calib_sensor_imu_node["base_link"] = "sensor";
        calib_sensor_imu_node["frame"] = "imu";

        // 定义的默认值
        YAML::Node default_extrinsic_translate_node;
        YAML::Node default_extrinsic_quaternion_node;
        default_extrinsic_translate_node["x"] = 0.0;
        default_extrinsic_translate_node["y"] = 0.0;
        default_extrinsic_translate_node["z"] = 0.0;

        default_extrinsic_quaternion_node["x"] = 0.0;
        default_extrinsic_quaternion_node["y"] = 0.0;
        default_extrinsic_quaternion_node["z"] = 0.0;
        default_extrinsic_quaternion_node["w"] = 1.0;

        YAML::Node imu_extrinsic_translate_node =
            default_extrinsic_translate_node;
        YAML::Node imu_extrinsic_quaternion_node =
            default_extrinsic_quaternion_node;

        for (auto iterMap = calib_json.begin(); iterMap != calib_json.end();
             ++iterMap) {
          const std::string &key = iterMap.key();
          const double value =
              fromString<double>(calib_json[key].template get<std::string>());

          if (key == "IMUToLidarTX") {
            imu_extrinsic_translate_node["x"] = value;
          } else if (key == "IMUToLidarTY") {
            imu_extrinsic_translate_node["y"] = value;
          } else if (key == "IMUToLidarTZ") {
            imu_extrinsic_translate_node["z"] = value;
          } else if (key == "IMUToLidarQX") {
            imu_extrinsic_quaternion_node["x"] = value;
          } else if (key == "IMUToLidarQY") {
            imu_extrinsic_quaternion_node["y"] = value;
          } else if (key == "IMUToLidarQZ") {
            imu_extrinsic_quaternion_node["z"] = value;
          } else if (key == "IMUToLidarQW") {
            imu_extrinsic_quaternion_node["w"] = value;
          }
        }
        YAML::Node calib_sensor_imu_extrinsic_node;
        calib_sensor_imu_extrinsic_node["translation"] =
            imu_extrinsic_translate_node;
        calib_sensor_imu_extrinsic_node["quaternion"] =
            imu_extrinsic_quaternion_node;
        calib_sensor_imu_node["extrinsic"] = calib_sensor_imu_extrinsic_node;

        calib_sensor_node["IMU"] = calib_sensor_imu_node;
      }

      calib_node["Sensor"] = calib_sensor_node;
    }

    // 构造导出路径
    const uint64_t timestampNs = TIMESTAMP_NS;
    const auto fullSavePath =
        saveDirPath /
        (device_id + "_calibration_" + std::to_string(timestampNs) + ".yaml");
    std::ofstream ofstr(fullSavePath.string(),
                        std::ios_base::out | std::ios_base::binary);
    if (!ofstr.is_open()) {
      RERROR << name() << ": Open File " << fullSavePath.string()
             << " To Write Failed !";
      return -5;
    }

    YAML::Emitter emitter;
    emitter << calib_node;

    ofstr << emitter.c_str() << std::endl;
    ofstr.flush();
    ofstr.close();
  } else {
    RERROR << name() << " Device Manage Is Nullptr !";
    return -6;
  }

  return 0;
}

int SuperSensor::WriteCalibFileToHardware(const std::string &uuid,
                                          const std::string &calib_file_path) {

  if (device_manager_ptr_) {
    YAML::Node calib_node;
    try {
      calib_node = YAML::LoadFile(calib_file_path);
    } catch (...) {
      RERROR << name() << ": Load Calibration File: " << calib_file_path
             << " Failed !";
      return -1;
    }
    RINFO << name() << ": Load Calibration File: " << calib_file_path
          << " Successed !";

    std::string device_id;
    try {
      device_id = calib_node["DEVICE_ID"].as<std::string>();
    } catch (...) {
      RERROR << name() << ": Parse Calibration Node \"DEVICE_ID\" Failed !";
      return -2;
    }
    // device_id: 大写字母改为小写字母
    std::transform(device_id.begin(), device_id.end(), device_id.begin(),
                   [](unsigned char c) { return std::tolower(c); });

    if (device_id != uuid) {
      RERROR << name()
             << ": Write Calibration File Not Match Hardware: device_id = "
             << device_id << ", uuid = " << uuid;
      return -3;
    }

    YAML::Node calib_sensor_camera_int_matrix_node;
    YAML::Node calib_sensor_camera_dist_coeff_matrix_node;
    YAML::Node calib_sensor_camera_extrinsic_translation_node;
    YAML::Node calib_sensor_camera_extrinsic_quaternion_node;
    try {
      calib_sensor_camera_int_matrix_node =
          calib_node["Sensor"]["Camera"]["intrinsic"]["int_matrix"];
      calib_sensor_camera_dist_coeff_matrix_node =
          calib_node["Sensor"]["Camera"]["intrinsic"]["dist_coeff"];
      calib_sensor_camera_extrinsic_translation_node =
          calib_node["Sensor"]["Camera"]["extrinsic"]["translation"];
      calib_sensor_camera_extrinsic_quaternion_node =
          calib_node["Sensor"]["Camera"]["extrinsic"]["quaternion"];
    } catch (...) {
      RERROR << name() << ": Calibration File Parse Camera Node Info Failed !";
      return -4;
    }

    YAML::Node calib_sensor_imu_extrinsic_translation_node;
    YAML::Node calib_sensor_imu_extrinsic_quaternion_node;
    try {
      calib_sensor_imu_extrinsic_translation_node =
          calib_node["Sensor"]["IMU"]["extrinsic"]["translation"];
      calib_sensor_imu_extrinsic_quaternion_node =
          calib_node["Sensor"]["IMU"]["extrinsic"]["quaternion"];
    } catch (...) {
      RERROR << name() << ": Calibration File Parse IMU Node Info Failed !";
      return -5;
    }

    // 所有的值都需要转为字符串的形式: double
    // 精度为小数点后面to_string_percesion位
    nlohmann::json calib_json;
    const int32_t to_string_percesion = 15;
    calib_json["CamCX"] =
        toString<double>(calib_sensor_camera_int_matrix_node[2].as<double>(),
                         to_string_percesion);
    calib_json["CamCY"] =
        toString<double>(calib_sensor_camera_int_matrix_node[5].as<double>(),
                         to_string_percesion);
    calib_json["CamDistCoeff1"] = toString<double>(
        calib_sensor_camera_dist_coeff_matrix_node[0].as<double>(),
        to_string_percesion);
    calib_json["CamDistCoeff2"] = toString<double>(
        calib_sensor_camera_dist_coeff_matrix_node[1].as<double>(),
        to_string_percesion);
    calib_json["CamDistCoeff3"] = toString<double>(
        calib_sensor_camera_dist_coeff_matrix_node[2].as<double>(),
        to_string_percesion);
    calib_json["CamDistCoeff4"] = toString<double>(
        calib_sensor_camera_dist_coeff_matrix_node[3].as<double>(),
        to_string_percesion);
    calib_json["CamDistCoeff5"] = toString<double>(
        calib_sensor_camera_dist_coeff_matrix_node[4].as<double>(),
        to_string_percesion);
    calib_json["CamDistCoeff6"] = toString<double>(
        calib_sensor_camera_dist_coeff_matrix_node[5].as<double>(),
        to_string_percesion);
    calib_json["CamDistCoeff7"] = toString<double>(
        calib_sensor_camera_dist_coeff_matrix_node[6].as<double>(),
        to_string_percesion);
    calib_json["CamDistCoeff8"] = toString<double>(
        calib_sensor_camera_dist_coeff_matrix_node[7].as<double>(),
        to_string_percesion);
    calib_json["CamFX"] =
        toString<double>(calib_sensor_camera_int_matrix_node[0].as<double>(),
                         to_string_percesion);
    calib_json["CamFY"] =
        toString<double>(calib_sensor_camera_int_matrix_node[4].as<double>(),
                         to_string_percesion);
    calib_json["CamToLidarQW"] = toString<double>(
        calib_sensor_camera_extrinsic_quaternion_node["w"].as<double>(),
        to_string_percesion);
    calib_json["CamToLidarQX"] = toString<double>(
        calib_sensor_camera_extrinsic_quaternion_node["x"].as<double>(),
        to_string_percesion);
    calib_json["CamToLidarQY"] = toString<double>(
        calib_sensor_camera_extrinsic_quaternion_node["y"].as<double>(),
        to_string_percesion);
    calib_json["CamToLidarQZ"] = toString<double>(
        calib_sensor_camera_extrinsic_quaternion_node["z"].as<double>(),
        to_string_percesion);
    calib_json["CamToLidarTX"] = toString<double>(
        calib_sensor_camera_extrinsic_translation_node["x"].as<double>(),
        to_string_percesion);
    calib_json["CamToLidarTY"] = toString<double>(
        calib_sensor_camera_extrinsic_translation_node["y"].as<double>(),
        to_string_percesion);
    calib_json["CamToLidarTZ"] = toString<double>(
        calib_sensor_camera_extrinsic_translation_node["z"].as<double>(),
        to_string_percesion);
    calib_json["IMUToLidarQW"] = toString<double>(
        calib_sensor_imu_extrinsic_quaternion_node["w"].as<double>(),
        to_string_percesion);
    calib_json["IMUToLidarQX"] = toString<double>(
        calib_sensor_imu_extrinsic_quaternion_node["x"].as<double>(),
        to_string_percesion);
    calib_json["IMUToLidarQY"] = toString<double>(
        calib_sensor_imu_extrinsic_quaternion_node["y"].as<double>(),
        to_string_percesion);
    calib_json["IMUToLidarQZ"] = toString<double>(
        calib_sensor_imu_extrinsic_quaternion_node["z"].as<double>(),
        to_string_percesion);
    calib_json["IMUToLidarTX"] = toString<double>(
        calib_sensor_imu_extrinsic_translation_node["x"].as<double>(),
        to_string_percesion);
    calib_json["IMUToLidarTY"] = toString<double>(
        calib_sensor_imu_extrinsic_translation_node["y"].as<double>(),
        to_string_percesion);
    calib_json["IMUToLidarTZ"] = toString<double>(
        calib_sensor_imu_extrinsic_translation_node["z"].as<double>(),
        to_string_percesion);

    // 数据序列化
    std::string calib_json_str = calib_json.dump();
    int ret = device_manager_ptr_->writeDeviceCalibInfo(uuid, calib_json_str);
    if (ret != 0) {
      RERROR << name()
             << ": Write Calibration File To Hardware Failed: ret = " << ret
             << ", calib_json = " << calib_json;
      return -6;
    } else {
      RINFO << name()
            << ": Write Calibration File To Hardware Successed: ret = " << ret
            << ", calib_json = " << calib_json;
    }
  } else {
    RERROR << name() << " Device Manage Is Nullptr !";
    return -7;
  }

  return 0;
}

int SuperSensor::OperatorDeviceOta(
    const robosense::common::DeviceOtaOperator_t &device_ota_operator,
    bool &ota_result_value) {
  int ret;
  std::string uuid;
  ota_result_value = false;
  switch (device_ota_operator.operation_type) {
  case robosense::common::DEVICE_OTA_OPERATOR_START: {
    uuid = std::string(device_ota_operator.uuid, device_ota_operator.uuid_size);
    const std::string &ota_bin_file_path =
        std::string(device_ota_operator.ota_file_path,
                    device_ota_operator.ota_file_path_size);
    ret = StartDeviceOta(uuid, ota_bin_file_path);
    if (ret != 0) {
      RERROR << name() << ": Start Device Ota Failed: ret = " << ret
             << ", uuid = " << uuid
             << ", ota_bin_file_path = " << ota_bin_file_path;
      return -1;
    } else {
      RINFO << name() << ": Start Device Ota Successed: uuid = " << uuid
            << ", ota_bin_file_path = " << ota_bin_file_path;
    }
    break;
  }
  case robosense::common::DEVICE_OTA_OPERATOR_CANCLE: {
    uuid = std::string(device_ota_operator.uuid, device_ota_operator.uuid_size);
    ret = CancleDeviceOta(uuid);
    if (ret != 0) {
      RERROR << name() << ": Cancle Device Ota Failed: ret = " << ret
             << ", uuid = " << uuid;
      return -2;
    } else {
      RINFO << name() << ": Cancle Device Ota Sucessed: uuid = " << uuid;
    }
    break;
  }
  case robosense::common::DEVICE_OTA_OPERATOR_FINISH: {
    uuid = std::string(device_ota_operator.uuid, device_ota_operator.uuid_size);
    ret = FinishDeviceOta(uuid);
    if (ret != 0) {
      RERROR << name() << ": Finish Device Ota Failed: ret = " << ret
             << ", uuid = " << uuid;
      return -3;
    } else {
      RINFO << name() << ": Finish Device Ota Successed: uuid = " << uuid;
    }
    break;
  }
  case robosense::common::DEVICE_OTA_OPERATOR_CHECK_FINISH: {
    uuid = std::string(device_ota_operator.uuid, device_ota_operator.uuid_size);
    ret = CheckDeviceOtaFinish(uuid, ota_result_value);
    if (ret != 0) {
      RERROR << name() << ": Check Device Finish Status Failed: ret = " << ret
             << ", uuid = " << uuid;
      return -4;
    } else {
      RINFO << name()
            << ": Check Device Finish Status Successed: uuid = " << uuid
            << ", ota_result_value = " << ota_result_value;
    }
    break;
  }
  case robosense::common::DEVICE_OTA_OPERATOR_CHECK_SUCCESS: {
    uuid = std::string(device_ota_operator.uuid, device_ota_operator.uuid_size);
    ret = CheckDeviceOtaSuccess(uuid, ota_result_value);
    if (ret != 0) {
      RERROR << name() << ": Check Device Success Status Failed: ret = " << ret;
      return -5;
    } else {
      RINFO << name()
            << ": Check Device Success Status Successed: uuid = " << uuid
            << ", ota_result_value = " << ota_result_value;
    }
    break;
  }
  default: {
    RERROR << name() << ": Not Support Device Ota Operator: "
           << static_cast<int>(device_ota_operator.operation_type)
           << ", uuid = " << uuid;
    return -6;
  }
  }

  return 0;
}

int SuperSensor::QueryDeviceInfo(const std::string &device_uuid,
                                 std::string &device_info) {
  device_info.clear();
  if (device_manager_ptr_ != nullptr) {
    int ret = device_manager_ptr_->queryDeviceInfo(device_uuid, device_info);
    if (ret != 0) {
      RERROR << name() << ": Query Device Info Failed: ret = " << ret;
      return -1;
    }
  }

  return 0;
}

void SuperSensor::Core() {
  while (run_flag_) {
    {
      std::lock_guard<std::mutex> lg(stat_mapper_mtx_);
      RINFO << name() << ": Imu count = "
            << stat_mapper_[RS_DEVICE_DATA_TYPE::RS_DEVICE_DATA_IMU];
      RINFO << name() << ": PointCloud count = "
            << stat_mapper_[RS_DEVICE_DATA_TYPE::RS_DEVICE_DATA_POINTCLOUD];
      RINFO << name() << ": Image count = "
            << stat_mapper_[RS_DEVICE_DATA_TYPE::RS_DEVICE_DATA_IMAGE];
    }
    std::this_thread::sleep_for(std::chrono::seconds(output_cycle_th_s_));
  }
}

int SuperSensor::OpenDevice(const std::string &uuid) {
#if ENABLE_SINGLE_DEVICE_MODE
  {
    std::lock_guard<std::mutex> lg(device_uuid_mtx_);
    if (uuid == current_device_uuid_) {
      RINFO << "Open Device uuid = " << uuid << " Already Open !";
      return 0;
    }
  }
#endif // ENABLE_SINGLE_DEVICE_MODE

  if (device_manager_ptr_ != nullptr) {
    int ret = device_manager_ptr_->openDevice(uuid);
    if (ret != 0) {
      RERROR << name() << ": Open Device uuid = " << uuid
             << " Failed: ret = " << ret;
      return -1;
    } else {
      RINFO << name() << ": Open Device uuid = " << uuid << " Successed !";
    }
  }

#if ENABLE_SINGLE_DEVICE_MODE
  {
    std::lock_guard<std::mutex> lg(device_uuid_mtx_);
    current_device_uuid_ = uuid;
  }
#endif // ENABLE_SINGLE_DEVICE_MODE

  return 0;
}

int SuperSensor::CloseDevice(const std::string &uuid) {
#if ENABLE_SINGLE_DEVICE_MODE
  {
    std::lock_guard<std::mutex> lg(device_uuid_mtx_);
    if (uuid != current_device_uuid_ && !current_device_uuid_.empty()) {
      RERROR << name() << ": Close Device uuid = " << uuid
             << ", Current Opened Device uuid = " << current_device_uuid_
             << ", Not Process !";
      return -1;
    }
  }
#endif // ENABLE_SINGLE_DEVICE_MODE

  if (device_manager_ptr_ != nullptr) {
    int ret = device_manager_ptr_->closeDevice(uuid);
    if (ret != 0) {
      RERROR << name() << ": Close Device: " << uuid
             << " Failed: ret = " << ret;
      return -2;
    }
  }

#if ENABLE_SINGLE_DEVICE_MODE
  {
    std::lock_guard<std::mutex> lg(device_uuid_mtx_);
    current_device_uuid_.clear();
  }
#endif // ENABLE_SINGLE_DEVICE_MODE

  return 0;
}

int SuperSensor::PauseDevice(const std::string &uuid, const bool isPauseOp) {
#if ENABLE_SINGLE_DEVICE_MODE
  {
    std::lock_guard<std::mutex> lg(device_uuid_mtx_);
    if (uuid != current_device_uuid_ && !current_device_uuid_.empty()) {
      RERROR << name() << ": Pause/Play Device uuid = " << uuid
             << ", Current Opened Device uuid = " << current_device_uuid_
             << ", Not Process !";
      return -1;
    }
  }
#endif // ENABLE_SINGLE_DEVICE_MODE

  if (device_manager_ptr_ != nullptr) {
    int ret = device_manager_ptr_->pauseDevice(uuid, isPauseOp);
    if (ret != 0) {
      RERROR << name() << ": Pause/Play Device: " << uuid
             << " Failed: isPauseOp = " << isPauseOp << ", ret = " << ret;
      return -2;
    }
  }

  return 0;
}

int SuperSensor::StartDeviceOta(const std::string &uuid,
                                const std::string &ota_bin_file_path) {
  if (device_manager_ptr_ != nullptr) {
    int ret = device_manager_ptr_->startOtaDevice(uuid, ota_bin_file_path);
    if (ret != 0) {
      RERROR << name() << ": Start Device Ota Failed: ret = " << ret
             << ", uuid = " << uuid
             << ", ota_bin_file_path = " << ota_bin_file_path;
      return -1;
    }
  }

  return 0;
}

int SuperSensor::CancleDeviceOta(const std::string &uuid) {
  if (device_manager_ptr_ != nullptr) {
    int ret = device_manager_ptr_->cancleOtaDevice(uuid);
    if (ret != 0) {
      RERROR << name() << ": Cancle Device Ota Failed: ret = " << ret
             << ", uuid = " << uuid;
      return -1;
    }
  }

  return 0;
}

int SuperSensor::FinishDeviceOta(const std::string &uuid) {
  if (device_manager_ptr_ != nullptr) {
    int ret = device_manager_ptr_->finishOtaDevice(uuid);
    if (ret != 0) {
      RERROR << name() << ": Finish Device Ota Failed: ret = " << ret
             << ", uuid = " << uuid;
      return -1;
    }
  }

  return 0;
}

int SuperSensor::CheckDeviceOtaFinish(const std::string &uuid,
                                      bool &ota_result_value) {
  if (device_manager_ptr_ != nullptr) {
    int ret = device_manager_ptr_->checkOtaFinish(uuid, ota_result_value);
    if (ret != 0) {
      RERROR << name()
             << ": Check Device Ota Finish Status Failed: ret = " << ret
             << ", uuid = " << uuid;
      return -1;
    }
  }
  return 0;
}

int SuperSensor::CheckDeviceOtaSuccess(const std::string &uuid,
                                       bool &ota_result_value) {
  if (device_manager_ptr_ != nullptr) {
    int ret = device_manager_ptr_->checkOtaSuccess(uuid, ota_result_value);
    if (ret != 0) {
      RERROR << name()
             << ": Check Device Ota Success Status Failed: ret = " << ret
             << ", uuid = " << uuid;
      return -1;
    }
  }
  return 0;
}

#if defined(ENABLE_USE_CUDA)
int SuperSensor::InitCuda() {
  if (pYuvBuf_ == nullptr) {
    CHECK_CUDA(
        cudaMalloc(reinterpret_cast<void **>(&pYuvBuf_), image_nv12_size_));
  }

  if (pRgbBuf_ == nullptr) {
    CHECK_CUDA(
        cudaMalloc(reinterpret_cast<void **>(&pRgbBuf_), image_rgb_size_));
  }

  if (pOutBuf_ == nullptr) {
    CHECK_CUDA(
        cudaMalloc(reinterpret_cast<void **>(&pOutBuf_), image_rgb_size_));
  }
  return 0;
}

int SuperSensor::ReleaseCuda() {
  if (pYuvBuf_) {
    CHECK_CUDA(cudaFree(pYuvBuf_));
    pYuvBuf_ = nullptr;
  }
  if (pRgbBuf_) {
    CHECK_CUDA(cudaFree(pRgbBuf_));
    pRgbBuf_ = nullptr;
  }
  if (pOutBuf_) {
    CHECK_CUDA(cudaFree(pOutBuf_));
    pOutBuf_ = nullptr;
  }
  return 0;
}
#endif // ENABLE_USE_CUDA

void SuperSensor::device_plug_handle(
    const robosense::common::DeviceEvent_t &deviceEvent) {
  if (deviceEvent.event_type == robosense::common::DEVICE_EVENT_ATTACH) {
    RINFO << name() << ": attach: uuid = "
          << std::string(deviceEvent.uuid, deviceEvent.uuid_size);
  } else if (deviceEvent.event_type == robosense::common::DEVICE_EVENT_DETACH) {
    RINFO << name() << ": detach: uuid = "
          << std::string(deviceEvent.uuid, deviceEvent.uuid_size);
  } else {
    RINFO << name() << ": unknown: uuid = "
          << std::string(deviceEvent.uuid, deviceEvent.uuid_size);
  }
  if (device_event_cb_ != nullptr &&
      deviceEvent.event_type != robosense::common::DEVICE_EVENT_UNKNOWN) {
    device_event_cb_(deviceEvent);
  }
}

void SuperSensor::image_handle(
    const std::shared_ptr<robosense::lidar::ImageData> &msgPtr,
    const std::string &uuid) {
  if (msgPtr == nullptr) {
    RERROR << name()
           << ": robosense::lidar::ImageData is nullptr: uuid = " << uuid;
    return;
  }
  if (output_stat_info_) {
    std::lock_guard<std::mutex> lg(stat_mapper_mtx_);
    stat_mapper_[RS_DEVICE_DATA_TYPE::RS_DEVICE_DATA_IMAGE]++;
  }
  ++image_seq_;

  // 格式转换
  std::shared_ptr<robosense::common::ImageFrame> copyImagePtr(
      new robosense::common::ImageFrame());
  to_img_output_type(msgPtr, copyImagePtr);

  // 回调
  if (image_frame_cb_2_ != nullptr) {
    image_frame_cb_2_(copyImagePtr);
  }

  // 保存最后一帧
  {
    std::lock_guard<std::mutex> lg(image_last_msg_mtx_);
    image_last_frame_ = copyImagePtr;
  }

  // if (static_cast<int>(image_output_type_) &
  //     static_cast<int>(RS_IMAGE_OUTPUT_NV12)) {
  //   nv12_handle(msgPtr, uuid);
  // }
  // if (static_cast<int>(image_output_type_) &
  //     static_cast<int>(RS_IMAGE_OUTPUT_RGB8)) {
  //   rgb_handle(msgPtr, uuid);
  // }
}

void SuperSensor::nv12_handle(
    const std::shared_ptr<robosense::lidar::ImageData> &msgPtr,
    const std::string &uuid) {
  (void)(uuid);
  if (image_frame_cb_2_ != nullptr) {
    std::shared_ptr<robosense::common::ImageFrame> copyImagePtr(
        new robosense::common::ImageFrame());
    copyImagePtr->width = msgPtr->width;
    copyImagePtr->height = msgPtr->height;
    copyImagePtr->frame_format = robosense::common::FRAME_FORMAT_NV12;
    copyImagePtr->step = msgPtr->width * 2;
    copyImagePtr->sequence = image_seq_;
    const uint64_t timestamp = msgPtr->timestamp * 1e6;
    copyImagePtr->capture_time.tv_sec = timestamp / 1000000;
    copyImagePtr->capture_time.tv_usec = timestamp % 1000000;
    copyImagePtr->data_bytes = msgPtr->data_bytes;
    // copyImagePtr->data = std::shared_ptr<uint8_t>(
    //     new uint8_t[msgPtr->data_bytes], std::default_delete<uint8_t[]>());
    // memcpy(copyImagePtr->data.get(), msgPtr->data,
    //        sizeof(uint8_t) * msgPtr->data_bytes);
    copyImagePtr->data = msgPtr->data;
    image_frame_cb_2_(copyImagePtr);
  }
}

void SuperSensor::rgb_handle(
    const std::shared_ptr<robosense::lidar::ImageData> &msgPtr,
    const std::string &uuid) {
  (void)(uuid);
  // create rgb image frame
  if (image_frame_cb_2_ != nullptr) {
    unsigned int required_size;
    int camera_height = msgPtr->height;
    int camera_width = msgPtr->width;

    required_size = camera_height * camera_width * 3;
    if (rgb_buf_.size() != required_size) {
      rgb_buf_.resize(required_size, 0);
    }
#if ENABLE_NV12_TO_RGB_DEBUG
    const auto startTimestampNs = rally::Time::getNow().toNanosecond();
#endif // ENABLE_NV12_TO_RGB_DEBUG
#if defined(ENABLE_USE_CUDA)
    // NV12 to RGB24
    if (image_height_ == static_cast<uint32_t>(camera_height) &&
        image_width_ == static_cast<uint32_t>(camera_width) &&
        image_nv12_size_ == msgPtr->data_bytes) {
      cudaMemcpy(pYuvBuf_, msgPtr->data.get(), image_nv12_size_,
                 cudaMemcpyHostToDevice);
      CUDA_NV12ToRGB(pYuvBuf_, pRgbBuf_, pOutBuf_, camera_width, camera_height);
      cudaMemcpy(rgb_buf_.data(), pOutBuf_, image_rgb_size_,
                 cudaMemcpyDeviceToHost);
    }
#else
    // NV12 to RGB24
    unsigned int y_size;
    uint8_t *y_plane;
    uint8_t *uv_plane;

    y_size = camera_height * camera_width;
    y_plane = static_cast<uint8_t *>(msgPtr->data.get());
    uv_plane = y_plane + y_size;

    for (int i = 0; i < camera_height; i++) {
      int y_offset = camera_width * i;
      int uv_offset = camera_width * (i >> 1);
      int rgb_offset = y_offset * 3;

      for (int j = 0; j < camera_width; j++) {
        int y = y_plane[y_offset++];
        int u = uv_plane[uv_offset];
        int v = uv_plane[uv_offset + 1];

        int r = y + (1.402 * (v - 128));
        int g = y - (0.34414 * (u - 128)) - (0.71414 * (v - 128));
        int b = y + (1.772 * (u - 128));

        rgb_buf_[rgb_offset++] = (r > 255) ? 255 : (r < 0) ? 0 : r;
        rgb_buf_[rgb_offset++] = (g > 255) ? 255 : (g < 0) ? 0 : g;
        rgb_buf_[rgb_offset++] = (b > 255) ? 255 : (b < 0) ? 0 : b;

        if (0 != (j & 1)) {
          uv_offset += 2;
        }
      }
    }
#endif // ENABLE_USE_CUDA
#if ENABLE_NV12_TO_RGB_DEBUG
    const auto midTimestampNs = rally::Time::getNow().toNanosecond();
#endif // ENABLE_NV12_TO_RGB_DEBUG

    std::shared_ptr<robosense::common::ImageFrame> copyImagePtr(
        new robosense::common::ImageFrame());
    copyImagePtr->state = msgPtr->state;
    copyImagePtr->width = msgPtr->width;
    copyImagePtr->height = msgPtr->height;
    copyImagePtr->frame_format = robosense::common::FRAME_FORMAT_RGB24;
    copyImagePtr->step = msgPtr->width * 3;
    copyImagePtr->sequence = image_seq_;
    const uint64_t timestamp = msgPtr->timestamp * 1e6;
    copyImagePtr->capture_time.tv_sec = timestamp / 1000000;
    copyImagePtr->capture_time.tv_usec = timestamp % 1000000;
    copyImagePtr->data_bytes = rgb_buf_.size();
    copyImagePtr->data = std::shared_ptr<uint8_t>(
        new uint8_t[rgb_buf_.size()], std::default_delete<uint8_t[]>());
    memcpy(copyImagePtr->data.get(), rgb_buf_.data(),
           sizeof(uint8_t) * rgb_buf_.size());
    image_frame_cb_2_(copyImagePtr);
#if ENABLE_NV12_TO_RGB_DEBUG
    const auto endTimestampNs = rally::Time::getNow().toNanosecond();
    RINFO << name() << ": To RGB = " << (midTimestampNs - startTimestampNs)
          << " (ns), Depth Copy = " << (endTimestampNs - midTimestampNs)
          << " (ns) !";
#endif // ENABLE_NV12_TO_RGB_DEBUG
  }
}

void SuperSensor::depth_handle(
    const std::shared_ptr<PointCloudT<RsPointXYZIRT>> &msgPtr,
    const std::string &uuid) {
  if (msgPtr == nullptr) {
    RERROR << name()
           << ": PointCloudT<RsPointXYZIRT> is nullptr: uuid = " << uuid;
    return;
  }
  if (output_stat_info_) {
    std::lock_guard<std::mutex> lg(stat_mapper_mtx_);
    stat_mapper_[RS_DEVICE_DATA_TYPE::RS_DEVICE_DATA_POINTCLOUD]++;
  }

  // 格式转换
  std::shared_ptr<robosense::common::DepthFrame> copyDepthPtr(
      new robosense::common::DepthFrame());
  const uint64_t timestamp = msgPtr->timestamp * 1e6;
  copyDepthPtr->capture_time.tv_sec = timestamp / 1000000;
  copyDepthPtr->capture_time.tv_usec = timestamp % 1000000;

  const uint32_t pointCnt = msgPtr->size();
  copyDepthPtr->point_nums = pointCnt;
  copyDepthPtr->points = std::shared_ptr<robosense::common::CloudPointXYZIRT>(
      new robosense::common::CloudPointXYZIRT[pointCnt],
      std::default_delete<robosense::common::CloudPointXYZIRT[]>());
  memcpy(copyDepthPtr->points.get(), msgPtr->points.data(),
         sizeof(robosense::common::CloudPointXYZIRT) * pointCnt);

  // 回调
  if (depth_frame_cb_2_ != nullptr) {
    depth_frame_cb_2_(copyDepthPtr);
  }

  // 保存最后一帧
  {
    std::lock_guard<std::mutex> lg(depth_last_msg_mtx_);
    depth_last_frame_ = copyDepthPtr;
  }
}

void SuperSensor::imu_handle(
    const std::shared_ptr<robosense::lidar::ImuData> &msgPtr,
    const std::string &uuid) {
  if (msgPtr == nullptr) {
    RERROR << name()
           << ": robosense::lidar::ImuData is nullptr: uuid = " << uuid;
    return;
  }
  if (output_stat_info_) {
    std::lock_guard<std::mutex> lg(stat_mapper_mtx_);
    stat_mapper_[RS_DEVICE_DATA_TYPE::RS_DEVICE_DATA_IMU]++;
  }

  // 格式转换
  std::shared_ptr<robosense::common::MotionFrame> copyMotionPtr(
      new robosense::common::MotionFrame);
  copyMotionPtr->state = msgPtr->state;
  copyMotionPtr->accel.x = msgPtr->linear_acceleration_x;
  copyMotionPtr->accel.y = msgPtr->linear_acceleration_y;
  copyMotionPtr->accel.z = msgPtr->linear_acceleration_z;
  copyMotionPtr->gyro.x = msgPtr->angular_velocity_x;
  copyMotionPtr->gyro.y = msgPtr->angular_velocity_y;
  copyMotionPtr->gyro.z = msgPtr->angular_velocity_z;
  copyMotionPtr->temperature = 0;
  copyMotionPtr->orientation.x = msgPtr->orientation_x;
  copyMotionPtr->orientation.y = msgPtr->orientation_y;
  copyMotionPtr->orientation.z = msgPtr->orientation_z;
  copyMotionPtr->orientation.w = msgPtr->orientation_w;
  //
  const uint64_t timestamp = msgPtr->timestamp * 1e6;
  copyMotionPtr->capture_time.tv_sec = timestamp / 1000000;
  copyMotionPtr->capture_time.tv_usec = timestamp % 1000000;

  // 回调
  if (motion_frame_cb_2_ != nullptr) {
    motion_frame_cb_2_(copyMotionPtr);
  }

  // 保留最后一帧
  {
    std::lock_guard<std::mutex> lg(imu_last_msg_mtx_);
    motion_last_frame_ = copyMotionPtr;
  }
}

void SuperSensor::to_img_output_type(
    const std::shared_ptr<robosense::lidar::ImageData> &msgPtr,
    std::shared_ptr<robosense::common::ImageFrame> &copyImagePtr) {
  switch (image_input_type_) {
  case robosense::lidar::frame_format::FRAME_FORMAT_NV12: {
    nv12_to_img_output_type(msgPtr, copyImagePtr);
    break;
  }
  case robosense::lidar::frame_format::FRAME_FORMAT_BGR24: {
    bgr24_to_img_output_type(msgPtr, copyImagePtr);
    break;
  }
  case robosense::lidar::frame_format::FRAME_FORMAT_RGB24: {
    rgb24_to_img_output_type(msgPtr, copyImagePtr);
    break;
  }
  case robosense::lidar::frame_format::FRAME_FORMAT_YUV422: {
    yuv422_to_img_output_type(msgPtr, copyImagePtr);
    break;
  }
  default: {
  }
  }
}

void SuperSensor::nv12_to_img_output_type(
    const std::shared_ptr<robosense::lidar::ImageData> &msgPtr,
    std::shared_ptr<robosense::common::ImageFrame> &copyImagePtr) {
  if (image_output_type_ == RS_IMAGE_OUTPUT_NV12) {
    copyImagePtr->width = msgPtr->width;
    copyImagePtr->height = msgPtr->height;
    copyImagePtr->frame_format = robosense::common::FRAME_FORMAT_NV12;
    copyImagePtr->step = msgPtr->width * 2;
    copyImagePtr->sequence = image_seq_;
    const uint64_t timestamp = msgPtr->timestamp * 1e6;
    copyImagePtr->capture_time.tv_sec = timestamp / 1000000;
    copyImagePtr->capture_time.tv_usec = timestamp % 1000000;
    copyImagePtr->data_bytes = msgPtr->data_bytes;
    copyImagePtr->data = msgPtr->data;
  } else if (image_output_type_ == RS_IMAGE_OUTPUT_RGB8) {
    unsigned int required_size;
    int camera_height = msgPtr->height;
    int camera_width = msgPtr->width;

    required_size = camera_height * camera_width * 3;
    if (rgb_buf_.size() != required_size) {
      rgb_buf_.resize(required_size, 0);
    }
#if ENABLE_NV12_TO_RGB_DEBUG
    const auto startTimestampNs = rally::Time::getNow().toNanosecond();
#endif // ENABLE_NV12_TO_RGB_DEBUG
#if defined(ENABLE_USE_CUDA)
    // NV12 to RGB24
    if (image_height_ == static_cast<uint32_t>(camera_height) &&
        image_width_ == static_cast<uint32_t>(camera_width) &&
        image_nv12_size_ == msgPtr->data_bytes) {
      cudaMemcpy(pYuvBuf_, msgPtr->data.get(), image_nv12_size_,
                 cudaMemcpyHostToDevice);
      CUDA_NV12ToRGB(pYuvBuf_, pRgbBuf_, pOutBuf_, camera_width, camera_height);
      cudaMemcpy(rgb_buf_.data(), pOutBuf_, image_rgb_size_,
                 cudaMemcpyDeviceToHost);
    }
#else
    // NV12 to RGB24
    unsigned int y_size;
    uint8_t *y_plane;
    uint8_t *uv_plane;

    y_size = camera_height * camera_width;
    y_plane = static_cast<uint8_t *>(msgPtr->data.get());
    uv_plane = y_plane + y_size;

    for (int i = 0; i < camera_height; i++) {
      int y_offset = camera_width * i;
      int uv_offset = camera_width * (i >> 1);
      int rgb_offset = y_offset * 3;

      for (int j = 0; j < camera_width; j++) {
        int y = y_plane[y_offset++];
        int u = uv_plane[uv_offset];
        int v = uv_plane[uv_offset + 1];

        int r = y + (1.402 * (v - 128));
        int g = y - (0.34414 * (u - 128)) - (0.71414 * (v - 128));
        int b = y + (1.772 * (u - 128));

        rgb_buf_[rgb_offset++] = (r > 255) ? 255 : (r < 0) ? 0 : r;
        rgb_buf_[rgb_offset++] = (g > 255) ? 255 : (g < 0) ? 0 : g;
        rgb_buf_[rgb_offset++] = (b > 255) ? 255 : (b < 0) ? 0 : b;

        if (0 != (j & 1)) {
          uv_offset += 2;
        }
      }
    }
#endif // ENABLE_USE_CUDA
#if ENABLE_NV12_TO_RGB_DEBUG
    const auto midTimestampNs = rally::Time::getNow().toNanosecond();
#endif // ENABLE_NV12_TO_RGB_DEBUG
    copyImagePtr->state = msgPtr->state;
    copyImagePtr->width = msgPtr->width;
    copyImagePtr->height = msgPtr->height;
    copyImagePtr->frame_format = robosense::common::FRAME_FORMAT_RGB24;
    copyImagePtr->step = msgPtr->width * 3;
    copyImagePtr->sequence = image_seq_;
    const uint64_t timestamp = msgPtr->timestamp * 1e6;
    copyImagePtr->capture_time.tv_sec = timestamp / 1000000;
    copyImagePtr->capture_time.tv_usec = timestamp % 1000000;
    copyImagePtr->data_bytes = rgb_buf_.size();
    copyImagePtr->data = std::shared_ptr<uint8_t>(
        new uint8_t[rgb_buf_.size()], std::default_delete<uint8_t[]>());
    memcpy(copyImagePtr->data.get(), rgb_buf_.data(),
           sizeof(uint8_t) * rgb_buf_.size());
  }
}

void SuperSensor::rgb24_to_img_output_type(
    const std::shared_ptr<robosense::lidar::ImageData> &msgPtr,
    std::shared_ptr<robosense::common::ImageFrame> &copyImagePtr) {
  const int width = msgPtr->width;
  const int height = msgPtr->height;
  copyImagePtr->state = msgPtr->state;
  copyImagePtr->width = width;
  copyImagePtr->height = height;
  copyImagePtr->sequence = image_seq_;
  const uint64_t timestamp = msgPtr->timestamp * 1e6;
  copyImagePtr->capture_time.tv_sec = timestamp / 1000000;
  copyImagePtr->capture_time.tv_usec = timestamp % 1000000;

  if (image_output_type_ == RS_IMAGE_OUTPUT_NV12) {
    // NV12输出模式
    const size_t nv12_size = width * height * 3 / 2;
    unsigned char *nv12_host_buf = nullptr;
#if defined(ENABLE_USE_CUDA)
    cudaMemcpy(pRgbBuf_, msgPtr->data.get(), nv12_size, cudaMemcpyHostToDevice);
    CUDA_RGBToNV12(pRgbBuf_, pYuvBuf_, nv12_host_buf, image_width_,
                   image_height_);
    // 配置输出帧
    copyImagePtr->frame_format = robosense::common::FRAME_FORMAT_NV12;
    copyImagePtr->step = width;
    copyImagePtr->data_bytes = nv12_size;
    copyImagePtr->data =
        std::shared_ptr<uint8_t>(nv12_host_buf, [](uint8_t *p) { delete[] p; });
#else
    // CPU路径
    nv12_host_buf = new uint8_t[nv12_size];
    const uint8_t *rgb_data = msgPtr->data.get();
    uint8_t *y_plane = nv12_host_buf;
    uint8_t *uv_plane = y_plane + width * height;

    // Y分量计算
    for (int i = 0; i < height; ++i) {
      for (int j = 0; j < width; ++j) {
        const int rgb_idx = (i * width + j) * 3;
        const uint8_t r = rgb_data[rgb_idx];
        const uint8_t g = rgb_data[rgb_idx + 1];
        const uint8_t b = rgb_data[rgb_idx + 2];
        y_plane[i * width + j] =
            static_cast<uint8_t>(0.299 * r + 0.587 * g + 0.114 * b);
      }
    }

    // UV分量计算（2x2下采样）
    for (int i = 0; i < height; i += 2) {
      for (int j = 0; j < width; j += 2) {
        int u_sum = 0, v_sum = 0;
        int count = 0;

        for (int di = 0; di < 2 && (i + di) < height; ++di) {
          for (int dj = 0; dj < 2 && (j + dj) < width; ++dj) {
            const int rgb_idx = ((i + di) * width + (j + dj)) * 3;
            const uint8_t r = rgb_data[rgb_idx];
            const uint8_t g = rgb_data[rgb_idx + 1];
            const uint8_t b = rgb_data[rgb_idx + 2];

            int u = static_cast<int>(-0.169 * r - 0.331 * g + 0.5 * b + 128);
            int v = static_cast<int>(0.5 * r - 0.419 * g - 0.081 * b + 128);
            u_sum += u;
            v_sum += v;
            count++;
          }
        }

        const int uv_idx = (i / 2) * (width / 2) + (j / 2);
        uv_plane[uv_idx * 2] = static_cast<uint8_t>(u_sum / count);
        uv_plane[uv_idx * 2 + 1] = static_cast<uint8_t>(v_sum / count);
      }
    }

    copyImagePtr->frame_format = robosense::common::FRAME_FORMAT_NV12;
    copyImagePtr->step = width;
    copyImagePtr->data_bytes = nv12_size;
    copyImagePtr->data =
        std::shared_ptr<uint8_t>(nv12_host_buf, [](uint8_t *p) { delete[] p; });
#endif

  } else if (image_output_type_ == RS_IMAGE_OUTPUT_RGB8) {
    // Direct copy for RGB24 to RGB8 (assumed same format)
    copyImagePtr->frame_format = robosense::common::FRAME_FORMAT_RGB24;
    copyImagePtr->step = width * 3;
    copyImagePtr->data_bytes = msgPtr->data_bytes;
    copyImagePtr->data = msgPtr->data; // Share data pointer to avoid copy
  }
}

void SuperSensor::bgr24_to_img_output_type(
    const std::shared_ptr<robosense::lidar::ImageData> &msgPtr,
    std::shared_ptr<robosense::common::ImageFrame> &copyImagePtr) {
  const int width = msgPtr->width;
  const int height = msgPtr->height;
  const uint8_t *bgr_data = msgPtr->data.get();

  // 设置公共元数据
  copyImagePtr->width = width;
  copyImagePtr->height = height;
  copyImagePtr->sequence = image_seq_;
  const uint64_t timestamp = msgPtr->timestamp * 1e6;
  copyImagePtr->capture_time.tv_sec = timestamp / 1000000;
  copyImagePtr->capture_time.tv_usec = timestamp % 1000000;

  if (image_output_type_ == RS_IMAGE_OUTPUT_NV12) {
    // NV12输出模式（BGR24->NV12）
    const size_t nv12_size = width * height * 3 / 2;
    cv::Mat bgr_mat(height, width, CV_8UC3, const_cast<uint8_t *>(bgr_data));
    cv::Mat yuv_mat;

    // 使用OpenCV进行颜色空间转换[1](@ref)
    cv::cvtColor(bgr_mat, yuv_mat, cv::COLOR_BGR2YUV_I420);

    // 重组为NV12格式（Y平面 + UV交错平面）
    uint8_t *nv12_buf = new uint8_t[nv12_size];
    memcpy(nv12_buf, yuv_mat.data, width * height); // Y平面
    memcpy(nv12_buf + width * height, yuv_mat.data + width * height,
           width * height / 2); // UV交错平面

    copyImagePtr->frame_format = robosense::common::FRAME_FORMAT_NV12;
    copyImagePtr->data =
        std::shared_ptr<uint8_t>(nv12_buf, [](uint8_t *p) { delete[] p; });
    copyImagePtr->data_bytes = nv12_size;
    copyImagePtr->step = width;
  } else if (static_cast<int>(image_output_type_) & RS_IMAGE_OUTPUT_RGB8) {
    // RGB8输出（BGR转RGB）
    cv::Mat bgr_mat(height, width, CV_8UC3, const_cast<uint8_t *>(bgr_data));
    cv::Mat rgb_mat;
    cv::cvtColor(bgr_mat, rgb_mat, cv::COLOR_BGR2RGB);

    copyImagePtr->frame_format = robosense::common::FRAME_FORMAT_RGB24;
    copyImagePtr->data = std::shared_ptr<uint8_t>(
        new uint8_t[rgb_mat.total() * 3], [](uint8_t *p) { delete[] p; });
    memcpy(copyImagePtr->data.get(), rgb_mat.data, rgb_mat.total() * 3);
    copyImagePtr->data_bytes = rgb_mat.total() * 3;
    copyImagePtr->step = width * 3;
  }
}

void SuperSensor::yuv422_to_img_output_type(
    const std::shared_ptr<robosense::lidar::ImageData> &msgPtr,
    std::shared_ptr<robosense::common::ImageFrame> &copyImagePtr) {
  const int width = msgPtr->width;
  const int height = msgPtr->height;
  const uint8_t *yuyv_data = msgPtr->data.get();

  // 设置公共元数据
  copyImagePtr->width = width;
  copyImagePtr->height = height;
  copyImagePtr->sequence = image_seq_;
  const uint64_t timestamp = msgPtr->timestamp * 1e6;
  copyImagePtr->capture_time.tv_sec = timestamp / 1000000;
  copyImagePtr->capture_time.tv_usec = timestamp % 1000000;

  if (static_cast<int>(image_output_type_) & RS_IMAGE_OUTPUT_NV12) {
    // NV12输出模式（YUV422->NV12）
    const size_t nv12_size = width * height * 3 / 2;
    uint8_t *nv12_buf = new uint8_t[nv12_size];

    // Y分量直接复制
    memcpy(nv12_buf, yuyv_data, width * height);

    // UV分量处理（YUYV每两个像素共享一组UV）
    uint8_t *uv_plane = nv12_buf + width * height;
    for (int i = 0; i < height; ++i) {
      for (int j = 0; j < width; j += 2) {
        const int src_idx = i * width * 2 + j * 2;
        const int dst_idx = (i / 2) * width + j;
        uv_plane[dst_idx] = yuyv_data[src_idx + 1];     // U
        uv_plane[dst_idx + 1] = yuyv_data[src_idx + 3]; // V
      }
    }

    copyImagePtr->frame_format = robosense::common::FRAME_FORMAT_NV12;
    copyImagePtr->data =
        std::shared_ptr<uint8_t>(nv12_buf, [](uint8_t *p) { delete[] p; });
    copyImagePtr->data_bytes = nv12_size;
    copyImagePtr->step = width;
  } else if (static_cast<int>(image_output_type_) & RS_IMAGE_OUTPUT_RGB8) {
    // RGB8输出（YUV422->RGB）
    cv::Mat yuyv_mat(height, width, CV_8UC2, const_cast<uint8_t *>(yuyv_data));
    cv::Mat rgb_mat;

    // 使用OpenCV的YUYV转RGB[1](@ref)
    cv::cvtColor(yuyv_mat, rgb_mat, cv::COLOR_YUV2RGB_YUYV);

    copyImagePtr->frame_format = robosense::common::FRAME_FORMAT_RGB24;
    copyImagePtr->data = std::shared_ptr<uint8_t>(
        new uint8_t[rgb_mat.total() * 3], [](uint8_t *p) { delete[] p; });
    memcpy(copyImagePtr->data.get(), rgb_mat.data, rgb_mat.total() * 3);
    copyImagePtr->data_bytes = rgb_mat.total() * 3;
    copyImagePtr->step = width * 3;
  }
}

int SuperSensor::saveDepthDataToFile(
    const std::string &save_dir_path,
    const std::shared_ptr<robosense::common::DepthFrame> &depthFramePtr) {
  if (depthFramePtr == nullptr) {
    RERROR << name() << ": Input Depth Frame Is Nullptr !";
    return -1;
  }

  // Step1: 构造点云
  pcl::PointCloud<robosense::common::CloudPointXYZIRT>::Ptr cloudPtr(
      new pcl::PointCloud<robosense::common::CloudPointXYZIRT>());

  cloudPtr->resize(depthFramePtr->point_nums);
  memcpy(cloudPtr->points.data(), depthFramePtr->points.get(),
         sizeof(robosense::common::CloudPointXYZIRT) *
             depthFramePtr->point_nums);

  cloudPtr->width = 1;
  cloudPtr->height = depthFramePtr->point_nums;

  const uint64_t depthTimestampNs = depthFramePtr->capture_time.tv_sec * 1e9 +
                                    depthFramePtr->capture_time.tv_usec * 1e3;

  const std::string &full_save_path =
      (std::filesystem::path(save_dir_path) /
       (std::to_string(depthTimestampNs) + ".pcd"))
          .string();

  RINFO << name() << ": ACData(depth): full_save_path = " << full_save_path;

  // Step2: 保存点云
  int ret = pcl::io::savePCDFileBinary(full_save_path, *cloudPtr);
  if (ret != 0) {
    RERROR << name() << ": Save Depth Data To PCD File Failed: ret = " << ret;
    return -2;
  }

  return 0;
}

int SuperSensor::saveImageDataToFile(
    const std::string &save_dir_path,
    const std::shared_ptr<robosense::common::ImageFrame> &imageFramePtr) {
  if (imageFramePtr == nullptr) {
    RERROR << name() << ": Input Imae Frame Is Nullptr !";
    return -1;
  }

  // 检查jpeg coder 的重新初始化条件
  if (jpeg_coder_ptr_ == nullptr ||
      jpeg_coder_config_.imageWidth != imageFramePtr->width ||
      jpeg_coder_config_.imageHeight != imageFramePtr->height ||
      jpeg_coder_config_.imageFrameFormat != imageFramePtr->frame_format) {
    jpeg_coder_config_.coderType =
        robosense::jpeg::JPEG_CODER_TYPE::RS_JPEG_CODER_ENCODE;
    jpeg_coder_config_.imageWidth = imageFramePtr->width;
    jpeg_coder_config_.imageHeight = imageFramePtr->height;
    jpeg_coder_config_.jpegQuality = 70;
    jpeg_coder_config_.imageFrameFormat = imageFramePtr->frame_format;

    jpeg_coder_ptr_.reset(new robosense::jpeg::JpegCoder());

    int ret = jpeg_coder_ptr_->init(jpeg_coder_config_);
    if (ret != 0) {
      RERROR << name() << ": Jpeg Coder Initial Failed: ret = " << ret;
      return -2;
    }
  }

  // 重新初始化JPEG 缓冲区
  size_t jpeg_buffer_size = imageFramePtr->width * imageFramePtr->height * 4;
  if (jpeg_buffer_.size() != jpeg_buffer_size) {
    jpeg_buffer_.resize(jpeg_buffer_size);
  }

  int ret = jpeg_coder_ptr_->encode(imageFramePtr->data.get(),
                                    imageFramePtr->data_bytes,
                                    jpeg_buffer_.data(), jpeg_buffer_size);
  if (ret != 0) {
    RERROR << name() << ": Jpeg Encode Failed: ret = " << ret;
    return -3;
  }

  const uint64_t imageTimestampNs = imageFramePtr->capture_time.tv_sec * 1e9 +
                                    imageFramePtr->capture_time.tv_usec * 1e3;

  const std::string &full_save_path =
      (std::filesystem::path(save_dir_path) /
       (std::to_string(imageTimestampNs) + ".jpeg"))
          .string();

  RINFO << name() << ": ACData(image): full_save_path = " << full_save_path;

  // 写文件
  std::ofstream ofstr(full_save_path,
                      std::ios_base::out | std::ios_base::binary);

  if (!ofstr.is_open()) {
    RERROR << name() << ": Open File: " << full_save_path
           << " To Write Jpeg Data Failed !";
    return -4;
  }

  if (!ofstr.write(reinterpret_cast<const char *>(jpeg_buffer_.data()),
                   jpeg_buffer_size)) {
    RERROR << name() << ": Write Jpeg Data To File: " << full_save_path
           << " Failed !";
    return -5;
  }

  return 0;
}

int SuperSensor::saveImuDataToFile(
    const std::string &save_dir_path,
    const std::shared_ptr<robosense::common::MotionFrame> &imuFramePtr) {
  if (imuFramePtr == nullptr) {
    RERROR << name() << ": Input Imu Frame Is Nullptr !";
    return -1;
  }

  const uint64_t imuTimestampNs = imuFramePtr->capture_time.tv_sec * 1e9 +
                                  imuFramePtr->capture_time.tv_usec * 1e3;
  const std::string &full_save_path =
      (std::filesystem::path(save_dir_path) /
       (std::to_string(imuTimestampNs) + ".imu"))
          .string();

  // imu save path
  RINFO << name() << ": ACData(imu): full_save_path = " << full_save_path;

  std::ofstream ofstr(full_save_path,
                      std::ios_base::out | std::ios_base::binary);
  if (!ofstr.is_open()) {
    RERROR << name() << ": Open File: " << full_save_path
           << " To Write Failed !";
    return -2;
  }

  ofstr << "accel[x, y, z]: " << std::to_string(imuFramePtr->accel.x) << ", "
        << std::to_string(imuFramePtr->accel.y) << ", "
        << std::to_string(imuFramePtr->accel.z) << std::endl;
  ofstr << "gyro[x, y, z]: " << std::to_string(imuFramePtr->gyro.x) << ", "
        << std::to_string(imuFramePtr->gyro.y) << ", "
        << std::to_string(imuFramePtr->gyro.z) << std::endl;
  ofstr << "orientation[w, x, y, z]: "
        << std::to_string(imuFramePtr->orientation.w) << ", "
        << std::to_string(imuFramePtr->orientation.x) << ", "
        << std::to_string(imuFramePtr->orientation.y) << ", "
        << std::to_string(imuFramePtr->orientation.z) << std::endl;
  ofstr.flush();
  ofstr.close();

  return 0;
}

} // namespace driver
} // namespace robosense
