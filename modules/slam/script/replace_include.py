#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Python脚本：替换 common.h 文件中的 include 语句
将 'rally/utils/utils.h' 替换为 'cyber/common/log.h'
"""

import os
import sys

def replace_include_in_file(file_path):
    """
    替换文件中的 include 语句
    """
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 执行替换
        old_include = '#include "rally/utils/utils.h"'
        new_include = '#include "cyber/common/log.h"'
        
        if old_include in content:
            new_content = content.replace(old_include, new_include)
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(new_content)
            
            print(f"✅ 成功替换文件: {file_path}")
            print(f"   替换前: {old_include}")
            print(f"   替换后: {new_include}")
            return True
        else:
            print(f"⚠️  未找到要替换的内容: {old_include}")
            return False
            
    except FileNotFoundError:
        print(f"❌ 文件未找到: {file_path}")
        return False
    except Exception as e:
        print(f"❌ 替换过程中出现错误: {e}")
        return False

def main():
    """
    主函数
    """
    # 目标文件路径（相对路径）
    target_file = "modules/slam/FAST-LIVO/include/common/common.h"
    
    print("🚀 开始执行 include 语句替换...")
    print(f"📁 目标文件: {target_file}")
    print()
    
    # 检查文件是否存在
    if not os.path.exists(target_file):
        print(f"❌ 目标文件不存在: {target_file}")
        sys.exit(1)
    
    # 执行替换
    success = replace_include_in_file(target_file)
    
    if success:
        print()
        print("🎉 替换操作完成！")
    else:
        print()
        print("❌ 替换操作失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()