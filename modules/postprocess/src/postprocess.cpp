/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "hyper_vision/postprocess/postprocess.h"

namespace robosense::postprocess
{
  int MultiSensorPostprocess::Init(const YAML::Node& cfg_node)
  {
    try {
      YAML::Node post_node;
      Utils::yamlSubNode(cfg_node, "postprocess", post_node);

      YAML::Node meta_node;
      Utils::yamlSubNode(post_node["multi_sensor_postprocess"], "motion_correct", meta_node);

      impl_ptr = std::make_shared<PostprocessImpl>();
      impl_ptr->Init(meta_node);
      RINFO << name() << "Initialization successful...";

      return 0;
    }
    catch (const std::exception& e) {
      RERROR << name() << "Initialization failed: " << e.what();
      return -1;
    }
  }

  int MultiSensorPostprocess::Start()
  {
    RINFO << name() << "Started...";
    return 0;
  }

  void MultiSensorPostprocess::Stop()
  {
    std::lock_guard<std::mutex> lock(data_state_mutex_);
    data_state_.reset();
    RINFO << name() << "Stopped...";
  }

  void MultiSensorPostprocess::AddData(const std::shared_ptr<robosense::common::MotionFrame>& msg_ptr)
  {
    if (not msg_ptr)
    {
      RWARN << name() << "Received null motion message";
      return ;
    }

    std::lock_guard<std::mutex> lock(data_state_mutex_);
    data_state_.has_odom = true;

    impl_ptr->AddData(msg_ptr);

    if (data_state_.is_ready())
      TryProcess();
  }

  void MultiSensorPostprocess::AddData(const std::shared_ptr<robosense::common::DepthFrame>& msg_ptr)
  {
    if (not msg_ptr)
    {
      RWARN << name() << "Received null depth message";
      return ;
    }

    std::lock_guard<std::mutex> lock(data_state_mutex_);
    data_state_.has_depth = true;

    impl_ptr->AddData(msg_ptr);

    if (data_state_.is_ready())
      TryProcess();
  }

  void MultiSensorPostprocess::AddData(const std::shared_ptr<robosense::common::ImageFrame>& msg_ptr)
  {
    if (not msg_ptr)
    {
      RWARN << name() << "Received null image message";
      return ;
    }

    std::lock_guard<std::mutex> lock(data_state_mutex_);
    data_state_.has_image = true;

    impl_ptr->AddData(msg_ptr);

    if (data_state_.is_ready())
      TryProcess();
  }

  void MultiSensorPostprocess::SetCallback(const std::function<void(const PostprocessOutputMsg::Ptr& msg_ptr)>& callback)
  {
    std::lock_guard<std::mutex> lock(output_msg_mtx_);
    output_msg_callback_ = callback;
  }

  void MultiSensorPostprocess::TryProcess()
  {
    // 调用此函数时已经持有 data_state_mutex_ 锁

    if (not data_state_.is_ready())
      return;

    PostprocessOutputMsg::Ptr out_msg_ptr(new PostprocessOutputMsg);

    auto start = std::chrono::high_resolution_clock::now();
    impl_ptr->Process(out_msg_ptr);
    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> duration = end - start;

    // 重置状态
    data_state_.reset();

    // 调用回调函数（释放锁后调用以避免死锁）
    std::function<void(const PostprocessOutputMsg::Ptr&)> callback;
    {
      std::lock_guard<std::mutex> callback_lock(output_msg_mtx_);
      callback = output_msg_callback_;
    }

    if (callback)
      callback(out_msg_ptr);

    RINFO << name() << "message successfully published, time consumption: " << duration.count() << " ms";
  }

} // namespace robosense::postprocess

