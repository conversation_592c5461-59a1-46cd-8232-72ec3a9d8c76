#include "hyper_vision/postprocess/postprocess_impl/postprocess_impl.h"
#include "hyper_vision/postprocess/postprocess_impl/imu_module.h"
#include "hyper_vision/postprocess/postprocess_impl/camera_model.h"


namespace robosense::postprocess
{
  void FromMsg(const robosense::common::DepthFrame& msg, pcl::PointCloud<PointXYZIRT>& cloud)
  {
    cloud.points.resize(msg.point_nums);
    for (int i = 0; i < msg.point_nums; ++i)
    {
      auto point = msg.points.get()[i];
      cloud.points[i].x         = point.x;
      cloud.points[i].y         = point.y;
      cloud.points[i].z         = point.z;
      cloud.points[i].intensity = point.intensity;
      cloud.points[i].ring      = point.ring;
      cloud.points[i].timestamp = point.timestamp;
    }
  }

  void PostprocessImpl::Init(const YAML::Node& cfg_node)
  {
    motion_cfg_ = parse_config(cfg_node);
    Utils::yamlRead(cfg_node, "CAMERA_LIDAR_TIME_DURA_THRES", threshold_);
    RINFO << name() << "camera-lidar time duration threshold is " << threshold_;

    InitCalib();

    imu_module_ = std::make_shared<IMUModule>(num_drift_, using_imu_linear_acceleration_, 9.81);
    imu_image_module_ = std::make_shared<IMUModule>(num_drift_, using_imu_linear_acceleration_, 9.81);

    CreateColormapLUT();

    RINFO << name() << "Initialization successful...";
  }

  void PostprocessImpl::AddData(const std::shared_ptr<common::MotionFrame>& msg_ptr)
  {
    auto imu_data = FromMsg(msg_ptr);
    imu_module_->addFrameData(imu_data);
    imu_image_module_->addFrameData(imu_data);
  }

  void PostprocessImpl::AddData(const std::shared_ptr<robosense::common::DepthFrame>& msg_ptr)
  {
    point_cloud_queue_.push(msg_ptr);
  }

  void PostprocessImpl::AddData(const std::shared_ptr<robosense::common::ImageFrame>& msg_ptr)
  {
    cam_queue_.push_back(msg_ptr);
    if (cam_queue_.size() > 30)
      cam_queue_.pop_front();
  }

  void PostprocessImpl::Process(const PostprocessOutputMsg::Ptr& msg_ptr)
  {
    out_msg_ptr_ = msg_ptr;
    if (ProcessPointCloud() and ProcessCompressedImage())
      return;

    RWARN << name() << "Set output message to be empty...";
    out_msg_ptr_->point_cloud.points_vec     = std::vector<TagPoint>(0);
    out_msg_ptr_->rgb_image.width            = 0;
    out_msg_ptr_->rgb_image.height           = 0;
    out_msg_ptr_->rgb_image.header.frame_id  = "rs_camera";
    out_msg_ptr_->rgb_image.header.timestamp = 0;
    out_msg_ptr_->rgb_image.header.seq_id    = 0;
    out_msg_ptr_->rgb_image.data_vec         = std::vector<unsigned char>(0);
  }

  void PostprocessImpl::InitCalib()
  {
    const LidarConfig& lidar_cfg = motion_cfg_.ldiar_config;
    const CameraConfig& cam_cfg = motion_cfg_.camera_config;
    const MotionConfig& motion_cfg = motion_cfg_.motion_config;

    // lidar2imu
    {
      const TransformXYZQuat& lidar2imu_cfg = lidar_cfg.T_Lidar2IMU;
      Eigen::Quaterniond q_lidar2imu(lidar2imu_cfg.qw, lidar2imu_cfg.qx, lidar2imu_cfg.qy, lidar2imu_cfg.qz);
      RINFO << name() << "[lidar2imu] w=" << q_lidar2imu.w() << " x="  << q_lidar2imu.x() << " y="  << q_lidar2imu.y() << " z="  << q_lidar2imu.z();

      T_imu_lidar_.block<3, 3>(0, 0) = q_lidar2imu.toRotationMatrix();
      T_imu_lidar_.block<3, 1>(0, 3) = Eigen::Vector3d(lidar2imu_cfg.x, lidar2imu_cfg.y, lidar2imu_cfg.z);
      T_lidar_imu_ = T_imu_lidar_.inverse();
    }

    // lidar2cam
    {
      const TransformXYZQuat& lidar2cam_cfg = lidar_cfg.T_Lidar2Cam;

      Eigen::Quaterniond q_lidar2cam(lidar2cam_cfg.qw, lidar2cam_cfg.qx, lidar2cam_cfg.qy, lidar2cam_cfg.qz);
      RINFO << name() << "[lidar2cam] w=" << q_lidar2cam.w() << " x="  << q_lidar2cam.x() << " y="  << q_lidar2cam.y() << " z="  << q_lidar2cam.z();

      Eigen::Vector3d trans(lidar2cam_cfg.x, lidar2cam_cfg.y, lidar2cam_cfg.z);
      RINFO << name() << "[lidar2cam] x=" << trans.x() << " y=" << trans.y() << " z="<< trans.z();

      T_cam_lidar_.block<3, 3>(0, 0) = q_lidar2cam.toRotationMatrix();
      T_cam_lidar_.block<3, 1>(0, 3) = trans;
    }

    // 畸变系数
    {
      std::vector<double> distortion_coeffs = cam_cfg.intrinsics.distortion_coeffs;
      distortion_coeffs_ = cv::Mat(distortion_coeffs).clone();
    }

    // 内参
    {
      std::vector<double> intrinsic_matrix = cam_cfg.intrinsics.camera_matrix;
      camera_intrisic_ = cv::Mat(3, 3, CV_64F, intrinsic_matrix.data()).clone();
    }

    // 运动矫正参数
    {
      projection_root_               = motion_cfg.projection_root;
      motion_correct_                = motion_cfg.motion_correct;
      using_imu_linear_acceleration_ = motion_cfg.using_imu_linear_acceleration;
      using_odom_linear_velocity_    = motion_cfg.using_odom_linear_velocity;
      frame_tail_                    = motion_cfg.frame_tail;
      num_drift_                     = motion_cfg.num_drift;
    }

    RINFO << name() << "The following are the motion correction parameters..." << std::endl
          << "MOTION_CORRECT                 : " + std::to_string(motion_correct_) << std::endl
          << "NUM_DRIFT                      : " + std::to_string(num_drift_) << std::endl
          << "USING_IMU_LINEAR_ACCELERATION  : " + std::to_string(using_imu_linear_acceleration_) << std::endl
          << "USING_ODOM_LINEAR_VELOCITY     : " + std::to_string(using_odom_linear_velocity_) << std::endl
          << "FRAME_TAIL                     : " + std::to_string(frame_tail_) << std::endl
          << "PROJ_IMG_TOPIC                 : " + projection_root_ << std::endl;

    T_cam_imu_ = T_cam_lidar_ * T_imu_lidar_.inverse();
    RINFO << name() << "Calibration parameter initialization is complete...";
  }

  bool PostprocessImpl::ProcessPointCloud()
  {

    auto start = std::chrono::high_resolution_clock::now();
    auto msg = point_cloud_queue_.popWait(1000);

    if (msg == nullptr)
      return false;

    pcl::PointCloud<PointXYZIRT>::Ptr ori_cloud(new pcl::PointCloud<PointXYZIRT>());
    FromMsg(*msg, *ori_cloud);

    int point_index = 0;
    double head_stamp = ori_cloud->points.front().timestamp;
    double tail_stamp = ori_cloud->points.back().timestamp;
    double points_stamp = frame_tail_ ? tail_stamp : head_stamp;

//    RINFO << name() << "Start processing point cloud, head stamp:" << head_stamp << " tail stamp:" << tail_stamp << " using stamp:" << points_stamp;

    MCStatus status = MCStatus::SKIPT;
    std::size_t count = 0, count_thres = 5;
    while (count++ < count_thres and not imu_module_->setHeadStamp(points_stamp, status, frame_tail_))
    {
      RERROR << name() << count << " IMU module failed to set timestamp. status: " << static_cast<int>(status);
      if (MCStatus::WAITING == status or MCStatus::SKIPT == status)
      {
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
        continue;
      }
      break;
    }
    if (MCStatus::SKIPT == status or MCStatus::WAITING == status)
      return false;

//    RINFO << name() << "IMU module successfully set timestamp. find frames. points timestamp:" << points_stamp
//        << " odom head:" << imu_module_->imu_window_.front().imu_data.timestamp
//        << " odom tail:" << imu_module_->imu_window_.back().imu_data.timestamp
//        << " odom size:" << imu_module_->imu_window_.size();

    std::size_t search_idx = 0;
    Eigen::Matrix4d T_i1_i2 = Eigen::Matrix4d::Identity();
    pcl::PointCloud<PointXYZIRT>::Ptr cm_cloud(new pcl::PointCloud<PointXYZIRT>());
    for (std::size_t i = 0, num_pts = ori_cloud->points.size(); i < num_pts; ++i)
    {
      const auto& current_point = ori_cloud->points.at(point_index);
      double cur_stamp = current_point.timestamp;
      if (cur_stamp > tail_stamp)
      {
        RERROR << "current stamp is greater than tail stamp. current stamp:" << cur_stamp << " tail stamp:" << tail_stamp;
        RERROR << "1st  point stamp:" << ori_cloud->points[0].timestamp
               << "2nd  point stamp:" << ori_cloud->points[1].timestamp
               << "3rd  point stamp:" << ori_cloud->points[2].timestamp
               << "last point stamp:" << ori_cloud->points[msg->point_nums - 1].timestamp;
      }

      if (not imu_module_->calHeadIMURot(cur_stamp, T_i1_i2, search_idx, frame_tail_))
      {
        RWARN << "Motion Correct Warning, calHeadIMURot failed. " << head_stamp << " " << cur_stamp << " " << tail_stamp;
        ++point_index;
        continue;
      }

      if (frame_tail_)
      {
        Eigen::Matrix<double, 4, 4> tmp = T_i1_i2.eval();
        T_i1_i2 = tmp.inverse();
      }

      Eigen::Vector4d pt2(current_point.x, current_point.y, current_point.z, 1.0);
      Eigen::Matrix4d T_l1_l2 = T_lidar_imu_ * T_i1_i2 * T_imu_lidar_;
      Eigen::Vector4d trans_pt1 = T_l1_l2 * pt2;

      PointXYZIRT cm_pt{};
      cm_pt.x = static_cast<float>(trans_pt1[0]);
      cm_pt.y = static_cast<float>(trans_pt1[1]);
      cm_pt.z = static_cast<float>(trans_pt1[2]);
      cm_pt.intensity = current_point.intensity;
      cm_pt.ring = point_index;
      cm_pt.timestamp = points_stamp;

      cm_cloud->push_back(cm_pt);
      ++point_index;
    }

    mc_point_cloud_vec_.add(cm_cloud);
    point_cloud_free_vec_.add(ori_cloud);

    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> duration = end - start;
//    RINFO << name() << "Successfully Process Point Cloud, Cost time: " << duration.count() << " ms";

    return true;
}

bool PostprocessImpl::ProcessCompressedImage()
{
  auto start = std::chrono::high_resolution_clock::now();

  pcl::PointCloud<PointXYZIRT>::Ptr ori_pts;
  pcl::PointCloud<PointXYZIRT>::Ptr cm_pts;
  ori_pts = point_cloud_free_vec_.back();
  cm_pts = mc_point_cloud_vec_.back();

  auto pts_stamp = cm_pts->points.front().timestamp;
  auto msg = findNearestCam(pts_stamp);
  if (msg == nullptr or msg->data == nullptr)
  {
    RERROR << "cannot find nearest camera message...";
    return false;
  }

  double cam_stamp = static_cast<double>(msg->capture_time.tv_sec) +
                     static_cast<double>(msg->capture_time.tv_usec) / 1e6;
//  RINFO << name() << "processing image stamp " << cam_stamp << " with point stamp " << pts_stamp;

  cv::Mat img(cv::Size(static_cast<int>(msg->width), static_cast<int>(msg->height)), CV_8UC3, msg->data.get());

  // Get the transformation matrix from camera to lidar
  Eigen::Matrix4d T_ic_il = Eigen::Matrix4d::Identity();
  Eigen::Matrix4d T_il_ic = Eigen::Matrix4d::Identity();
  std::size_t imu_search_idx = 0;
  if (motion_correct_)
  {
    if (cam_stamp < pts_stamp)
    {
      if (not imu_image_module_->calIMURot(cam_stamp, pts_stamp, T_ic_il, imu_search_idx))
      {
        RERROR << "Image Projection CalIMURot Error, cam_stamp: " << cam_stamp << " pts_stamp: " << pts_stamp;
      }
      T_il_ic = T_ic_il.inverse();
    }
    else if (cam_stamp > pts_stamp)
    {
      if (not imu_image_module_->calIMURot(pts_stamp, cam_stamp, T_il_ic, imu_search_idx))
      {
        RERROR << "Image Projection CalIMURot Error, cam_stamp: " << cam_stamp << " pts_stamp: " << pts_stamp;
      }
      T_ic_il = T_il_ic.inverse();
    }

    if (imu_search_idx > 20)
    {
      imu_image_module_->clearData(imu_search_idx - 20);
      imu_search_idx = imu_search_idx - 20;
    }
  }

  // Get the angle and linear velocity
//  Eigen::Matrix3d rotation_matrix = T_ic_il.block<3, 3>(0, 0);
//  Eigen::AngleAxisd angle_axis(rotation_matrix);
//  double angle = angle_axis.angle() * (180.0 / M_PI);
//  double angle_velocity = angle / std::abs(pts_stamp - cam_stamp);
//  Eigen::Vector3d translation = T_ic_il.block<3, 1>(0, 3);
//  double linear_velocity = translation.norm() / std::abs(pts_stamp - cam_stamp);
  Eigen::Matrix4d T_cam_cl = T_cam_imu_ * T_ic_il * T_imu_lidar_;

  // Project the point cloud to image
  std::vector<cv::Point2f> image_points;
  cv::Mat img_proj = img.clone();
  DrawProjImage(img_proj, cm_pts, T_cam_cl, image_points);

  pcl::PointCloud<pcl::PointXYZRGB>::Ptr rgb_pts(new pcl::PointCloud<pcl::PointXYZRGB>());
  Eigen::Matrix4d pub_trans = Eigen::Matrix4d::Identity();

  // 构造输出的点云消息
  out_msg_ptr_->point_cloud.points_vec.resize(image_points.size());
  for (size_t i = 0; i < image_points.size(); ++i)
  {
    auto& pt = out_msg_ptr_->point_cloud.points_vec[i];
    const auto& cm_pt = ori_pts->points[i];  // why is not cm_pts->points[i]
    Eigen::Vector4d pt_eigen(cm_pt.x, cm_pt.y, cm_pt.z, 1.0);
    Eigen::Vector4d pt_trans = pub_trans * pt_eigen;

    pt.x = static_cast<float>(pt_trans[0]);
    pt.y = static_cast<float>(pt_trans[1]);
    pt.z = static_cast<float>(pt_trans[2]);

    int u = static_cast<int>(image_points[i].x);
    int v = static_cast<int>(image_points[i].y);

    cv::Vec3b pixel_value(0, 0, 0);
    if (u >= 0 and u < img.cols and v >= 0 and v < img.rows)
    {
      pixel_value = img.at<cv::Vec3b>(v, u);
    }
    pt.r = pixel_value[0];
    pt.g = pixel_value[1];
    pt.b = pixel_value[2];
    pt.a = 255;

    pt.intensity = cm_pt.intensity;
    pt.timeStamp = static_cast<unsigned long long>(cm_pt.timestamp * 1e9);
  }

  if (not projection_root_.empty())
  {
    std::string file_name = projection_root_ + std::to_string(cam_stamp) + ".png";
    RINFO << name() << "Saved Image " + file_name;
    cv::cvtColor(img_proj, img_proj, cv::COLOR_BGR2RGB);
    cv::imwrite(file_name, img_proj);
  }

  // 构造输出的图像消息
  out_msg_ptr_->rgb_image.width = msg->width;
  out_msg_ptr_->rgb_image.height = msg->height;
  out_msg_ptr_->rgb_image.header.frame_id = "rs_camera";
  out_msg_ptr_->rgb_image.header.timestamp = static_cast<uint64_t>(
      static_cast<double>(msg->capture_time.tv_sec) * 1e9 + static_cast<double>(msg->capture_time.tv_usec) * 1e3
  );
  out_msg_ptr_->rgb_image.header.seq_id = msg->sequence;

  auto data = img_proj.data;
  out_msg_ptr_->rgb_image.data_vec = std::vector<unsigned char>(data, data + msg->data_bytes);

  auto end = std::chrono::high_resolution_clock::now();
  std::chrono::duration<double, std::milli> duration = end - start;
//  RINFO << name() << "Successfully Process Camera Image, Cost time: " << duration.count() << " ms" << std::endl;
  return true;
}

  std::shared_ptr<robosense::common::ImageFrame>
  PostprocessImpl::findNearestCam(double point_stamp)
  {
    std::shared_ptr<robosense::common::ImageFrame> res_msg = nullptr;
    double min_dur = std::numeric_limits<double>::max();
    for (const auto& msg: cam_queue_)
    {
      auto cur_stamp = static_cast<double>(msg->capture_time.tv_sec) +
                       static_cast<double>(msg->capture_time.tv_usec) / 1e6;
      auto time_diff = std::abs(cur_stamp - point_stamp);
      if (time_diff < threshold_ and time_diff < min_dur)
      {
        res_msg = msg;
        min_dur = time_diff;
      }
    }
    if (res_msg == nullptr)
    {
      auto tail_stamp = static_cast<double>(cam_queue_.back()->capture_time.tv_sec) +
                        static_cast<double>(cam_queue_.back()->capture_time.tv_usec) / 1e6;
      RERROR << "Failed to find cam, tail cam is " << tail_stamp << " " << point_stamp - tail_stamp;
    }
    return res_msg;
  }

  void PostprocessImpl::CreateColormapLUT()
  {
    // You can adjust the LUT size for more granularity
    int lut_size = 256;
    cv::Mat colormap_lut_gray = cv::Mat(lut_size, 1, CV_8UC1);
    colormap_lut_ = cv::Mat(lut_size, 1, CV_8UC3);

    // Fill the LUT with values from 0 to 255
    for (int i = 0; i < lut_size; ++i)
    {
      colormap_lut_gray.at<uchar>(i, 0) = static_cast<uchar>(i);
    }

    // Apply the colormap to the LUT
    cv::applyColorMap(colormap_lut_gray, colormap_lut_, cv::COLORMAP_JET);
  }

  int PostprocessImpl::GetColormapIndex(float depth)
  {
    depth = depth > max_z_ ? max_z_ : (depth < min_z_ ? min_z_ : depth);
    float normalized_depth = (depth - min_z_) / (max_z_ - min_z_);
    return static_cast<int>(normalized_depth * static_cast<float>(lut_size_ - 1));
  }

  void PostprocessImpl::DrawProjImage(cv::Mat& img,
                                      const pcl::PointCloud<PointXYZIRT>::Ptr& cm_pts,
                                      const Eigen::Matrix4d& transform,
                                      std::vector<cv::Point2f>& image_points)
  {
    std::vector<cv::Point3f> transform_points;
    for (const auto& point: cm_pts->points)
    {
      Eigen::Vector4d pt(point.x, point.y, point.z, 1.0);
      Eigen::Vector4d pt_trans = transform * pt;
      transform_points.emplace_back(pt_trans[0], pt_trans[1], pt_trans[2]);
    }

    cv::projectPoints(transform_points,
                      cv::Mat::zeros(3, 1, CV_64F),
                      cv::Mat::zeros(3, 1, CV_64F),
                      camera_intrisic_,
                      distortion_coeffs_,
                      image_points);

    for (size_t i = 0; i < image_points.size(); ++i)
    {
      int u = static_cast<int>(image_points[i].x);
      int v = static_cast<int>(image_points[i].y);
      if (u < 0 or u >= img.cols or v < 0 or v >= img.rows)
        continue;

      float depth = transform_points[i].z;
      int colormap_index = GetColormapIndex(depth);
      cv::Vec3b color = colormap_lut_.at<cv::Vec3b>(colormap_index, 0);
      cv::Scalar point_color(color[0], color[1], color[2]);
      cv::circle(img, image_points[i], 3, point_color, -1);
    }
  }

} // namespace robosense::postprocess
