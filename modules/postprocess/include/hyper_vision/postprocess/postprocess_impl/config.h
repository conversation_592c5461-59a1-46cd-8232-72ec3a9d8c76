/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef HYPER_VISION_POSTPROCESS_CONFIG_H_
#define HYPER_VISION_POSTPROCESS_CONFIG_H_

#include <string>
#include <vector>

#include <yaml-cpp/yaml.h>

namespace robosense {
namespace postprocess {

struct TransformXYZQuat {
  TransformXYZQuat()=default;
  double qx;
  double qy;
  double qz;
  double qw;
  double x;
  double y;
  double z;
};

struct Intrinsics {
  Intrinsics()=default;
  std::vector<double> distortion_coeffs;             // Distortion coefficients
  std::vector<double> camera_matrix;             // Intrinsic matrix values (fx, 0, cx; 0, fy, cy; 0, 0, 1)
  std::string camera_model;          // Type of camera model

  // Constructor to initialize intrinsics with values
  Intrinsics(std::vector<double> d_values, std::vector<double> k_values, std::string model)
    : distortion_coeffs(d_values), camera_matrix(k_values), camera_model(model) {}
};

struct MotionConfig {
  MotionConfig()=default;
  bool motion_correct;                  // Whether to perform motion correction
  bool using_imu_linear_acceleration;   // Whether to use IMU linear acceleration data for motion compensation
  bool using_odom_linear_velocity;      // Whether to use ODOM data as linear velocity compensation, if true, it will override the displacement calculated from IMU linear acceleration
  bool frame_tail;                      // Whether to compensate points to the end time of the point cloud frame
  std::size_t num_drift;                       // The number of IMU data required to calculate drift
  std::string projection_root;         // Path to save data (commented out)
  std::string ori_points_topic;         // The topic name for obtaining RGB from original point cloud + motion-corrected point cloud
  std::string motion_points_topic;         // The topic name for obtaining RGB from original point cloud + motion-corrected point cloud
  std::string motion_rgb_points_topic;         // The topic name for obtaining RGB from original point cloud + motion-corrected point cloud
  std::string ori_rgb_points_topic;         // The topic name for obtaining RGB from original point cloud + motion-corrected point cloud
  std::string ori_img_topic;         // The topic name for obtaining RGB from original point cloud + motion-corrected point cloud
  std::string motion_proj_img_topic;         // The topic name for obtaining RGB from original point cloud + motion-corrected point cloud
};

struct CameraConfig {
  CameraConfig()=default;
  std::string ros_topic;              // ROS topic for the camera
  Intrinsics  intrinsics;               // Intrinsic parameters for the camera
  // Constructor to initialize camera config with values
  CameraConfig(std::string topic, std::string root, Intrinsics intr)
    : ros_topic(topic), intrinsics(intr) {}
};

struct LidarConfig {
  LidarConfig()=default;
  std::string ros_topic;              // ROS topic for the LIDAR
  std::string imu_ros_topic;           // ROS topic for the IMU
  TransformXYZQuat T_Lidar2Cam;    // Transformation from LIDAR to Camera
  TransformXYZQuat T_Lidar2IMU;    // Transformation from LIDAR to IMU
  // Constructor to initialize LIDAR config with values
  LidarConfig(std::string topic, std::string imu_topic)
      : ros_topic(topic), imu_ros_topic(imu_topic) {}
};

struct MotionDriverConfig {
  MotionDriverConfig()=default;
  MotionConfig motion_config;
  CameraConfig camera_config;
  LidarConfig  ldiar_config;
};

MotionDriverConfig parse_config(const YAML::Node& cfg);

} // namespace postprocess
} // namespace robosense
#endif  // HYPER_VISION_POSTPROCESS_CONFIG_H_
