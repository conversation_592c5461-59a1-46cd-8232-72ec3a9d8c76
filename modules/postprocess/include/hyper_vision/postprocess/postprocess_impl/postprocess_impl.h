/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef HYPER_VISION_POSTPROCESS_POSTPROCESS_IMPL_H_
#define HYPER_VISION_POSTPROCESS_POSTPROCESS_IMPL_H_

#include <thread>

#include <pcl/common/transforms.h>
#include <pcl/features/normal_3d.h>
#include <pcl/filters/extract_indices.h>
#include <pcl/kdtree/kdtree_flann.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/segmentation/extract_clusters.h>
#include <pcl/segmentation/sac_segmentation.h>

#include <opencv2/opencv.hpp>

#include "hyper_vision/common/common.h"
#include "hyper_vision/postprocess/message/postprocess_output_msg.h"
#include "hyper_vision/postprocess/postprocess_impl/queue.h"
#include "hyper_vision/postprocess/postprocess_impl/config.h"
#include "hyper_vision/postprocess/postprocess_impl/imu_module.h"


namespace robosense::postprocess
{

struct EIGEN_ALIGN16 PointXYZIRT
{
  PCL_ADD_POINT4D;
  float intensity;
  std::uint16_t ring;
  double timestamp;
  EIGEN_MAKE_ALIGNED_OPERATOR_NEW
};


class PostprocessImpl
{
 public:
  using Ptr = std::shared_ptr<PostprocessImpl>;

  PostprocessImpl() = default;

  ~PostprocessImpl() = default;

  void Init(const YAML::Node& cfg_node);

  void AddData(const std::shared_ptr<common::MotionFrame>& msg_ptr);

  void AddData(const std::shared_ptr<robosense::common::DepthFrame>& msg_ptr);

  void AddData(const std::shared_ptr<robosense::common::ImageFrame>& msg_ptr);

  void Process(const PostprocessOutputMsg::Ptr& msg_ptr);

private:
  static std::string name() { return " PostprocessImpl: "; }

  void InitCalib();

  bool ProcessPointCloud();

  bool ProcessCompressedImage();

  void CreateColormapLUT();

  int GetColormapIndex(float depth);

  void DrawProjImage(cv::Mat& img,
                     const pcl::PointCloud<PointXYZIRT>::Ptr& cm_pts,
                     const Eigen::Matrix4d& transform,
                     std::vector<cv::Point2f>& image_points);

  std::shared_ptr<robosense::common::ImageFrame>
  findNearestCam(double point_stamp);

private:
  MotionDriverConfig motion_cfg_;

  // for color mapping
  float min_z_ = 0;
  float max_z_ = 15.0;
  int lut_size_ = 256;
  cv::Mat colormap_lut_;

  std::shared_ptr<IMUModule> imu_module_;
  std::shared_ptr<IMUModule> imu_image_module_;
  Eigen::Matrix4d T_imu_lidar_ = Eigen::Matrix4d::Identity();
  Eigen::Matrix4d T_lidar_imu_ = Eigen::Matrix4d::Identity();
  Eigen::Matrix4d T_cam_lidar_ = Eigen::Matrix4d::Identity();
  Eigen::Matrix4d T_cam_imu_ = Eigen::Matrix4d::Identity();
  Eigen::Matrix4d T_base_lidar_ = Eigen::Matrix4d::Identity(); // Lidar to base

  cv::Mat distortion_coeffs_;
  cv::Mat camera_intrisic_;

  std::string projection_root_;
  std::string fusion_pcd_root_;

  bool motion_correct_ = false;
  bool using_imu_linear_acceleration_ = false;
  bool using_odom_linear_velocity_ = false;
  bool frame_tail_ = false;
  double threshold_ = 0;
  std::size_t num_drift_ = 0;

  PostprocessOutputMsg::Ptr out_msg_ptr_;

  SyncVector<pcl::PointCloud<PointXYZIRT>::Ptr> point_cloud_free_vec_;
  SyncVector<pcl::PointCloud<PointXYZIRT>::Ptr> mc_point_cloud_vec_;

  SyncQueue<std::shared_ptr<robosense::common::DepthFrame>> point_cloud_queue_{1};
  std::deque<std::shared_ptr<robosense::common::ImageFrame>> cam_queue_;

  std::unique_ptr<std::thread> point_cloud_process_thread_ptr_ = nullptr;
  std::unique_ptr<std::thread> cam_process_thread_ptr_ = nullptr;
};

} // namespace robosense::postprocess


#endif  // HYPER_VISION_POSTPROCESS_POSTPROCESS_IMPL_H_
