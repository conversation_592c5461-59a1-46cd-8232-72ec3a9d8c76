/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef HYPER_VISION_POSTPROCESS_MULTI_SENSOR_POSTPROCESS_H_
#define HYPER_VISION_POSTPROCESS_MULTI_SENSOR_POSTPROCESS_H_

#include "hyper_vision/postprocess/message/postprocess_output_msg.h"
#include "hyper_vision/postprocess/postprocess_impl/postprocess_impl.h"

namespace robosense::postprocess
{
  struct DataState
  {
    bool has_odom = false;
    bool has_depth = false;
    bool has_image = false;

    bool is_ready() const { return has_odom and has_depth and has_image; }

    void reset() { has_odom = has_depth = has_image = false; }
  };

  class MultiSensorPostprocess
  {
  public:
    using Ptr = std::shared_ptr<MultiSensorPostprocess>;

    MultiSensorPostprocess() = default;

    ~MultiSensorPostprocess() { Stop(); }

    int Init(const YAML::Node& cfg_node);

    int Start();

    void Stop();

    void AddData(const std::shared_ptr<robosense::common::MotionFrame>& msg_ptr);

    void AddData(const std::shared_ptr<robosense::common::DepthFrame>& msg_ptr);

    void AddData(const std::shared_ptr<robosense::common::ImageFrame>& msg_ptr);

    void SetCallback(const std::function<void(const PostprocessOutputMsg::Ptr& msg_ptr)>& callback);

  private:
    static std::string name() { return " Postprocess: "; }

    void TryProcess();

  private:
    DataState data_state_;
    std::mutex data_state_mutex_;

    std::mutex output_msg_mtx_;
    std::function<void(const PostprocessOutputMsg::Ptr& msg_ptr)> output_msg_callback_;

    PostprocessImpl::Ptr impl_ptr;
  };

} // namespace robosense::postprocess


#endif  // HYPER_VISION_POSTPROCESS_MULTI_SENSOR_POSTPROCESS_H_
