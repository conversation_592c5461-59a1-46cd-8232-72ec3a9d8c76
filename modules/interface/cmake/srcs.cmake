#========================
# libs
#========================

set(CUR_SRCS "")
set(CUR_INCLUDES "include")

set(CUR_SUB_DIR "")
LIST(APPEND CUR_SUB_DIR include)
LIST(APPEND CUR_SUB_DIR src)

foreach (dir ${CUR_SUB_DIR})
    file(GLOB_RECURSE tmp_srcs ${dir}/*.cpp ${dir}/*.h)
    list(APPEND CUR_SRCS ${tmp_srcs})
endforeach ()

add_library(${CUR_LIB} SHARED
        ${CUR_SRCS}
        )

target_include_directories(${CUR_LIB}
        PUBLIC
        ${CUR_INCLUDES}
        )

# MSVC嵌套依赖会报PCL重定义错误
target_link_libraries(${CUR_LIB}
        PUBLIC
        driver
        front_end
        dataio
#        socket
        )

target_compile_definitions(${CUR_LIB} PRIVATE MODULE_NAME="interface")

# 构建后计算哈希值
add_custom_command(
    TARGET ${CUR_LIB} POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E md5sum $<TARGET_FILE:${CUR_LIB}> > ${CMAKE_BINARY_DIR}/${CUR_LIB}.md5
    COMMENT "Calculating hash for ${CUR_LIB}"
)
