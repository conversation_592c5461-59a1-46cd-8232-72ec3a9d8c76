/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "hyper_vision/interface/interface_impl.h"
#include "project_version/version.h"

namespace robosense {

InterfaceImpl::InterfaceImpl() {
  printVersion();
  config_path_.clear();
}

int InterfaceImpl::Init(const std::string &config_file,
                        const robosense::common::PlayMode play_mode) {
  config_path_ = config_file;
  play_mode_ = play_mode;

  int ret = initInterface();
  if (ret != 0) {
    AERROR << name() << ": Initial Interface Failed: ret = " << ret;
    return -1;
  }

  return 0;
}

int InterfaceImpl::Start() {
  int ret = 0;

  if (postprocess_output_buffer_ptr_) {
    ret = postprocess_output_buffer_ptr_->start();
    if (ret != 0) {
      AERROR << name()
             << ": PostProcess Output Buffer Start() Failed: ret = " << ret;
      return -1;
    } else {
      AINFO << name() << ": PostProcess Output Buffer Start() Successed !";
    }
  } else {
    AERROR << name() << ": PostProcess Output Buffer Is Nullptr";
    return -2;
  }

  if (slam_output_buffer_ptr_) {
    ret = slam_output_buffer_ptr_->start();
    if (ret != 0) {
      AERROR << name() << ": Slam Output Buffer Start() Failed: ret = " << ret;
      return -3;
    } else {
      AINFO << name() << ": Slam Ouput Buffer Start() Successed !";
    }
  } else {
    AERROR << name() << ": Slam Output Buffer Is Nullptr";
    return -4;
  }

  if (imagedepth_output_buffer_ptr_) {
    ret = imagedepth_output_buffer_ptr_->start();
    if (ret != 0) {
      AERROR << name()
             << ": ImageDepth Output Buffer Start() Failed: ret = " << ret;
      return -5;
    } else {
      AINFO << name() << ": ImageDepth Ouput Buffer Start() Successed !";
    }
  } else {
    AERROR << name() << ": ImageDepth Output Buffer Is Nullptr";
    return -6;
  }

  if (skeleton_output_buffer_ptr_) {
    ret = skeleton_output_buffer_ptr_->start();
    if (ret != 0) {
      AERROR << name()
             << ": Skeleton Output Buffer Start() Failed: ret = " << ret;
      return -7;
    } else {
      AINFO << name() << ": Skeleton Ouput Buffer Start() Successed !";
    }
  } else {
    AERROR << name() << ": Skeleton Output Buffer Is Nullptr";
    return -8;
  }

  if (play_mode_ == robosense::common::PlayMode::ONLINE) {
    AINFO << name() << ": play_mode_: ONLINE !";
    if (driver_ptr_) {
      ret = driver_ptr_->Start();
      if (ret != 0) {
        AERROR << name() << ": AC Driver Start Failed: ret = " << ret;
        return -9;
      } else {
        AINFO << name() << ": AC Driver Start Successed !";
      }
    } else {
      AERROR << name() << ": AC Driver is Nullptr !";
      return -10;
    }
  } else {
    AINFO << name() << ": play_mode_: OFFLINE !";
  }

  if (front_end_ptr_) {
    ret = front_end_ptr_->Start();
    if (ret != 0) {
      AERROR << name() << ": Front End Start Failed: ret = " << ret;
      return -11;
    } else {
      AINFO << name() << ": Front End Start Successed !";
    }
  } else {
    AERROR << name() << ": Front End is Nullptr !";
    return -12;
  }

  return 0;
}

int InterfaceImpl::Stop() {
  int ret = 0;
  if (driver_ptr_) {
    ret = driver_ptr_->Stop();
    if (ret != 0) {
      AERROR << name() << ": AC Driver Stop() Failed: ret = " << ret;
      return -1;
    }
    driver_ptr_.reset();
  }
  if (front_end_ptr_) {
    ret = front_end_ptr_->Stop();
    if (ret != 0) {
      AERROR << name() << ": Front End Stop() Failed: ret = " << ret;
      return -2;
    }
    front_end_ptr_.reset();
  }
  if (postprocess_output_buffer_ptr_) {
    ret = postprocess_output_buffer_ptr_->finish();
    if (ret != 0) {
      AERROR << name()
             << ": PostProcess Output Buffer Finish() Failed: ret = " << ret;
      return -3;
    }
    postprocess_output_buffer_ptr_.reset();
  }
  if (slam_output_buffer_ptr_) {
    ret = slam_output_buffer_ptr_->finish();
    if (ret != 0) {
      AERROR << name() << ": Slam Output Buffer Finish() Failed: ret = " << ret;
      return -4;
    }
    slam_output_buffer_ptr_.reset();
  }
  if (imagedepth_output_buffer_ptr_) {
    ret = imagedepth_output_buffer_ptr_->finish();
    if (ret != 0) {
      AERROR << name()
             << ": ImageDepth Output Buffer Finish Failed: ret = " << ret;
      return -5;
    }
    imagedepth_output_buffer_ptr_.reset();
  }
  if (skeleton_output_buffer_ptr_) {
    ret = skeleton_output_buffer_ptr_->finish();
    if (ret != 0) {
      AERROR << name()
             << ": Skeleton Output Buffer Finish Failed: ret = " << ret;
      return -6;
    }
  }
  reader_manager_ptr_.reset();
  writer_manager_ptr_.reset();
  slam_writer_manager_ptr_.reset();

  return 0;
}

void InterfaceImpl::SetCallback(
    const front_end::FrontEnd::FRONT_END_POSTPROCESS_OUTPUT_CALLBACK
        &callback) {
  if (callback) {
    postprocess_output_cb_ = callback;
  }
}

// 设置SLAM回调函数
void InterfaceImpl::SetCallback(
    const front_end::FrontEnd::FRONT_END_SLAM_OUTPUT_CALLBACK &callback) {
  if (callback) {
    slam_output_cb_ = callback;
  }
}

// 设置图像深度图回调函数
void InterfaceImpl::SetCallback(
    const front_end::FrontEnd::FRONT_END_IMAGE_DEPTH_OUTPUT_CALLBACK
        &callback) {
  if (callback) {
    imagedepth_output_cb_ = callback;
  }
}

// 设置Skeleton回调函数
void InterfaceImpl::SetCallback(
    const front_end::FrontEnd::FRONT_END_SKELETON_OUTPUT_CALLBACK &callback) {
  if (callback) {
    skeleton_output_cb_ = callback;
  }
}

const std::set<std::string> InterfaceImpl::GetDevices() {
  std::lock_guard<std::mutex> lg(driver_ptr_mtx_);
  if (driver_ptr_) {
    return driver_ptr_->GetDeviceUuids();
  } else {
    AERROR << name() << ": AC Driver Is Nullptr !";
    return std::set<std::string>();
  }
}

int InterfaceImpl::OperatorDevice(
    const robosense::common::DeviceOperator_t &device_operator) {
  std::lock_guard<std::mutex> lg(driver_ptr_mtx_);
  if (driver_ptr_) {
    return driver_ptr_->OperatorDevice(device_operator);
  } else {
    AERROR << name() << ": AC Driver Is Nullptr !";
    return -1;
  }
}

int InterfaceImpl::OperatorDeviceOta(
    const robosense::common::DeviceOtaOperator_t &device_ota_operator,
    bool &ota_result_value) {
  std::lock_guard<std::mutex> lg(driver_ptr_mtx_);
  if (driver_ptr_) {
    return driver_ptr_->OperatorDeviceOta(device_ota_operator,
                                          ota_result_value);
  } else {
    AERROR << name() << ": AC Driver Is Nullptr !";
    return -1;
  }
}

int InterfaceImpl::QueryDeviceInfo(const std::string &device_uuid,
                                   std::string &device_info) {
  std::lock_guard<std::mutex> lg(driver_ptr_mtx_);
  if (driver_ptr_) {
    return driver_ptr_->QueryDeviceInfo(device_uuid, device_info);
  } else {
    AERROR << name() << ": AC Driver Is Nullptr !";
    return -1;
  }
}

int InterfaceImpl::StartDataWrite(
    const robosense::io::DataWriterConfig &dataWriterConfig) {
  std::lock_guard<std::mutex> lg(writer_manager_mtx_);
  if (writer_manager_ptr_) {
    return writer_manager_ptr_->start(dataWriterConfig);
  } else {
    AERROR << name() << ": Data Writer Manager Is Nullptr !";
    return -1;
  }
}

int InterfaceImpl::StopDataWrite() {
  std::lock_guard<std::mutex> lg(writer_manager_mtx_);
  if (writer_manager_ptr_) {
    return writer_manager_ptr_->stop();
  } else {
    AERROR << name() << ": Data Writer Manager Is Nullptr !";
    return -1;
  }
}

int InterfaceImpl::CheckDataWriteIsStart() {
  std::lock_guard<std::mutex> lg(writer_manager_mtx_);
  if (writer_manager_ptr_) {
    return writer_manager_ptr_->checkIsStart();
  } else {
    AERROR << name() << ": Data Writer Manager Is Nullptr !";
    return -1;
  }
}

int InterfaceImpl::GetDataTopicInfoRos(
    const std::string &rosbagFilePath,
    std::map<std::string, std::string> &topic_types,
    std::pair<uint64_t, uint64_t> &rosbagTimepoints) {
  topic_types.clear();
  return robosense::io::DataTopicInfoUtil::getDataTopicInfoRos(
      rosbagFilePath, topic_types, rosbagTimepoints);
}

int InterfaceImpl::GetDataTopicInfoRos2(
    const std::string &db3DirFilePath,
    std::map<std::string, std::string> &topic_types,
    std::pair<uint64_t, uint64_t> &rosbagTimepoints) {
  topic_types.clear();
  return robosense::io::DataTopicInfoUtil::getDataTopicInfoRos2(
      db3DirFilePath, topic_types, rosbagTimepoints);
}

int InterfaceImpl::GetSupportTopicNameInfos(
    std::map<std::string, io::DataTopicNameInfoItem> &infos) {
  infos.clear();
  io::DataTopicNameSupportUtil::getAllSupportTopicNameInfos(infos);
  return 0;
}

int InterfaceImpl::StartDataRead(
    const robosense::io::DataReaderConfig &dataReaderConfig) {
  // 如果启动了，则关闭
  {
    std::lock_guard<std::mutex> lg(reader_manager_mtx_);
    if (reader_manager_ptr_) {
      int ret = reader_manager_ptr_->stop();
      if (ret != 0) {
        AERROR << name()
               << ": Data Reader Manager Stop() Failed: ret = " << ret;
        return -1;
      }
      reader_manager_ptr_.reset();
    }
  }

  // 创建新的DataReaderManager
  robosense::io::DataReaderManager::Ptr reader_manager_ptr;
  try {
    reader_manager_ptr.reset(new robosense::io::DataReaderManager());
  } catch (...) {
    AERROR << name() << ": Malloc robosense::io::DataReaderManager Failed !";
    return -2;
  }

  reader_manager_ptr->registerCallback(
      std::bind(&InterfaceImpl::runLocalDepthFrameCb, this,
                std::placeholders::_1, std::placeholders::_2));
  reader_manager_ptr->registerCallback(
      std::bind(&InterfaceImpl::runLocalMotionFrameCb, this,
                std::placeholders::_1, std::placeholders::_2));
  reader_manager_ptr->registerCallback(
      std::bind(&InterfaceImpl::runLocalImageFrameCb, this,
                std::placeholders::_1, std::placeholders::_2));
  reader_manager_ptr->registerCallback(
      std::bind(&InterfaceImpl::runLocalPointCloudXYZRGBIRTCb, this,
                std::placeholders::_1, std::placeholders::_2));
  reader_manager_ptr->registerCallback(
      std::bind(&InterfaceImpl::runLocalPointCloudXYZIRTCb, this,
                std::placeholders::_1, std::placeholders::_2));

  int ret = reader_manager_ptr->init(dataReaderConfig);
  if (ret != 0) {
    AERROR << name() << ": Initial Reader Manager Failed: ret = " << ret;
    return -3;
  }

  {
    std::lock_guard<std::mutex> lg(reader_manager_mtx_);
    reader_manager_ptr_ = reader_manager_ptr;
  }

  return 0;
}

uint32_t InterfaceImpl::GetInitProgressDataRead() {
  std::lock_guard<std::mutex> lg(reader_manager_mtx_);
  if (reader_manager_ptr_) {
    return reader_manager_ptr_->getInitProgress();
  } else {
    AERROR << name() << ": Data Reader Manager Is Nullptr !";
    return static_cast<uint32_t>(-1);
  }
}

uint32_t InterfaceImpl::GetTotalFrameCount() {
  std::lock_guard<std::mutex> lg(reader_manager_mtx_);
  if (reader_manager_ptr_) {
    return reader_manager_ptr_->getTotalFrameCount();
  } else {
    AERROR << name() << ": Data Reader Manager Is Nullptr !";
    return 0;
  }
}

uint64_t InterfaceImpl::GetTotalDuration() {
  std::lock_guard<std::mutex> lg(reader_manager_mtx_);
  if (reader_manager_ptr_) {
    return reader_manager_ptr_->getTotalDuration();
  } else {
    AERROR << name() << ": Data Reader Manager Is Nullptr !";
    return 0;
  }
}

uint64_t InterfaceImpl::GetEndTimestamp() {
  std::lock_guard<std::mutex> lg(reader_manager_mtx_);
  if (reader_manager_ptr_) {
    return reader_manager_ptr_->getEndTimestamp();
  } else {
    AERROR << name() << ": Data Reader Manager Is Nullptr !";
    return 0;
  }
}

uint64_t InterfaceImpl::GetBeginTimestamp() {
  std::lock_guard<std::mutex> lg(reader_manager_mtx_);
  if (reader_manager_ptr_) {
    return reader_manager_ptr_->getBeginTimestamp();
  } else {
    AERROR << name() << ": Data Reader Manager Is Nullptr !";
    return 0;
  }
}

uint64_t InterfaceImpl::GetCurrentTimestamp() {
  std::lock_guard<std::mutex> lg(reader_manager_mtx_);
  if (reader_manager_ptr_) {
    return reader_manager_ptr_->getCurrentTimestamp();
  } else {
    AERROR << name() << ": Data Reader Manager Is Nullptr !";
    return 0;
  }
}

int32_t InterfaceImpl::GetCurrentFrameIndex() {
  std::lock_guard<std::mutex> lg(reader_manager_mtx_);
  if (reader_manager_ptr_) {
    return reader_manager_ptr_->getCurrentFrameIndex();
  } else {
    AERROR << name() << ": Data Reader Manager Is Nullptr !";
    return -1;
  }
}

bool InterfaceImpl::CheckDataReadIsPlaying() {
  std::lock_guard<std::mutex> lg(reader_manager_mtx_);
  if (reader_manager_ptr_) {
    return reader_manager_ptr_->checkIsPlaying();
  } else {
    AERROR << name() << ": Data Reader Manager Is Nullptr !";
    return false;
  }
}

int InterfaceImpl::DataReadSkipFrame(int skip) {
  std::lock_guard<std::mutex> lg(reader_manager_mtx_);
  if (reader_manager_ptr_) {
    return reader_manager_ptr_->skipFrame(skip);
  } else {
    AERROR << name() << ": Data Reader Manager Is Nullptr !";
    return -1;
  }
}

int InterfaceImpl::DataReadPlay() {
  std::lock_guard<std::mutex> lg(reader_manager_mtx_);
  if (reader_manager_ptr_) {
    return reader_manager_ptr_->play();
  } else {
    AERROR << name() << ": Data Reader Manager Is Nullptr !";
    return -1;
  }
}

int InterfaceImpl::DataReadPause() {
  std::lock_guard<std::mutex> lg(reader_manager_mtx_);
  if (reader_manager_ptr_) {
    return reader_manager_ptr_->pause();
  } else {
    AERROR << name() << ": Data Reader Manager Is Nullptr !";
    return -1;
  }
}

int InterfaceImpl::DataReadStop() {
  std::lock_guard<std::mutex> lg(reader_manager_mtx_);
  if (reader_manager_ptr_) {
    return reader_manager_ptr_->stop();
  } else {
    AERROR << name() << ": Data Reader Manager Is Nullptr !";
    return 0;
  }
}

bool InterfaceImpl::CheckPlayFinish() {
  std::lock_guard<std::mutex> lg(reader_manager_mtx_);
  if (reader_manager_ptr_) {
    return reader_manager_ptr_->checkIsPlayFinish();
  } else {
    AERROR << name() << ": Data Reader Manager Is Nullptr !";
    return true;
  }
}

YAML::Node InterfaceImpl::GetSocketIoConfig() const {
  return cfg_node_["socket_io"];
}

// 获取AC 数据的帧率
int InterfaceImpl::GetACDataFrequence(const std::string &uuid,
                                      float &image_freq, float &depth_freq,
                                      float &imu_freq) {
  std::lock_guard<std::mutex> lg(driver_ptr_mtx_);
  if (driver_ptr_) {
    return driver_ptr_->GetACDataFrequenceInfo(uuid, image_freq, depth_freq,
                                               imu_freq);
  } else {
    AERROR << name() << ": AC Driver Is Nullptr !";
    return -1;
  }
}

// 保存最近的AC数据
int InterfaceImpl::SaveACDataToFile(const std::string &save_dir_path,
                                    const int save_operator_type) {
  std::lock_guard<std::mutex> lg(driver_ptr_mtx_);
  if (driver_ptr_) {
    return driver_ptr_->SaveACDataToFile(save_dir_path, save_operator_type);
  } else {
    AERROR << name() << ": AC Driver Is Nullptr !";
    return -1;
  }
}

// 重置Slam
int InterfaceImpl::ResetSlam() {
  std::lock_guard<std::mutex> lg(front_end_mtx_);
  if (front_end_ptr_) {
    return front_end_ptr_->ResetSlam();
  } else {
    AERROR << name() << ": Front End Is Nullptr !";
    return -1;
  }
}

// 开始保存Slam数据
int InterfaceImpl::StartSlamDataWriter(const std::string &slamDataSaveDirPath) {
  std::lock_guard<std::mutex> lg(slam_writer_manager_mtx_);
  if (slam_writer_manager_ptr_) {
    return slam_writer_manager_ptr_->startWrite(slamDataSaveDirPath);
  } else {
    AERROR << name() << ": Slam Writer Manager Is Nullptr !";
    return -1;
  }
}

// 停止保存Slam数据
int InterfaceImpl::StopSlamDataWriter() {
  std::lock_guard<std::mutex> lg(slam_writer_manager_mtx_);
  if (slam_writer_manager_ptr_) {
    return slam_writer_manager_ptr_->stopWrite();
  } else {
    AERROR << name() << ": Slam Writer Manager Is Nullptr !";
    return -1;
  }
}

// 导出Slam地图
int InterfaceImpl::StartSlamDataExporter(
    const robosense::io::SlamDataExporterConfig &config) {
  // 启动时先检查关闭
  int ret = StopSlamDataExporter();
  if (ret != 0) {
    AERROR << name()
           << ": Stop Slam Data Exporter Manager Failed: ret = " << ret;
    return -1;
  }

  {
    std::lock_guard<std::mutex> lg(slam_exporter_manager_mtx_);
    if (slam_exporter_manager_ptr_ == nullptr) {
      try {
        slam_exporter_manager_ptr_.reset(
            new robosense::io::SlamDataExporterManager());
      } catch (...) {
        AERROR << "Malloc Slam Exporter Manager Failed !";
        return -2;
      }
    }

    int ret = slam_exporter_manager_ptr_->init(config);
    if (ret != 0) {
      AERROR << "Slam Exporter Manager Initial Failed: ret = " << ret;
      return -3;
    }

    ret = slam_exporter_manager_ptr_->start();
    if (ret != 0) {
      AERROR << "Slam Exporter Manager Start Failed: ret = " << ret;
      return -4;
    }
  }

  return 0;
}

// 获取导出Slam地图进度
int InterfaceImpl::GetSlamDataExporterProgress(int32_t &progress) {
  std::lock_guard<std::mutex> lg(slam_exporter_manager_mtx_);
  if (slam_exporter_manager_ptr_) {
    progress = slam_exporter_manager_ptr_->getProgress();
    return 0;
  } else {
    AERROR << name() << ": Slam Exporter Manager Is Nullptr !";
    return -1;
  }
}

// 获取导出Slam地图的结果状态
int InterfaceImpl::CheckSlamDataExporterStatus(bool &status) {
  std::lock_guard<std::mutex> lg(slam_exporter_manager_mtx_);
  if (slam_exporter_manager_ptr_) {
    status = slam_exporter_manager_ptr_->checkIsSuccess();
    return 0;
  } else {
    AERROR << name() << ": Slam Exporter Manager Is Nullptr !";
    return -1;
  }
}

// 停止导出Slam地图
int InterfaceImpl::StopSlamDataExporter() {
  std::lock_guard<std::mutex> lg(slam_exporter_manager_mtx_);
  if (slam_exporter_manager_ptr_) {
    slam_exporter_manager_ptr_->stop();
    slam_exporter_manager_ptr_.reset();
  }
  return 0;
}

// 获取标定文件的序列号
int InterfaceImpl::GetCalibDeviceID(std::string &device_id) {
  std::filesystem::path config_file_path(config_path_);
  // 构造标定文件路径
  std::filesystem::path calibration_file_path =
      config_file_path.parent_path() / "calibration.yaml";

  YAML::Node calibNode;
  try {
    calibNode = YAML::LoadFile(calibration_file_path.string());
  } catch (...) {
    AERROR << name() << ": Load Calibration YAML Node From File: "
           << calibration_file_path.string() << " Failed !";
    return -1;
  }

  try {
    device_id = calibNode["DEVICE_ID"].as<std::string>();
  } catch (...) {
    AERROR
        << name()
        << ": Parse \"DEVICE_ID\" From Calibration YAML Node From Yaml File: "
        << calibration_file_path.string() << " Failed !";
    return -2;
  }

  return 0;
}

// 拷贝标定文件到配置文件路径
int InterfaceImpl::CopyCalibFileToConfigDirectory(
    const std::string &calib_file_path_src) {
  const std::filesystem::path calibFilePathSrc(calib_file_path_src);
  if (!std::filesystem::exists(calibFilePathSrc)) {
    AERROR << "calibFilePathSrc = " << calibFilePathSrc.string()
           << " Not Exists !";
    return -1;
  }

  // 检查是否为Calibration文件
  if (calibFilePathSrc.extension() != RS_CALIBARATION_SUFFIX) {
    AERROR << "calibFilePathSrc = " << calibFilePathSrc.string()
           << " Not Yaml Format File !";
    return -2;
  }

  YAML::Node calibNode;
  try {
    calibNode = YAML::LoadFile(calibFilePathSrc.string());
  } catch (...) {
    AERROR << "Load calibFilePathSrc = " << calibFilePathSrc.string()
           << " Failed !";
    return -3;
  }

  try {
    std::string device_id = calibNode["DEVICE_ID"].as<std::string>();
  } catch (...) {
    AERROR << "Parse \"DEVICE_ID\" From YAML Node Failed !";
    return -4;
  }

  // 标定文件路径
  std::filesystem::path config_file_path(config_path_);
  std::filesystem::path calibFilePathDst =
      config_file_path.parent_path() / "calibration.yaml";
  if (std::filesystem::exists(calibFilePathDst)) {
    try {
      bool isSuccess = std::filesystem::remove(calibFilePathDst);
      if (!isSuccess) {
        AERROR << "(A)Remove Exist Calib File: " << calibFilePathDst.string()
               << " Failed !";
        return -5;
      }
    } catch (...) {
      AERROR << "(B)Remove Exist Calib File: " << calibFilePathDst.string()
             << " Failed !";
      return -5;
    }
  }

  // 覆盖式拷贝
  try {
    bool isSuccess = std::filesystem::copy_file(
        calibFilePathSrc, calibFilePathDst,
        std::filesystem::copy_options::overwrite_existing);
    if (!isSuccess) {
      AERROR << "(A)Copy File: " << calibFilePathSrc.string()
             << " To File: " << calibFilePathDst.string() << " Failed !";
      return -6;
    }
  } catch (...) {
    AERROR << "(B)Copy File: " << calibFilePathSrc.string()
           << " To File: " << calibFilePathDst.string() << " Failed !";
    return -6;
  }

  return 0;
}

// 从硬件导出文件
int InterfaceImpl::ExportCalibFileFromHardware(
    const std::string &device_uuid, const std::string &export_calib_dir_path) {
  std::lock_guard<std::mutex> lg(driver_ptr_mtx_);
  if (driver_ptr_) {
    return driver_ptr_->ExportCalibFileFromHardware(device_uuid,
                                                    export_calib_dir_path);
  } else {
    AERROR << name() << ": AC Driver Is Nullptr !";
    return -1;
  }
}

// 标定信息写到硬件
int InterfaceImpl::WriteCalibFileToHardware(const std::string &device_uuid) {
  std::lock_guard<std::mutex> lg(driver_ptr_mtx_);
  if (driver_ptr_) {
    std::filesystem::path config_file_path(config_path_);
    // 构造标定文件路径
    std::filesystem::path calibration_file_path =
        config_file_path.parent_path() / "calibration.yaml";
    const std::string calib_file_path = calibration_file_path.string();
    return driver_ptr_->WriteCalibFileToHardware(device_uuid, calib_file_path);
  } else {
    AERROR << name() << ": AC Driver Is Nullptr !";
    return -1;
  }
}

// 启用PostProcess模块
int InterfaceImpl::SetEnablePostProcess(const bool is_enable) {
    std::lock_guard<std::mutex> lg(front_end_mtx_);
  if (front_end_ptr_) {
    return front_end_ptr_->SetEnablePostProcess(is_enable);
  } else {
    AERROR << name() << ": Front End Is Nullptr !";
    return -1;
  }
}

// 启用Slam模块
int InterfaceImpl::SetEnableSlam(const bool is_enable) {
  std::lock_guard<std::mutex> lg(front_end_mtx_);
  if (front_end_ptr_) {
    return front_end_ptr_->SetEnableSlam(is_enable);
  } else {
    AERROR << name() << ": Front End Is Nullptr !";
    return -1;
  }
}

// 设置Relocalization状态
int InterfaceImpl::SetEnableRelocalization(const bool is_enable) {
  std::lock_guard<std::mutex> lg(front_end_mtx_);
  if (front_end_ptr_) {
    return front_end_ptr_->EnableSlamRelocalization(is_enable);
  } else {
    AERROR << name() << ": Front End Is Nullptr !";
    return -1;
  }
}

// 设置是否进行离线优化
int InterfaceImpl::SetEnableSlamOfflineOpt(const bool is_enable) {
  std::lock_guard<std::mutex> lg(front_end_mtx_);
  if (front_end_ptr_) {
    return front_end_ptr_->EnableSlamOfflineOpt(is_enable);
  } else {
    AERROR << name() << ": Front End Is Nullptr !";
    return -1;
  }
}

// 设置是否启用ImageDepth模块
int InterfaceImpl::SetEnableImageDepth(const bool is_enable) {
  std::lock_guard<std::mutex> lg(front_end_mtx_);
  if (front_end_ptr_) {
    return front_end_ptr_->SetEnableImageDepth(is_enable);
  } else {
    AERROR << name() << ": Front End Is Nullptr !";
    return -1;
  }
}

// 启用Skeleton模块
int InterfaceImpl::SetEnableSkeleton(const bool is_enable) {
  std::lock_guard<std::mutex> lg(front_end_mtx_);
  if (front_end_ptr_) {
    return front_end_ptr_->SetEnableSkeleton(is_enable);
  } else {
    AERROR << name() << ": Front End Is Nullptr !";
    return -1;
  }
}

int InterfaceImpl::initInterface() {
  int ret = 0;
  ret = initConfigNode();
  if (ret != 0) {
    AERROR << name() << ": Initial Config Node Failed: ret = " << ret;
    return -1;
  }

  ret = initROS2Environment();
  if (ret != 0) {
    AERROR << name() << ": Initial ROS2 Environment Failed: ret = " << ret;
    return -2;
  }

  ret = initDataIoWriter();
  if (ret != 0) {
    AERROR << name() << ": Initial Data Io Writer Failed: ret = " << ret;
    return -3;
  }

  ret = initSlamDataWriter();
  if (ret != 0) {
    AERROR << name() << ": Initial Slam Data Writer Failed: ret = " << ret;
    return -4;
  }

  ret = initMessageBuffer();
  if (ret != 0) {
    AERROR << name() << ": Initial Message Buffer Failed: ret = " << ret;
    return -5;
  }

  ret = initFrontEnd();
  if (ret != 0) {
    AERROR << name() << ": Initial FronEnd Failed: ret = " << ret;
    return -6;
  }

  switch (play_mode_) {
  case robosense::common::PlayMode::ONLINE: {
    AINFO << name() << ": play_mode_: ONLINE !";
    ret = initSuperSensorDriver();
    if (ret != 0) {
      AERROR << name() << ": Initial Super Sensor Driver Failed: ret = " << ret;
      return -7;
    }
    break;
  }
  case robosense::common::PlayMode::OFFLINE: {
    AINFO << name() << ": play_mode_: OFFLINE !";
    break;
  }
  }

  return 0;
}

void InterfaceImpl::printVersion() {
  std::string version = PROJECT_VERSION;
  std::string commit_id = GIT_COMMIT_HASH;
  AINFO << "PROJECT_VERSION: " << version << ", GIT_COMMIT_HASH: " << commit_id;
}

int InterfaceImpl::initConfigNode() {
  // 初始化配置
  Utils::init(
      config_path_,
      robosense::io::Ros2EnvironmentUtil::getAppSettingRuntimeDirPath());

  // 获取配置
  ConfigureManager::getInstance().setConfigFile(config_path_);
  cfg_node_ = ConfigureManager::getInstance().getCfgNode();

  // 更新配置
  int ret = 0;
  ret = updateSocketIoConfigNode();
  if (ret != 0) {
    AERROR << name() << ": Update Socket Io Config Node Failed: ret = " << ret;
    return -1;
  }

  ret = updateSlamConfigNode();
  if (ret != 0) {
    AERROR << name() << ": Update Slam Config Node Failed: ret = " << ret;
    return -2;
  }

  return 0;
}

int InterfaceImpl::updateSocketIoConfigNode() {
  // Socket_IO
  YAML::Node socket_io_cfg;
  Utils::yamlSubNode(cfg_node_, "socket_io", socket_io_cfg);
  // std::cout << "socket_io_cfg = " << socket_io_cfg << std::endl;
  std::filesystem::path config_file_path(config_path_);
  const auto &parent_config_file_path = config_file_path.parent_path();
  const std::string socket_io_rel_path =
      socket_io_cfg["socket"]["socket_io_config_rel_path"].as<std::string>();
  const std::string socket_config_path =
      (parent_config_file_path / socket_io_rel_path).string();
  socket_io_cfg["socket"]["socket_io_config_path"] = socket_config_path;
#if defined(WIN32) || defined(_WIN32) || defined(__WIN32__)
  socket_io_cfg["socket"]["default_write_dir_path"]["system"] =
      socket_io_cfg["socket"]["default_write_dir_path"]["windows"]
          .as<std::string>();
#elif defined(linux) || defined(__linux) || defined(__linux__)
  socket_io_cfg["socket"]["default_write_dir_path"]["system"] =
      socket_io_cfg["socket"]["default_write_dir_path"]["linux"]
          .as<std::string>();
#elif defined(macintosh) || defined(__APPLE__) || defined(__APPLE_CC__)
  socket_io_cfg["socket"]["default_write_dir_path"]["system"] =
      socket_io_cfg["socket"]["default_write_dir_path"]["mac"]
          .as<std::string>();
#else
  throw std::runtime_error("not support system type !");
#endif //

  cfg_node_["socket_io"] = socket_io_cfg;

  return 0;
}

int InterfaceImpl::updateSlamConfigNode() {
  YAML::Node slam_cfg;
  Utils::yamlSubNode(cfg_node_, "slam", slam_cfg);

  std::filesystem::path config_file_path(config_path_);
  const auto &parent_config_file_path = config_file_path.parent_path();
  slam_cfg["config_root_path"] = parent_config_file_path.string();

  cfg_node_["slam"] = slam_cfg;

  return 0;
}

int InterfaceImpl::initROS2Environment() {
  YAML::Node data_io_node;
  Utils::yamlSubNode(cfg_node_, "data_io", data_io_node);
  std::string ament_rel_path;
#if defined(WIN32) || defined(_WIN32) || defined(__WIN32__)
  ament_rel_path =
      data_io_node["ros2"]["ament_rel_path"]["windows"].as<std::string>();
#elif defined(linux) || defined(__linux) || defined(__linux__)
  ament_rel_path =
      data_io_node["ros2"]["ament_rel_path"]["linux"].as<std::string>();
#elif defined(macintosh) || defined(__APPLE__) || defined(__APPLE_CC__)
  ament_rel_path =
      data_io_node["ros2"]["ament_rel_path"]["mac"].as<std::string>();
#else
  throw std::runtime_error("not support system type !");
#endif //
  io::Ros2EnvironmentUtil::initRos2EnvironmentParams(ament_rel_path);
#if defined(ROS2_FOUND)
  io::Ros2EnvironmentUtil::setRos2Environment();
#endif // #if defined(ROS2_FOUND)

  return 0;
}

int InterfaceImpl::initSuperSensorDriver() {
  driver::SuperSensor::Ptr driver_ptr;
  try {
    driver_ptr.reset(new driver::SuperSensor());
  } catch (...) {
    AERROR << name() << ": Malloc AC Driver Failed !";
    return -1;
  }

  // 读取配置文件
  bool isSuccess = true;
  YAML::Node super_sensor_node;
  isSuccess = Utils::yamlSubNode(cfg_node_, "super_sensor", super_sensor_node);
  if (!isSuccess) {
    AERROR << name()
           << ":Get \"super_sensor\" Config YAML Node From cfg_node_ Failed !";
    return -2;
  }

  YAML::Node driver_cfg_node;
  isSuccess = Utils::yamlSubNode(super_sensor_node, "driver", driver_cfg_node);
  if (!isSuccess) {
    AERROR << name()
           << ": Get \"driver\" Config YAML Node From super_sensor Failed !";
    return -3;
  }

  int ret = driver_ptr->Init(driver_cfg_node);
  if (ret != 0) {
    AERROR << name() << ": AC Driver Initial Failed: ret = " << ret;
    return -4;
  }

  {
    auto motion_func = std::bind(&InterfaceImpl::runOnlineLocalMotionFrameCb,
                                 this, std::placeholders::_1);
    driver_ptr->SetCallback(motion_func);

    auto depth_func = std::bind(&InterfaceImpl::runOnlineLocalDepthFrameCb,
                                this, std::placeholders::_1);
    driver_ptr->SetCallback(depth_func);

    auto image_func = std::bind(&InterfaceImpl::runOnlineLocalImageFrameCb,
                                this, std::placeholders::_1);
    driver_ptr->SetCallbackImage(image_func);
  }

  {
    std::lock_guard<std::mutex> lg(driver_ptr_mtx_);
    driver_ptr_ = driver_ptr;
  }

  return 0;
}

int InterfaceImpl::initDataIoWriter() {
  io::DataWriterManager::Ptr writer_manager_ptr;
  try {
    writer_manager_ptr.reset(new io::DataWriterManager());
  } catch (...) {
    AERROR << name() << ": malloc data writer manager failed !";
    return -1;
  }

  int ret = writer_manager_ptr->init();
  if (ret != 0) {
    AERROR << name() << ": data writer manager failed: ret = " << ret;
    return -2;
  }

  {
    std::lock_guard<std::mutex> lg(writer_manager_mtx_);
    writer_manager_ptr_ = writer_manager_ptr;
  }

  return 0;
}

int InterfaceImpl::initSlamDataWriter() {
  io::SlamDataWriterManager::Ptr slam_writer_manager_ptr;
  try {
    slam_writer_manager_ptr.reset(new io::SlamDataWriterManager());
  } catch (...) {
    AERROR << name() << ": malloc slam writer manager failed !";
    return -1;
  }

  int ret = slam_writer_manager_ptr->init();
  if (ret != 0) {
    AERROR << name()
           << ": slam data writer manager initial failed: ret = " << ret;
    return -2;
  }
  ret = slam_writer_manager_ptr->start();
  if (ret != 0) {
    AERROR << name()
           << ": slam data writer manager start failed: ret = " << ret;
    return -3;
  }

  {
    std::lock_guard<std::mutex> lg(slam_writer_manager_mtx_);
    slam_writer_manager_ptr_ = slam_writer_manager_ptr;
  }

  return 0;
}

int InterfaceImpl::initFrontEnd() {
  front_end::FrontEnd::Ptr front_end_ptr;
  try {
    front_end_ptr.reset(new front_end::FrontEnd());
  } catch (...) {
    AERROR << name() << ": malloc front end failed !";
    return -1;
  }

  int ret = front_end_ptr->Init(cfg_node_);
  if (ret != 0) {
    AERROR << name() << ": front end initial failed: ret = " << ret;
    return -2;
  }

  // 设置回调函数
  {
    // postprocess
    const auto &postProcessCb =
        std::bind(&InterfaceImpl::runLocalFrontEndPostProcessCb, this,
                  std::placeholders::_1);

    front_end_ptr->SetCallback(postProcessCb);

    // slam
    const auto &slamCb = std::bind(&InterfaceImpl::runLocalFrontEndSlamCb, this,
                                   std::placeholders::_1);

    front_end_ptr->SetCallback(slamCb);

    // imagedepth
    const auto &imageDepthCb =
        std::bind(&InterfaceImpl::runLocalFrontEndImageDepthCb, this,
                  std::placeholders::_1);

    front_end_ptr->SetCallback(imageDepthCb);

    // skeleton
    const auto &skeletonCb =
        std::bind(&InterfaceImpl::runLocalFrontEndSkeletonCb, this,
                  std::placeholders::_1);

    front_end_ptr->SetCallback(skeletonCb);
  }

  {
    std::lock_guard<std::mutex> lg(front_end_mtx_);
    front_end_ptr_ = front_end_ptr;
  }

  return 0;
}

int InterfaceImpl::initMessageBuffer() {
  int ret = 0;
  // postprocess output buffer
  if (postprocess_output_buffer_ptr_) {
    int ret = postprocess_output_buffer_ptr_->finish();
    if (ret != 0) {
      AERROR << name()
             << ": PostProcess Output Buffer Finish() Failed: ret = " << ret;
      return -1;
    }
    postprocess_output_buffer_ptr_.reset();
  }

  if (postprocess_output_buffer_ptr_ == nullptr) {
    try {
      postprocess_output_buffer_ptr_.reset(
          new MessageBuffer<postprocess::PostprocessOutputMsg::Ptr>());
    } catch (...) {
      AERROR << name() << ": Malloc PostProcess Output Buffer Failed !";
      return -2;
    }
  }

  ret = postprocess_output_buffer_ptr_->init(
      postprocess_output_cb_,
      RS_MESSAGE_BUFFER_ACTION_TYPE::RS_MESSAGE_BUFFER_ACTION_LAST_CLEAR, 10);
  if (ret != 0) {
    AERROR << name() << ": Initial PostProcess Output Buffer Failed !";
    return -3;
  }

  // slam output buffer
  if (slam_output_buffer_ptr_) {
    int ret = slam_output_buffer_ptr_->finish();
    if (ret != 0) {
      AERROR << name() << ": Slam Output Buffer Finish() Failed: ret = " << ret;
      return -4;
    }
    slam_output_buffer_ptr_.reset();
  }

  if (slam_output_buffer_ptr_ == nullptr) {
    try {
      slam_output_buffer_ptr_.reset(
          new MessageBuffer<slam::SlamOutputMsg::Ptr>());
    } catch (...) {
      AERROR << name() << ": Malloc Slam Output Buffer Failed !";
      return -5;
    }
  }

  ret = slam_output_buffer_ptr_->init(
      slam_output_cb_,
      RS_MESSAGE_BUFFER_ACTION_TYPE::RS_MESSAGE_BUFFER_ACTION_LAST_CLEAR, 10);
  if (ret != 0) {
    AERROR << name() << ": Initial Slam Output Buffer Failed !";
    return -6;
  }

  // imagedepth output buffer
  if (imagedepth_output_buffer_ptr_) {
    int ret = imagedepth_output_buffer_ptr_->finish();
    if (ret != 0) {
      AERROR << name()
             << ": ImageDepth Output Buffer Finish() Failed: ret = " << ret;
      return -7;
    }
    imagedepth_output_buffer_ptr_.reset();
  }

  if (imagedepth_output_buffer_ptr_ == nullptr) {
    try {
      imagedepth_output_buffer_ptr_.reset(
          new MessageBuffer<imagedepth::ImageDepthOutputMsg::Ptr>());
    } catch (...) {
      AERROR << name() << ": Malloc ImageDepth Output Buffer Failed !";
      return -8;
    }
  }

  ret = imagedepth_output_buffer_ptr_->init(
      imagedepth_output_cb_,
      RS_MESSAGE_BUFFER_ACTION_TYPE::RS_MESSAGE_BUFFER_ACTION_LAST_CLEAR, 10);
  if (ret != 0) {
    AERROR << name() << ": Initial ImageDepth Output Buffer Failed !";
    return -9;
  }

  // skeleton output buffer
  if (skeleton_output_buffer_ptr_) {
    int ret = skeleton_output_buffer_ptr_->finish();
    if (ret != 0) {
      AERROR << name()
             << ": Skeleton Output Buffer Finish() Failed: ret = " << ret;
      return -10;
    }
    skeleton_output_buffer_ptr_.reset();
  }

  if (skeleton_output_buffer_ptr_ == nullptr) {
    try {
      skeleton_output_buffer_ptr_.reset(
          new MessageBuffer<skeleton::SkeletonOutputMsg::Ptr>());
    } catch (...) {
      AERROR << name() << ": Malloc Skeleton Output Buffer Failed !";
      return -11;
    }
  }

  ret = skeleton_output_buffer_ptr_->init(
      skeleton_output_cb_,
      RS_MESSAGE_BUFFER_ACTION_TYPE::RS_MESSAGE_BUFFER_ACTION_LAST_CLEAR, 10);
  if (ret != 0) {
    AERROR << name() << ": Initial Skeleton Output Buffer Failed !";
    return -12;
  }

  return 0;
}

void InterfaceImpl::runOnlineLocalDepthFrameCb(
    const std::shared_ptr<robosense::common::DepthFrame> &msg_ptr) {
  front_end_ptr_->AddData(msg_ptr);
  // ready to write data
  writer_manager_ptr_->addMessage<robosense::common::DepthFrame>(
      io::DataTopicNameSupportUtil::RS_INDEX_TOPICNAME_MAPPER
          [io::RS_SUPPORT_TOPICNAME_INDEX::RS_DATA_TOPICNAME_DEPTH_FRAME],
      msg_ptr);
}

void InterfaceImpl::runOnlineLocalMotionFrameCb(
    const std::shared_ptr<robosense::common::MotionFrame> &msg_ptr) {
  front_end_ptr_->AddData(msg_ptr);
  // ready to write data
  writer_manager_ptr_->addMessage<robosense::common::MotionFrame>(
      io::DataTopicNameSupportUtil::RS_INDEX_TOPICNAME_MAPPER
          [io::RS_SUPPORT_TOPICNAME_INDEX::RS_DATA_TOPICNAME_MOTION_FRAME],
      msg_ptr);
}

void InterfaceImpl::runOnlineLocalImageFrameCb(
    const std::shared_ptr<robosense::common::ImageFrame> &msg_ptr) {
  front_end_ptr_->AddDataImage(msg_ptr);
  // ready to write data
  writer_manager_ptr_->addMessage<robosense::common::ImageFrame>(
      io::DataTopicNameSupportUtil::RS_INDEX_TOPICNAME_MAPPER
          [io::RS_SUPPORT_TOPICNAME_INDEX::RS_DATA_TOPICNAME_IMAGE_FRAME],
      msg_ptr);
}

void InterfaceImpl::runOnlineLocalImageLeftFrameCb(
    const std::shared_ptr<robosense::common::ImageFrame> &msgPtr) {
  (void)(msgPtr);
}

void InterfaceImpl::runOnlineLocalImageRightFrameCb(
    const std::shared_ptr<robosense::common::ImageFrame> &msgPtr) {
  (void)(msgPtr);
}

void InterfaceImpl::runLocalDepthFrameCb(
    const std::string &topicName,
    const std::shared_ptr<robosense::common::DepthFrame> &msgPtr) {
  // if (front_end_ptr_ && front_end::RunMode::POSTPROCESS == run_mode_)
  {
    front_end_ptr_->AddData(msgPtr);
    AINFO << name() << ": Offline: topicName = " << topicName << ", AddData";
  }
}

void InterfaceImpl::runLocalMotionFrameCb(
    const std::string &topicName,
    const std::shared_ptr<robosense::common::MotionFrame> &msgPtr) {
  // if (front_end_ptr_ && front_end::RunMode::POSTPROCESS == run_mode_)
  {
    front_end_ptr_->AddData(msgPtr);
    AINFO << name() << ": Offline: topicName = " << topicName << ", AddData";
  }
}

void InterfaceImpl::runLocalImageFrameCb(
    const std::string &topicName,
    const std::shared_ptr<robosense::common::ImageFrame> &msgPtr) {
  // if (front_end_ptr_ && front_end::RunMode::POSTPROCESS == run_mode_)
  {
    front_end_ptr_->AddDataImage(msgPtr);
    AINFO << name() << ": Offline: topicName = " << topicName << ", AddData";
  }
}

void InterfaceImpl::runLocalPointCloudXYZRGBIRTCb(
    const std::string &topicName,
    const pcl::PointCloud<RsPointXYZRGBIRT>::Ptr &msgPtr) {
  (void)(msgPtr);
  AINFO << name() << ": Offline: topicName = " << topicName;
}

void InterfaceImpl::runLocalPointCloudXYZIRTCb(
    const std::string &topicName,
    const pcl::PointCloud<RsPointXYZIRT>::Ptr &msgPtr) {
  (void)(msgPtr);
  AINFO << name() << ": Offline: topicName = " << topicName;
}

void InterfaceImpl::runLocalFrontEndPostProcessCb(
    const postprocess::PostprocessOutputMsg::Ptr &msgPtr) {
  if (msgPtr) {
    if (postprocess_output_buffer_ptr_) {
      AINFO << name()
            << ": PostProcess Output Message Add PostProcess Output Buffer !";
      postprocess_output_buffer_ptr_->addMessage(msgPtr);
    }
  } else {
    AWARN << name() << ": PostProcess Output Message Is Nullptr !";
  }
}

void InterfaceImpl::runLocalFrontEndSlamCb(
    const slam::SlamOutputMsg::Ptr &msgPtr) {
  if (msgPtr) {
    if (slam_output_buffer_ptr_) {
      AINFO << name() << ": Slam Output Message Add Slam Output Buffer !";
      slam_output_buffer_ptr_->addMessage(msgPtr);
    }
    // 可能需要保存
    if (slam_writer_manager_ptr_) {
      AINFO << name() << ": Slam Output Message Add Slam Writer Manager !";
      slam_writer_manager_ptr_->addData(msgPtr);
    }
  } else {
    AWARN << name() << ": Slam Output Message Is Nullptr !";
  }
}

void InterfaceImpl::runLocalFrontEndImageDepthCb(
    const imagedepth::ImageDepthOutputMsg::Ptr &msgPtr) {
  if (msgPtr) {
    if (imagedepth_output_buffer_ptr_) {
      AINFO << name() << ": ImageDepth Output Message Add Slam Output Buffer !";
      imagedepth_output_buffer_ptr_->addMessage(msgPtr);
    }
  } else {
    AWARN << name() << ": ImageDepth Output Message Is Nullptr !";
  }
}

void InterfaceImpl::runLocalFrontEndSkeletonCb(
    const skeleton::SkeletonOutputMsg::Ptr &msgPtr) {
  if (msgPtr) {
    if (skeleton_output_buffer_ptr_) {
      AINFO << name() << ": Skeleton Output Message Add Slam Output Buffer !";
      skeleton_output_buffer_ptr_->addMessage(msgPtr);
    }
  } else {
    AWARN << name() << ": Skeleton Output Message Is Nullptr !";
  }
}

} // namespace robosense
