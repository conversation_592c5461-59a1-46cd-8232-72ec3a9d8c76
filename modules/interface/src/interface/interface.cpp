/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "hyper_vision/interface/interface.h"
#include "hyper_vision/interface/interface_impl.h"
//#include "hyper_vision/socketmanager/socketmanager.h"
#include "project_version/version.h"

#define USE_SOCKET 0

static robosense::InterfaceImpl::Ptr g_impl_ptr;
// websocket
//static robosense::socket::SocketManager::Ptr g_socket_manager_ptr;
static bool g_is_socket_mode = false;
static bool g_is_reset = false;
static bool g_is_enable_relocalization = false;
static std::vector<TagDeviceEvent> g_device_infos;
static std::vector<TagTConfig> g_writer_support_topics;
static std::vector<TagCString> g_writer_support_formats;
static std::string g_writer_default_dir_path;
static std::recursive_mutex g_mutex_;

enum State : std::uint8_t {
  STATE_UNINITIALIZED = 0,
  STATE_INITIALIZED,
  STATE_RUNNING,
  STATE_SHUTTING_DOWN,
  STATE_SHUTDOWN,
};

namespace {
std::atomic<State> g_state;
}

static State GetState() { return g_state.load(); }

static void SetState(const State &state) { g_state.store(state); }

static int g_play_mode{1};

static std::string g_config_path_;

static bool g_show_position{true};

static bool g_show_pointcloud{true};

static bool g_show_slam_pointcloud{true};

static bool g_show_trianglefacet{false};

static bool g_show_rgbimage{true};

static bool g_show_depth{false};

static bool g_show_left_image{true};

static bool g_show_right_image{true};

static bool g_show_image_depth{true};

static bool g_show_skeleton_3d_points{true};

static bool g_show_skeleton_2d_points_left_image{true};

static bool g_show_skeleton_2d_points_right_image{true};

// 点云投影数据回调函数: 着色点云/投影图像/深度图像(暂时未使用)
void (*g_postprocess_handler)(void *, void *, void *) = nullptr;
// slam数据回调函数: pose/点云/slam状态
void (*g_slam_handler)(void *, void *, void *) = nullptr;
// 图像深度图数据回调函数: left图像/right图像/深度图
void (*g_imagedepth_handler)(void *, void *, void *) = nullptr;
// 图像Skeleton数据回调函数:
void (*g_skeleton_handler)(void *, void *, void *) = nullptr;

// socket manager 的回调函数
#if USE_SOCKET
static int socketManagerCallback(
    const std::shared_ptr<robosense::acviewer_msgs::RSRequest> &reqPtr,
    std::shared_ptr<robosense::acviewer_msgs::RSResponse> &respPtr) {
  if (reqPtr == nullptr) {
    RERROR << ": reqPtr is Nullptr !";
    return -1;
  }
  if (respPtr == nullptr) {
    try {
      respPtr.reset(new robosense::acviewer_msgs::RSResponse);
    } catch (...) {
      RERROR << ": Malloc Response Failed !";
      return -2;
    }
  }

  respPtr->set_cmd_type(reqPtr->cmd_type());
  respPtr->set_request_id(reqPtr->request_id());
  respPtr->set_response_id(UINT64_T_TIMESTAMP_NS);
  switch (reqPtr->cmd_type()) {
  case robosense::acviewer_msgs::RS_CMD_TYPE::RS_HEART_BEAT: {
    respPtr->set_response_code(0);
    respPtr->set_response_info("Heart Beat successed !");
    break;
  }
  case robosense::acviewer_msgs::RS_CMD_TYPE::RS_SET_RUN_MODE: {
    if (reqPtr->has_request_data() == false) {
      RERROR << ": \"request_data\" Not Setting Value !";
      return 1;
    } else if (reqPtr->request_data().has_runmode() == false) {
      RERROR << ": \"request_data\" Not Setting \"runmode\" Value !";
      return 1;
    }

    SetPlayMode(reqPtr->request_data().runmode());

    respPtr->set_response_code(0);
    respPtr->set_response_info("Set Run Mode Successed !");

    break;
  }
  case robosense::acviewer_msgs::RS_CMD_TYPE::RS_SET_SHOW_CONFIG: {
    if (reqPtr->has_request_data() == false) {
      RERROR << ": \"request_data\" Not Setting Value !";
      return 2;
    } else if (reqPtr->request_data().has_show_config_data() == false) {
      RERROR << ": \"request_data\" Not Setting \"show_config_data\" Value !";
      return 2;
    }

    const auto &show_config_data = reqPtr->request_data().show_config_data();

    // SetShowConfig(show_config_data.show_pos(), show_config_data.show_pc(),
    //               show_config_data.show_pc_slam(),
    //               show_config_data.show_tri(), show_config_data.show_rgb(),
    //               show_config_data.show_depth());

    respPtr->set_response_code(0);
    respPtr->set_response_info("Set Show Config Successed !");
    break;
  }
  case robosense::acviewer_msgs::RS_CMD_TYPE::RS_GET_DEVICE_INFO: {
    auto handle = [](void *devices, unsigned int size) {
      g_device_infos.clear();
      for (unsigned int i = 0; i < size; i++) {
        TagDeviceEvent device_event = static_cast<TagDeviceEvent *>(devices)[i];
        g_device_infos.push_back(device_event);
      }
    };

    GetDeviceInfo(handle);

    for (size_t i = 0; i < g_device_infos.size(); ++i) {
      const auto &device_event = g_device_infos[i];

      auto str_size = device_event.uuid_size;
      std::string uuid = std::string(device_event.uuid, str_size);

      robosense::acviewer_msgs::RSDeviceInfo deviceInfo;
      deviceInfo.set_uuid(uuid);
      deviceInfo.set_event_type(1);

      auto pDeviceInfo =
          respPtr->mutable_response_data()->add_device_info_list();
      *pDeviceInfo = deviceInfo;
    }

    respPtr->set_response_code(0);
    respPtr->set_response_info("Get Device Info Successed !");
    break;
  }
  case robosense::acviewer_msgs::RS_CMD_TYPE::RS_OPERATOR_DEVICE: {
    if (reqPtr->has_request_data() == false) {
      RERROR << ": \"request_data\" Not Setting Value !";
      return 3;
    } else if (reqPtr->request_data().has_operator_device_data() == false) {
      RERROR
          << ": \"request_data\" Not Setting \"operator_device_data\" Value !";
      return 3;
    }

    const auto &opDeviceData = reqPtr->request_data().operator_device_data();
    int ret = OperatorDevice(opDeviceData.uuid().c_str(),
                             opDeviceData.operator_type());
    if (ret == 0) {
      respPtr->set_response_code(0);
      respPtr->set_response_info("Operator Device" + opDeviceData.uuid() +
                                 " Successed !");
    } else {
      respPtr->set_response_code(ret);
      respPtr->set_response_info("Operator Device" + opDeviceData.uuid() +
                                 " Failed !");
    }
    break;
  }
  case robosense::acviewer_msgs::RS_CMD_TYPE::RS_GET_WRITER_CONFIG: {
    // 获取话题信息
    auto handle_topic = [](void *tConfigs, unsigned int size) {
      g_writer_support_topics.clear();
      // std::cout << "size = " << size << std::endl;
      for (unsigned int i = 0; i < size; i++) {
        TagTConfig topicConfig = static_cast<TagTConfig *>(tConfigs)[i];
        g_writer_support_topics.push_back(topicConfig);
      }
    };

    // 获取支持的格式信息
    auto handle_format = [](void *tConfigs, unsigned int size) {
      g_writer_support_formats.clear();
      // std::cout << "size = " << size << std::endl;
      for (unsigned int i = 0; i < size; i++) {
        TagCString formatConfig = static_cast<TagCString *>(tConfigs)[i];
        g_writer_support_formats.push_back(formatConfig);
      }
    };

    int ret1 = GetTopicConfig(handle_topic);
    if (ret1 != 0) {
      RERROR << ": Get Topic Config Failed: ret = " << ret1;
    }

    int ret2 = GetFileFormat(handle_format);
    if (ret2 != 0) {
      RERROR << ": Get File Format Failed: ret = " << ret2;
    }

    if (ret1 == 0 && ret2 == 0) {
      auto pWriterConfig =
          respPtr->mutable_response_data()->mutable_writer_config();
      for (size_t i = 0; i < g_writer_support_topics.size(); ++i) {
        const auto &topicConfig = g_writer_support_topics[i];
        auto pTopicConfig = pWriterConfig->add_topic_config_list();
        //
        pTopicConfig->set_topic(
            std::string(topicConfig.topic.data, topicConfig.topic.length));
        //
        pTopicConfig->set_type(
            std::string(topicConfig.type.data, topicConfig.type.length));
        //
        for (size_t j = 0; j < topicConfig.process_types_size; ++j) {
          auto pProcessType = pTopicConfig->add_process_types();
          *pProcessType = std::string(topicConfig.process_types[j].data,
                                      topicConfig.process_types[j].length);
        }
      }

      for (size_t i = 0; i < g_writer_support_formats.size(); ++i) {
        const auto &formatConfig = g_writer_support_formats[i];
        auto pFormat = pWriterConfig->add_file_format();
        *pFormat = std::string(formatConfig.data, formatConfig.length);
      }

      respPtr->set_response_code(0);
      respPtr->set_response_info("Get Writer Config Successed !");
    } else {
      respPtr->set_response_code(-1);
      respPtr->set_response_info(
          "Get Writer Config Failed: ret1 = " + std::to_string(ret1) +
          ", ret2 = " + std::to_string(ret2));
    }

    break;
  }
  case robosense::acviewer_msgs::RS_CMD_TYPE::RS_START_WRITER: {
    if (reqPtr->has_request_data() == false) {
      RERROR << ": \"request_data\" Not Setting Value !";
      return 4;
    } else if (reqPtr->request_data().has_writer_setting() == false) {
      RERROR << ": \"request_data\" Not Setting \"writer_setting\" Value !";
      return 4;
    }

    const auto &writer_setting = reqPtr->request_data().writer_setting();
    std::vector<TagTSetting> tSettings;
    if (writer_setting.topic_setting_list_size() > 0) {
      tSettings.resize(writer_setting.topic_setting_list_size());
      for (int i = 0; i < writer_setting.topic_setting_list_size(); ++i) {
        const auto &topic_setting = writer_setting.topic_setting_list(i);
        TagTSetting &tSetting = tSettings[i];
        // topic
        tSetting.topic.length =
            std::min(topic_setting.topic().size(), sizeof(tSetting.topic.data));
        memcpy(tSetting.topic.data, topic_setting.topic().c_str(),
               tSetting.topic.length);

        // compressedType
        tSetting.compressedType.length =
            std::min(topic_setting.compressed_type().size(),
                     sizeof(tSetting.compressedType.data));
        memcpy(tSetting.compressedType.data,
               topic_setting.compressed_type().c_str(),
               tSetting.compressedType.length);
      }
    }

    // 当file_name没有设置时，则用默认配置值
    std::string file_name = writer_setting.file_name();
    if (file_name.empty()) {
      file_name = g_writer_default_dir_path;
    }

    int ret =
        StartWriter(file_name.c_str(), writer_setting.file_format().c_str(),
                    tSettings.data(), tSettings.size());
    if (ret == 0) {
      respPtr->set_response_code(0);
      respPtr->set_response_info("Start Writer Successed !");
    } else {
      respPtr->set_response_code(ret);
      respPtr->set_response_info("Start Writer Failed !");
    }

    break;
  }
  case robosense::acviewer_msgs::RS_CMD_TYPE::RS_STOP_WRITER: {
    int ret = StopWriter();
    if (ret == 0) {
      respPtr->set_response_code(0);
      respPtr->set_response_info("Stop Writer Successed !");
    } else {
      respPtr->set_response_code(ret);
      respPtr->set_response_info("Stop Writer Failed !");
    }
    break;
  }
  case robosense::acviewer_msgs::RS_CMD_TYPE::RS_GET_FILE_LIST: {
    std::vector<std::string> rosbagBothFilePaths;
    int ret = robosense::io::DataFormatSupportUtil::searchRosbagFilePath(
        g_writer_default_dir_path, true, rosbagBothFilePaths);
    if (ret == 0) {
      // 获取搜索到的文件路径
      for (size_t i = 0; i < rosbagBothFilePaths.size(); ++i) {
        respPtr->mutable_response_data()->add_file_list(rosbagBothFilePaths[i]);
      }

      respPtr->set_response_code(0);
      respPtr->set_response_info("Get File List Successed !");
    } else {
      respPtr->set_response_code(ret);
      respPtr->set_response_info("Get File List Failed !");
    }

    break;
  }
  case robosense::acviewer_msgs::RS_CMD_TYPE::RS_GET_FILE_TOPICS: {
    if (reqPtr->has_request_data() == false) {
      RERROR << ": \"request_data\" Not Setting Value !";
      return 5;
    } else if (reqPtr->request_data().has_topic_file_path() == false) {
      RERROR << ": \"request_data\" Not Setting \"topic_file_path\" Value !";
      return 5;
    }

    const std::string &topic_file_path =
        reqPtr->request_data().topic_file_path();

    std::map<std::string, std::string> topic_types;
    std::pair<uint64_t, uint64_t> dataTimepoints;
    int ret = robosense::io::DataTopicInfoUtil::getDataTopicInfo(
        topic_file_path, topic_types, dataTimepoints);

    if (ret == 0) {
      auto response_data = respPtr->mutable_response_data();
      auto reader_config = response_data->mutable_reader_config();
      for (auto iterMap = topic_types.begin(); iterMap != topic_types.end();
           ++iterMap) {
        reader_config->add_file_topics(iterMap->first);
      }
      reader_config->set_start_timestamp_ns(dataTimepoints.first);
      reader_config->set_end_timestamp_ns(dataTimepoints.second);

      respPtr->set_response_code(0);
      respPtr->set_response_info("Get File Topics Successed !");
    } else {
      respPtr->set_response_code(ret);
      respPtr->set_response_info("Get File Topics Failed !");
    }

    break;
  }
  case robosense::acviewer_msgs::RS_CMD_TYPE::RS_START_READER: {
    if (reqPtr->has_request_data() == false) {
      RERROR << ": \"request_data\" Not Setting Value !";
      return 6;
    } else if (reqPtr->request_data().has_reader_setting() == false) {
      RERROR << ": \"request_data\" Not Setting \"reader_setting\" Value !";
      return 6;
    }

    const auto &reader_setting = reqPtr->request_data().reader_setting();
    std::vector<Tag_CString> tCStrings;
    if (reader_setting.read_topic_names_size() > 0) {
      tCStrings.resize(reader_setting.read_topic_names_size());
      for (int i = 0; i < reader_setting.read_topic_names_size(); ++i) {
        const std::string &read_topic_name = reader_setting.read_topic_names(i);
        Tag_CString &tCString = tCStrings[i];
        tCString.length =
            std::min(read_topic_name.size(), sizeof(tCString.data));
        memcpy(tCString.data, read_topic_name.c_str(), tCString.length);
      }
    }

    int ret =
        StartReader(reader_setting.file_name().c_str(),
                    reader_setting.main_sync_topic().c_str(),
                    reader_setting.pre_load_mains_sync_cnt(),
                    (tCStrings.empty() ? nullptr : tCStrings.data()),
                    tCStrings.size(), reader_setting.read_start_timestamp_ns(),
                    reader_setting.read_end_timestamp_ns());
    if (ret == 0) {
      respPtr->set_response_code(0);
      respPtr->set_response_info("Start Reader Successed !");
    } else {
      respPtr->set_response_code(ret);
      respPtr->set_response_info("Start Reader Failed !");
    }

    break;
  }
  case robosense::acviewer_msgs::RS_CMD_TYPE::RS_LOADING_PROGRESS: {
    unsigned int progress = GetInitiProgress();
    respPtr->mutable_response_data()->set_loading_progress(progress);

    respPtr->set_response_code(0);
    respPtr->set_response_info("Loading Progerss Successed !");

    break;
  }
  case robosense::acviewer_msgs::RS_CMD_TYPE::RS_GET_READER_PROGRESS: {

    uint64_t timestamp = GetCurrentTimestamp();
    int32_t index = GetCurrentFrameIndex();
    bool isPlaying = CheckIsPlaying();
    uint32_t totalIndex = GetTotalFrameCount();

    auto pReaderProgress =
        respPtr->mutable_response_data()->mutable_reader_progress();

    pReaderProgress->set_current_frame_index(index);
    pReaderProgress->set_current_timestamp(timestamp);
    pReaderProgress->set_is_playing(isPlaying);
    pReaderProgress->set_total_frame_count(totalIndex);

    respPtr->set_response_code(0);
    respPtr->set_response_info("Get Reader Progress Successed !");

    break;
  }
  case robosense::acviewer_msgs::RS_CMD_TYPE::RS_SKIP_FRAME: {
    if (reqPtr->has_request_data() == false) {
      RERROR << ": \"request_data\" Not Setting Value !";
      return 7;
    } else if (reqPtr->request_data().has_skip_frame() == false) {
      RERROR << ": \"request_data\" Not Setting \"skip_frame\" Value !";
      return 7;
    }

    int ret = SkipFrame(reqPtr->request_data().skip_frame());
    if (ret == 0) {
      respPtr->set_response_code(0);
      respPtr->set_response_info("Skip Frame Successed !");
    } else {
      respPtr->set_response_code(ret);
      respPtr->set_response_info("Skip Frame Failed !");
    }

    break;
  }
  case robosense::acviewer_msgs::RS_CMD_TYPE::RS_PLAY: {
    int ret = Play();
    if (ret == 0) {
      respPtr->set_response_code(0);
      respPtr->set_response_info("Play Successed !");
    } else {
      respPtr->set_response_code(ret);
      respPtr->set_response_info("Play Failed !");
    }
    break;
  }
  case robosense::acviewer_msgs::RS_CMD_TYPE::RS_PAUSE: {
    int ret = Pause();
    if (ret == 0) {
      respPtr->set_response_code(0);
      respPtr->set_response_info("Pause Successed !");
    } else {
      respPtr->set_response_code(ret);
      respPtr->set_response_info("Pause Failed !");
    }
    break;
  }
  case robosense::acviewer_msgs::RS_CMD_TYPE::RS_STOP_READER: {
    int ret = StopReader();
    if (ret == 0) {
      respPtr->set_response_code(0);
      respPtr->set_response_info("StopReader Successed !");
    } else {
      respPtr->set_response_code(ret);
      respPtr->set_response_info("StopReader Failed !");
    }
    break;
  }
  }

  return 0;
}
#endif

// 界面的回调函数
static void guiPostProcessCallback(
    const robosense::postprocess::PostprocessOutputMsg::Ptr &msgPtr) {
  if (msgPtr) {
    auto cloud_msg = msgPtr->point_cloud.ToOutPointCloud();
    auto rgb_msg = msgPtr->rgb_image.ToOutRgbImage();
    auto depth_msg = msgPtr->depth_image.ToOutDepthImage();
    void *cloud_msg_ptr = nullptr;
    void *rgb_msg_ptr = nullptr;
    void *depth_msg_ptr = nullptr;
    if (g_show_pointcloud) {
      cloud_msg_ptr = &cloud_msg;
    }
    if (g_show_rgbimage) {
      rgb_msg_ptr = &rgb_msg;
    }
    if (g_show_depth) {
      depth_msg_ptr = &depth_msg;
    }
    if (cloud_msg.size == 0) {
      cloud_msg_ptr = nullptr;
    }
    if (rgb_msg.width == 0 && rgb_msg.height == 0) {
      rgb_msg_ptr = nullptr;
    }
    if (g_postprocess_handler) {
      g_postprocess_handler(cloud_msg_ptr, rgb_msg_ptr, depth_msg_ptr);
    }

    // SocketIo
    // if (g_socket_manager_ptr != nullptr) {
    //   g_socket_manager_ptr->sendRenderData(
    //       static_cast<TagPosition *>(pos_msg_ptr),
    //       static_cast<TagPointCloud *>(cloud_msg_ptr),
    //       static_cast<TagPointCloud *>(slam_cloud_msg_ptr),
    //       static_cast<TagTriangleFacet *>(triangle_msg_ptr),
    //       static_cast<TagRgbImage *>(rgb_msg_ptr),
    //       static_cast<TagDepthImage *>(depth_msg_ptr));
    // }
  }
}

static void
guiSlamMsgCallback(const robosense::slam::SlamOutputMsg::Ptr &msgPtr) {
  if (msgPtr) {
    auto slam_cloud_msg = msgPtr->slam_point_cloud.ToOutPointCloud();
    auto slam_status_msg = msgPtr->slam_status.ToOutSlamStatus();
    auto slam_odom_msg = msgPtr->odom.ToPosition();
    void *slam_cloud_msg_ptr = nullptr;
    void *slam_status_msg_ptr = nullptr;
    void *slam_odom_msg_ptr = nullptr;
    if (g_show_slam_pointcloud) {
      slam_cloud_msg_ptr = &slam_cloud_msg;
    }
    slam_status_msg_ptr = &slam_status_msg;
    if (g_show_position) {
      slam_odom_msg_ptr = &slam_odom_msg;
    }
    if (g_slam_handler) {
      g_slam_handler(slam_odom_msg_ptr, slam_cloud_msg_ptr,
                     slam_status_msg_ptr);
    }
  }
}

static void guiImageDepthCallback(
    const robosense::imagedepth::ImageDepthOutputMsg::Ptr &msgPtr) {
  if (msgPtr) {
    auto left_image_msg = msgPtr->left_rgb_image.ToOutRgbImage();
    auto right_image_msg = msgPtr->right_rgb_image.ToOutRgbImage();
    auto depth_image_msg = msgPtr->image_depth.ToOutDepthImage();

    void *left_image_msg_ptr = nullptr;
    void *right_image_msg_ptr = nullptr;
    void *depth_image_msg_ptr = nullptr;
    if (g_show_left_image) {
      left_image_msg_ptr = &left_image_msg;
    }
    if (g_show_right_image) {
      right_image_msg_ptr = &right_image_msg;
    }
    if (g_show_image_depth) {
      depth_image_msg_ptr = &depth_image_msg;
    }

    if (g_imagedepth_handler) {
      g_imagedepth_handler(left_image_msg_ptr, right_image_msg_ptr,
                           depth_image_msg_ptr);
    }
  }
}

static void
guiSkeletonCallback(const robosense::skeleton::SkeletonOutputMsg::Ptr &msgPtr) {
  if (msgPtr) {
    auto skeleton_points = msgPtr->points.ToOutSkeletonPoint();
    auto left_image = msgPtr->left_image.ToOutRgbImage();
    auto right_image = msgPtr->right_image.ToOutRgbImage();

    void *point3d_msg_ptr = nullptr;
    void *point2d_left_image_msg_ptr = nullptr;
    void *point2d_right_image_msg_ptr = nullptr;

    if (g_show_skeleton_3d_points) {
      point3d_msg_ptr = &skeleton_points;
    }

    if (g_show_skeleton_2d_points_left_image) {
      point2d_left_image_msg_ptr = &left_image;
    }

    if (g_show_skeleton_2d_points_right_image) {
      point2d_right_image_msg_ptr = &right_image;
    }

    if (g_skeleton_handler) {
      g_skeleton_handler(point3d_msg_ptr, point2d_left_image_msg_ptr,
                         point2d_right_image_msg_ptr);
    }
  }
}

EXPORT_API int OnInitialize(const char *config_file, const bool isSocketMode) {
  g_config_path_ = config_file;
  // 默认为在线模式
  robosense::common::PlayMode play_mode = robosense::common::PlayMode::ONLINE;
  // 初始化Interface Impl
  robosense::InterfaceImpl::Ptr impl_ptr;
  try {
    impl_ptr.reset(new robosense::InterfaceImpl());
  } catch (...) {
    RERROR << "Malloc robosense::InterfaceImpl Failed !";
    return -1;
  }
  // register callback(s)
  impl_ptr->SetCallback(guiPostProcessCallback);
  impl_ptr->SetCallback(guiSlamMsgCallback);
  impl_ptr->SetCallback(guiImageDepthCallback);
  impl_ptr->SetCallback(guiSkeletonCallback);

  int ret = impl_ptr->Init(config_file, play_mode);
  if (ret != 0) {
    RERROR << "Interface: Interface Impl Initial Failed: ret = " << ret;
    return -2;
  }

  {
    std::lock_guard<std::recursive_mutex> lg(g_mutex_);
    g_impl_ptr = impl_ptr;
  }

  // socket manager
#if USE_SOCKET
  g_is_socket_mode = isSocketMode;
  if (g_is_socket_mode && g_socket_manager_ptr == nullptr) {
    const auto socket_io_config_node = g_impl_ptr->GetSocketIoConfig();

    robosense::socket::SocketManager::Ptr socket_manager_ptr;
    try {
      socket_manager_ptr.reset(new robosense::socket::SocketManager);
    } catch (...) {
      RERROR << "Interface: Malloc robosense::socket::SocketManager Failed !";
      return -3;
    }

    int ret = socket_manager_ptr->init(
        socket_io_config_node["socket"]["socket_io_config_path"]
            .as<std::string>(),
        std::bind(socketManagerCallback, std::placeholders::_1,
                  std::placeholders::_2));
    if (ret != 0) {
      RERROR << "Interface: Initial Socket Manager Failed: ret = " << ret;
      return -2;
    }

    { g_socket_manager_ptr = socket_manager_ptr; }

    g_writer_default_dir_path =
        socket_io_config_node["socket"]["default_write_dir_path"]["system"]
            .as<std::string>();
  }
#endif
  // 更新状态
  SetState(State::STATE_INITIALIZED);

  return 0;
}

EXPORT_API int Reset() {
  g_is_reset = true;
  int ret = OnDestroy();
  if (ret != 0) {
    RERROR << "Interface: OnDestory Failed: ret = " << ret;
    return -1;
  }

  ret = OnInitialize(g_config_path_.c_str(), g_is_socket_mode);
  if (ret != 0) {
    RERROR << "Interface: OnInitialize Failed: ret = " << ret;
    return -2;
  }

  ret = OnStart();
  if (ret != 0) {
    RERROR << "Interface: OnStart Failed: ret = " << ret;
    return -3;
  }
  g_is_reset = false;

  return 0;
}

EXPORT_API int OnStart() {
  std::unique_lock<std::recursive_mutex> lock(g_mutex_);
  int ret = g_impl_ptr->Start();
  if (ret != 0) {
    RERROR << "Interface: Interface Impl Start() Failed: ret = " << ret;
    return -1;
  }
#if USE_SOCKET
  if (g_socket_manager_ptr) {
    ret = g_socket_manager_ptr->start();
    if (ret != 0) {
      RERROR << "Interface: Socket Manager Start Failed: ret = " << ret;
      return -2;
    }
  }
#endif
  SetState(State::STATE_RUNNING);
  return 0;
}

EXPORT_API int OnDestroy() {
  std::unique_lock<std::recursive_mutex> lock(g_mutex_);
  if (g_impl_ptr) {
    int ret = g_impl_ptr->Stop();
    if (ret != 0) {
      RERROR << "Interface: Interface Impl Stop() Failed: ret = " << ret;
      return -1;
    }
    g_impl_ptr.reset();
  }
#if USE_SOCKET
  if (g_socket_manager_ptr != nullptr && !g_is_reset) {
    int ret = g_socket_manager_ptr->stop();
    if (ret != 0) {
      RERROR << "Interface: Socket Manager Stop Failed: ret = " << ret;
      return -2;
    }
    g_socket_manager_ptr.reset();
  }
#endif
  return 0;
}

EXPORT_API void GetVersionInfo(void (*handler)(void *)) {
  std::string version = PROJECT_VERSION;
  std::string commit_id = GIT_COMMIT_HASH;
  RINFO << "GetVersionInfo PROJECT_VERSION: " << version
        << ", GIT_COMMIT_HASH: " << commit_id;
  if (handler == nullptr) {
    RWARN << "Interface: Input handler is Nullptr !";
    return;
  }
  TagTVersion version_info;
  version_info.version.length = version.length();
  memcpy(version_info.version.data, version.c_str(), version.length());
  version_info.commit_id.length = commit_id.length();
  memcpy(version_info.commit_id.data, commit_id.c_str(), commit_id.length());
  handler(&version_info);
}

EXPORT_API void SetPostProcessMsgHandler(void (*handler)(void *, void *,
                                                         void *)) {
  g_postprocess_handler = handler;
}

EXPORT_API void SetSlamMsgHandler(void (*handler)(void *, void *, void *)) {
  g_slam_handler = handler;
}

EXPORT_API void SetImageDepthMsgHandler(void (*handler)(void *, void *,
                                                        void *)) {
  g_imagedepth_handler = handler;
}

EXPORT_API void SetSkeletonMsgHandler(void (*handler)(void *, void *, void *)) {
  g_skeleton_handler = handler;
}

EXPORT_API int SetPlayMode(int play_mode) {
  std::unique_lock<std::recursive_mutex> lock(g_mutex_);
  if (play_mode <= 0 || play_mode > 2) {
    RERROR << "Interface: Invalid play_mode = " << play_mode
           << " and use default play_mode as online";
    play_mode = static_cast<int>(robosense::common::PlayMode::ONLINE);
  }

  // 判断是否需要重新启动
  if (g_play_mode != play_mode) {
    g_play_mode = play_mode;
    if (GetState() == State::STATE_RUNNING ||
        GetState() == State::STATE_INITIALIZED) {
      int ret = Reset();
      if (ret != 0) {
        RERROR << "Interface: Reset() Failed !";
        return -1;
      }
    }
  }
  return 0;
}

EXPORT_API int SetEnablePostProcess(bool enable_postprocess)
{
  if (g_impl_ptr) {
    return g_impl_ptr->SetEnablePostProcess(enable_postprocess);
  }
  return -1;
}

EXPORT_API int SetEnableSlam(bool enable_slam) {
  if (g_impl_ptr) {
    return g_impl_ptr->SetEnableSlam(enable_slam);
  }
  return -1;
}

EXPORT_API int SetEnableRelocalization(bool enable_relocalization) {
  g_is_enable_relocalization = enable_relocalization;
  if (g_impl_ptr) {
    return g_impl_ptr->SetEnableRelocalization(g_is_enable_relocalization);
  }
  return -1;
}

EXPORT_API int SetEnableSlamOfflineOpt(bool enable_slam_offline_opt) {
  if (g_impl_ptr) {
    return g_impl_ptr->SetEnableSlamOfflineOpt(enable_slam_offline_opt);
  }
  return -1;
}

EXPORT_API int ResetSlam() {
  if (g_impl_ptr == nullptr) {
    RERROR << "g_impl_ptr Is Nullptr !";
    return -1;
  }

  int ret = g_impl_ptr->ResetSlam();
  if (ret != 0) {
    RERROR << "ResetSlam Failed: ret = " << ret;
    return -2;
  }

  return 0;
}

EXPORT_API int SetEnableImageDepth(bool enable_image_depth) {
  if (g_impl_ptr) {
    return g_impl_ptr->SetEnableImageDepth(enable_image_depth);
  }
  return -1;
}

EXPORT_API int SetEnableSkeleton(bool enable_skeleton) {
  if (g_impl_ptr) {
    return g_impl_ptr->SetEnableSkeleton(enable_skeleton);
  }
  return -1;
}

// 设置是否显示Position
EXPORT_API void SetEnablePositionShow(bool enable_position_show) {
  g_show_position = enable_position_show;
}

// 设置是否显示着色RGB点云
EXPORT_API void SetEnablePointCloudShow(bool enable_pointcloud_show) {
  g_show_pointcloud = enable_pointcloud_show;
}

// 设置是否显示PointCloud Slam
EXPORT_API void SetEnablePointCloudSlamShow(bool enable_pointcloud_slam_show) {
  g_show_slam_pointcloud = enable_pointcloud_slam_show;
}

// 设置是否显示TriangleFacet
EXPORT_API void SetEnableTriangleFacetShow(bool enable_triangle_facet_show) {
  g_show_trianglefacet = enable_triangle_facet_show;
}

// 设置是否RGB图像(AC1)
EXPORT_API void SetEnableRgbImageShow(bool enable_rgb_image_show) {
  g_show_rgbimage = enable_rgb_image_show;
}

// 设置是否Depth
EXPORT_API void SetEnableDepthShow(bool enable_depth_show) {
  g_show_depth = enable_depth_show;
}

// 设置是否显示Left Image(AC2)
EXPORT_API void SetEnableLeftImageShow(bool enable_left_image_show) {
  g_show_left_image = enable_left_image_show;
}

// 设置是否显示Right Image(AC2)
EXPORT_API void SetEnableRightImageShow(bool enable_right_image_show) {
  g_show_right_image = enable_right_image_show;
}

// 设置是否显示Image Depth(AC2)
EXPORT_API void SetEnableImageDepthShow(bool enable_image_depth_show) {
  g_show_image_depth = enable_image_depth_show;
}

// 设置是否显示Skeleton 3D点
EXPORT_API void SetEnableSkeleton3DPointsShow(bool enable) {
  g_show_skeleton_3d_points = enable;
}

// 设置是否显示Skeleton 2D点左图像
EXPORT_API void SetEnableSkeleton2DPointsLeftImageShow(bool enable) {
  g_show_skeleton_2d_points_left_image = enable;
}

// 设置是否显示Skeleton 2D点右图像
EXPORT_API void SetEnableSkeleton2DPointsRightImageShow(bool enable) {
  g_show_skeleton_2d_points_right_image = enable;
}

EXPORT_API void GetDeviceInfo(void (*handler)(void *, //
                                              unsigned int)) {
  if (!g_impl_ptr) {
    RERROR << "g_impl_ptr is nullptr !";
    return;
  } else {
    auto device_infos = g_impl_ptr->GetDevices();
    auto devices = new TagDeviceEvent[device_infos.size()];
    int index = 0;
    for (auto iterSet = device_infos.begin(); iterSet != device_infos.end();
         iterSet++, index++) {
      auto &device = devices[index];
      const auto &info = *iterSet;
      auto type = static_cast<uint8_t>(
          robosense::common::DeviceEventType_t::DEVICE_EVENT_ATTACH);
      device.event_type = type;
      device.uuid_size = info.size();
      memcpy(device.uuid, info.c_str(), info.size());
    }
    handler(devices, device_infos.size());
    delete[] devices;
  }
}

EXPORT_API int OperatorDevice(const char *uuid, unsigned char operator_type) {
  if (!g_impl_ptr) {
    RERROR << "g_impl_ptr is nullptr !";
    return -1;
  } else {
    robosense::common::DeviceOperator device_operator;
    device_operator.operation_type =
        static_cast<robosense::common::DeviceOperatorType_t>(operator_type);
    std::string str_uuid(uuid);
    device_operator.uuid_size = str_uuid.size();
    memcpy(device_operator.uuid, str_uuid.c_str(), str_uuid.size());
    return g_impl_ptr->OperatorDevice(device_operator);
  }
}

EXPORT_API int OperatorOtaDevice(const char *uuid, unsigned char operator_type,
                                 const char *ota_bin_file_path,
                                 bool *ota_result_value) {
  if (!g_impl_ptr) {
    RERROR << "g_impl_ptr is nullptr !";
    return -1;
  } else {
    robosense::common::DeviceOtaOperator device_ota_operator;
    device_ota_operator.operation_type =
        static_cast<robosense::common::DeviceOtaOperatorType_t>(operator_type);
    std::string str_uuid(uuid);
    device_ota_operator.uuid_size = str_uuid.size();
    memcpy(device_ota_operator.uuid, str_uuid.c_str(), str_uuid.size());

    // OTA START时
    if (device_ota_operator.operation_type ==
        robosense::common::DEVICE_OTA_OPERATOR_START) {
      if (ota_bin_file_path == nullptr) {
        RERROR << "ota_bin_file_path is nullptr !";
        return -2;
      }
      std::string str_ota_bin_file_path(ota_bin_file_path);
      device_ota_operator.ota_file_path_size = str_ota_bin_file_path.size();
      memcpy(device_ota_operator.ota_file_path, str_ota_bin_file_path.c_str(),
             str_ota_bin_file_path.size());
    }

    int ret =
        g_impl_ptr->OperatorDeviceOta(device_ota_operator, *ota_result_value);
    if (ret != 0) {
      RERROR << "Operator Device Ota Failed: ret = " << ret;
      return -3;
    }

    return 0;
  }
}

EXPORT_API int QueryDeviceInfo(const char *uuid,
                               void(handler)(void *, void *)) {
  if (handler == nullptr || uuid == nullptr) {
    RERROR << "handler or uuid is nullptr !";
    return -1;
  } else if (g_impl_ptr == nullptr) {
    RERROR << "g_impl_ptr is nullptr !";
    return -2;
  }
  const std::string &device_uuid = std::string(uuid);
  std::string device_info;
  int ret = g_impl_ptr->QueryDeviceInfo(device_uuid, device_info);
  if (ret != 0) {
    RERROR << "Query Device Info Failed: device_uuid = " << device_uuid
           << ", ret = " << ret;
    return -3;
  }

  Tag_CString *pUuid = new Tag_CString();
  pUuid->length = std::min(device_uuid.size(), sizeof(pUuid->data));
  memcpy(pUuid->data, uuid, pUuid->length);

  Tag_CString2 *pDeviceInfo = new Tag_CString2();
  pDeviceInfo->length = std::min(device_info.size(), sizeof(pDeviceInfo->data));
  memcpy(pDeviceInfo->data, device_info.c_str(), pDeviceInfo->length);

  handler(pUuid, pDeviceInfo);
  delete pUuid;
  delete pDeviceInfo;

  return 0;
}

EXPORT_API int GetTopicConfig(void (*handler)(void *, unsigned int)) {
  if (handler == nullptr) {
    RERROR << "GetTopicConfig: handler is nullptr !";
    return -1;
  }

  std::map<std::string, robosense::io::DataTopicNameInfoItem> infos;
  g_impl_ptr->GetSupportTopicNameInfos(infos);
  const uint32_t infos_size = infos.size();

  auto data = new TagTConfig[infos_size];
  int index = 0;
  for (auto iterMap = infos.begin(); iterMap != infos.end(); ++iterMap) {
    const auto &item = iterMap->second;
    TagTConfig &config = data[index];

    // topicName
    config.topic.length = item.topicName.size();
    memcpy(config.topic.data, item.topicName.c_str(), config.topic.length);

    // dataType
    config.type.length = item.dataType.size();
    memcpy(config.type.data, item.dataType.c_str(), config.type.length);

    // ProcessTypes
    config.process_types_size = item.processTypes.size();
    for (size_t i = 0;
         i < config.process_types_size && i < sizeof(config.process_types);
         ++i) {
      const std::string &process_type = item.processTypes[i];
      config.process_types[i].length = process_type.size();
      memcpy(config.process_types[i].data, process_type.c_str(),
             config.process_types[i].length);
    }
    ++index;
  }
  handler(data, infos_size);
  delete[] data;
  return 0;
}

EXPORT_API int GetFileFormat(void (*handler)(void *, unsigned int)) {
  if (handler == nullptr) {
    RERROR << "GetFileFormat: handler is nullptr !";
    return -1;
  }

  std::set<std::string> formats;
  formats = g_impl_ptr->GetDataSupportFormat();
  const uint32_t format_size = formats.size();
  auto data = new TagCString[format_size];
  int index = 0;
  for (auto iterSet = formats.begin(); iterSet != formats.end(); ++iterSet) {
    const std::string &format = *iterSet;
    TagCString &item = data[index];
    item.length = format.size();
    memcpy(item.data, format.c_str(), item.length);
    ++index;
  }
  handler(data, format_size);
  delete[] data;
  return 0;
}

EXPORT_API void SetAppRootPath(const char *appRootPath) {
  // 更新App
  robosense::io::Ros2EnvironmentUtil::initAppSettingRuntimeDirPath(
      std::string(appRootPath));
}

EXPORT_API int StartWriter(const char *dataDirPath, const char *dataFormat,
                           const void *tSetting, unsigned int tSettingCount) {
  if (dataDirPath == nullptr) {
    RERROR << "StartWriter: dataDirPath is nullptr !";
    return -1;
  } else if (dataFormat == nullptr) {
    RERROR << "StartWriter: dataFormat is nullptr !";
    return -2;
  } else if (tSetting == nullptr || tSettingCount == 0) {
    RERROR << "StartWriter: tSetting is nullptr or tSettingCount is 0 !";
    return -3;
  }

  const TagTSetting *pTSetting =
      reinterpret_cast<const TagTSetting *>(tSetting);
  robosense::io::DataWriterConfig config;
  config.dataDirPath = std::string(dataDirPath);
  const auto formatType =
      robosense::io::DataFormatSupportUtil::fromStringToFormatType(
          std::string(dataFormat));
  if (formatType ==
      robosense::io::RS_DATA_FORMAT_TYPE::RS_DATA_FORMAT_UNKNOWN) {
    RERROR << "StartWriter: Format type is unknown !";
    return -4;
  }
  config.dataIoSourceType = formatType;
  for (unsigned int i = 0; i < tSettingCount; ++i) {
    const TagTSetting &topic = pTSetting[i];

    const std::string &topicName =
        std::string(topic.topic.data, topic.topic.length);

    // writerTopicNames
    config.writerTopicNames.insert(topicName);

    const std::string &processType =
        std::string(topic.compressedType.data, topic.compressedType.length);

    // topicProcessTypes
    config.topicProcessTypes.insert(
        {topicName,
         robosense::io::DataTopicNameSupportUtil::fromStringToProcessType(
             processType)});
  }

  int ret = g_impl_ptr->StartDataWrite(config);
  if (ret != 0) {
    RERROR << "StartWriter: Start Data Writer Failed: ret = " << ret;
    return -5;
  }

  return 0;
}

EXPORT_API int StopWriter() {
  int ret = g_impl_ptr->StopDataWrite();
  if (ret != 0) {
    RERROR << "StopWriter: Stop Data Writer Failed: ret = " << ret;
    return -1;
  }
  return 0;
}

EXPORT_API int GetFileTopicsRos1(const char *sourceDataFilePath,
                                 void (*handler)(void *, unsigned int,
                                                 unsigned long long int,
                                                 unsigned long long int)) {
  if (sourceDataFilePath == nullptr) {
    RERROR << "GetFileTopicsRos1: sourceDataFilePath is nullptr !";
    return -1;
  } else if (handler == nullptr) {
    RERROR << "GetFileTopicsRos1: handler is nullptr !";
    return -2;
  }

  std::map<std::string, std::string> topic_types;
  std::pair<uint64_t, uint64_t> time_points;
  int ret = g_impl_ptr->GetDataTopicInfoRos(std::string(sourceDataFilePath),
                                            topic_types, time_points);
  if (ret != 0) {
    RERROR << "GetFileTopicsRos1: Get Data Topic Info Ros Failed: ret = " << ret
           << ", ROS File Path = " << std::string(sourceDataFilePath);
    return -3;
  }

  const uint32_t topic_size = topic_types.size();
  auto data = new TagCString[topic_size];
  int index = 0;
  for (auto iterMap = topic_types.begin(); iterMap != topic_types.end();
       ++iterMap) {
    const std::string &topicName = iterMap->first;

    TagCString &item = data[index];
    item.length = topicName.size();
    memcpy(item.data, topicName.c_str(), item.length);

    ++index;
  }
  handler(data, topic_size, time_points.first, time_points.second);
  delete[] data;
  return 0;
}

EXPORT_API int GetFileTopicsRos2(const char *sourceDataFilePath,
                                 void (*handler)(void *, unsigned int,
                                                 unsigned long long int,
                                                 unsigned long long int)) {
  if (sourceDataFilePath == nullptr) {
    RERROR << "GetFileTopicsRos2: sourceDataFilePath is nullptr !";
    return -1;
  } else if (handler == nullptr) {
    RERROR << "GetFileTopicsRos2: handler is nullptr !";
    return -2;
  }

  std::map<std::string, std::string> topic_types;
  std::pair<uint64_t, uint64_t> time_points;
  int ret = g_impl_ptr->GetDataTopicInfoRos2(std::string(sourceDataFilePath),
                                             topic_types, time_points);
  if (ret != 0) {
    RERROR << "GetFileTopicsRos2: Get Topic Info ROS2 Failed: ret = " << ret
           << ", ROS2 File Directory Path = "
           << std::string(sourceDataFilePath);
    return -3;
  }

  const uint32_t topic_size = topic_types.size();
  auto data = new TagCString[topic_size];
  int index = 0;
  for (auto iterMap = topic_types.begin(); iterMap != topic_types.end();
       ++iterMap) {
    const std::string &topicName = iterMap->first;

    TagCString &item = data[index];
    item.length = topicName.size();
    memcpy(item.data, topicName.c_str(), item.length);

    ++index;
  }
  handler(data, topic_size, time_points.first, time_points.second);
  delete[] data;
  return 0;
}

EXPORT_API int StartReader(const char *sourceDataFilePath,
                           const char *mainSyncTopicName,
                           unsigned int preLoadMainSyncCnt,
                           const void *tCStrings, unsigned int tCStringsCount,
                           long long int startTimestampNs,
                           long long int endTimestampNs) {
  std::unique_lock<std::recursive_mutex> lock(g_mutex_);
  // 设置为离线模式
  SetPlayMode(static_cast<int>(robosense::common::PlayMode::OFFLINE));

  if (sourceDataFilePath == nullptr) {
    RERROR << "StartReader: sourceDataFilePath is nullptr !";
    return -1;
  } else if (mainSyncTopicName == nullptr) {
    RERROR << "StartReader: mainSyncTopicName is nullptr !";
    return -2;
  }

  robosense::io::DataReaderConfig config;
  config.sourceDataFilePath = std::string(sourceDataFilePath);
  config.mainSyncTopicName = std::string(mainSyncTopicName);
  config.preLoadMainSyncCnt = preLoadMainSyncCnt;
  const Tag_CString *pTCStrings =
      reinterpret_cast<const Tag_CString *>(tCStrings);
  if (tCStringsCount > 0 && pTCStrings != nullptr) {
    for (size_t i = 0; i < tCStringsCount; ++i) {
      const auto &setting = pTCStrings[i];
      const std::string &readTopicName =
          std::string(setting.data, setting.length);
      config.readTopicNames.insert(readTopicName);
    }
  }
  config.readTopicTimeRange =
      std::pair<int64_t, int64_t>{startTimestampNs, endTimestampNs};

  int ret = g_impl_ptr->StartDataRead(config);
  if (ret != 0) {
    RERROR << "StartReader: Start Read Failed: ret = " << ret
           << ", sourceDataFilePath = " << config.sourceDataFilePath
           << ", mainSyncTopicName = " << config.mainSyncTopicName;
    return -3;
  }

  return 0;
}

EXPORT_API unsigned int GetTotalFrameCount() {
  std::unique_lock<std::recursive_mutex> lock(g_mutex_);
  if (g_impl_ptr->GetInitProgressDataRead() != 100) {
    return static_cast<unsigned int>(-1);
  }
  return g_impl_ptr->GetTotalFrameCount();
}

EXPORT_API unsigned long long GetCurrentTimestamp() {
  std::unique_lock<std::recursive_mutex> lock(g_mutex_);
  if (g_impl_ptr->GetInitProgressDataRead() != 100) {
    return static_cast<unsigned long long>(-1);
  }
  return g_impl_ptr->GetCurrentTimestamp();
}

EXPORT_API int GetCurrentFrameIndex() {
  std::unique_lock<std::recursive_mutex> lock(g_mutex_);
  if (g_impl_ptr->GetInitProgressDataRead() != 100) {
    return -1;
  }
  return g_impl_ptr->GetCurrentFrameIndex();
}

EXPORT_API unsigned int GetInitiProgress() {
  std::unique_lock<std::recursive_mutex> lock(g_mutex_);
  if (!g_impl_ptr) {
    return 0;
  }
  return g_impl_ptr->GetInitProgressDataRead();
}

EXPORT_API bool CheckIsPlaying() {
  std::unique_lock<std::recursive_mutex> lock(g_mutex_);
  return g_impl_ptr->CheckDataReadIsPlaying();
}

EXPORT_API int SkipFrame(int skip) {
  int ret = g_impl_ptr->DataReadSkipFrame(skip);
  if (ret != 0) {
    return -1;
  }
  return 0;
}

EXPORT_API int Play() {
  if (g_impl_ptr->CheckPlayFinish()) {
    RERROR << "Play is end!";
    return -1;
  }
  int ret = g_impl_ptr->DataReadPlay();
  if (ret != 0) {
    return -1;
  }
  return 0;
}

EXPORT_API int Pause() {
  int ret = g_impl_ptr->DataReadPause();
  if (ret != 0) {
    return -1;
  }
  return 0;
}

EXPORT_API int StopReader() {
  std::unique_lock<std::recursive_mutex> lock(g_mutex_);
  SetPlayMode(static_cast<int>(robosense::common::PlayMode::ONLINE));
  int ret = g_impl_ptr->DataReadStop();
  if (ret != 0) {
    RERROR << "DataReadStop Failed: ret = " << ret;
    return -1;
  }
  return 0;
}

EXPORT_API int GetACDataFrequence(const char *uuid, float *image_freq,
                                  float *depth_freq, float *imu_freq) {

  if (uuid == nullptr || image_freq == nullptr || depth_freq == nullptr ||
      imu_freq == nullptr) {
    RERROR << "Input uuid Or image_freq Or depth_freq Or imu_freq Is Nullptr !";
    return -1;
  } else if (g_impl_ptr == nullptr) {
    RERROR << "g_impl_ptr Is Nullptr !";
    return -2;
  }

  const std::string &device_uuid = std::string(uuid);
  float tmp_image_freq = 0;
  float tmp_depth_freq = 0;
  float tmp_imu_freq = 0;
  int ret = g_impl_ptr->GetACDataFrequence(device_uuid, tmp_image_freq,
                                           tmp_depth_freq, tmp_imu_freq);
  if (ret != 0) {
    RERROR << "Get ACData Frequence Failed: device_uuid = " << device_uuid
           << ", ret = " << ret;
    return -3;
  }
  *image_freq = tmp_image_freq;
  *depth_freq = tmp_depth_freq;
  *imu_freq = tmp_imu_freq;

  return 0;
}

EXPORT_API int SaveACDataToFile(const char *save_dir_path,
                                const int save_oprerator_type) {

  if (save_dir_path == nullptr) {
    RERROR << "Input save_dir_path Is Nullptr !";
    return -1;
  } else if (g_impl_ptr == nullptr) {
    RERROR << "g_impl_ptr Is Nullptr !";
    return -2;
  }
  const std::string &tmp_save_dir_path = std::string(save_dir_path);
  int ret =
      g_impl_ptr->SaveACDataToFile(tmp_save_dir_path, save_oprerator_type);
  if (ret != 0) {
    RERROR << "Save AC Data To File Failed: ret = " << ret;
    return -3;
  }

  return 0;
}

EXPORT_API int StartSlamDataWriter(const char *slam_data_save_path) {
  if (g_impl_ptr == nullptr) {
    RERROR << "g_impl_ptr Is Nullptr !";
    return -1;
  } else if (slam_data_save_path == nullptr) {
    RERROR << "slam_data_save_path Is Nullptr !";
    return -2;
  }

  const std::string &slamDataSavePath = std::string(slam_data_save_path);
  int ret = g_impl_ptr->StartSlamDataWriter(slamDataSavePath);
  if (ret != 0) {
    RERROR << "Start Slam Data Writer Failed: ret = " << ret;
    return -3;
  }

  return 0;
}

EXPORT_API int StopSlamDataWriter() {
  if (g_impl_ptr == nullptr) {
    RERROR << "g_impl_ptr Is Nullptr !";
    return -1;
  }

  int ret = g_impl_ptr->StopSlamDataWriter();
  if (ret != 0) {
    RERROR << "Stop Slam Data Writer Failed: ret = " << ret;
    return -2;
  }

  return 0;
}

EXPORT_API int
StartSlamDataExporter(const char *slam_data_read_path,
                      const char *slam_data_export_path,
                      const bool is_enable_segment,
                      const unsigned long long int segment_size_bytes_th) {
  if (g_impl_ptr == nullptr) {
    RERROR << "g_impl_ptr Is Nullptr !";
    return -1;
  } else if (slam_data_read_path == nullptr ||
             slam_data_export_path == nullptr) {
    RERROR << "slam_data_read_path or/and slam_data_export_path Is Nullptr !";
    return -2;
  }

  const std::string &slamDataReadPath = std::string(slam_data_read_path);
  const std::string &slamDataExportPath = std::string(slam_data_export_path);
  robosense::io::SlamDataExporterConfig config;
  config.basic_save_dir_path = slamDataReadPath;
  config.export_save_dir_path = slamDataExportPath;
  config.is_segment = is_enable_segment;
  config.max_segment_size_bytes = segment_size_bytes_th;

  int ret = g_impl_ptr->StartSlamDataExporter(config);
  if (ret != 0) {
    RERROR << "Start Slam Data Export Failed: ret = " << ret;
    return -3;
  }

  return 0;
}

EXPORT_API int GetSlamDataExporterProgress(int *progress) {
  if (g_impl_ptr == nullptr) {
    RERROR << "g_impl_ptr Is Nullptr !";
    return -1;
  } else if (progress == nullptr) {
    RERROR << "progress Is Nullptr !";
    return -2;
  }

  int32_t tmpProgress = 0;
  int ret = g_impl_ptr->GetSlamDataExporterProgress(tmpProgress);
  if (ret != 0) {
    RERROR << "Get Slam Data Exporter Progress Failed: ret = " << ret;
    return -3;
  }
  *progress = tmpProgress;

  return 0;
}

EXPORT_API int CheckSlamDataExporterStatus(bool *status) {
  if (g_impl_ptr == nullptr) {
    RERROR << "g_impl_ptr Is Nullptr !";
    return -1;
  } else if (status == nullptr) {
    RERROR << "status Is Nullptr !";
    return -2;
  }

  bool tmpStatus = false;
  int ret = g_impl_ptr->CheckSlamDataExporterStatus(tmpStatus);
  if (ret != 0) {
    RERROR << "Check Slam Data Exporter Status Failed: ret = " << ret;
    return -3;
  }
  *status = tmpStatus;

  return 0;
}

EXPORT_API int StopSlamDataExporter() {
  if (g_impl_ptr == nullptr) {
    RERROR << "g_impl_ptr Is Nullptr !";
    return -1;
  }

  int ret = g_impl_ptr->StopSlamDataExporter();
  if (ret != 0) {
    RERROR << "Stop Slam Data Exporter Failed: ret = " << ret;
    return -2;
  }

  return 0;
}

EXPORT_API int GetCalibrationUUID(void (*handler)(void *)) {
  if (g_impl_ptr == nullptr) {
    RERROR << "g_impl_ptr Is Nullptr !";
    return -1;
  } else if (handler == nullptr) {
    RERROR << "handler Is Nullptr !";
    return -2;
  }

  std::string device_id;
  int ret = g_impl_ptr->GetCalibDeviceID(device_id);
  if (ret != 0) {
    RERROR << "Get Calibaration Device ID Failed: ret = " << ret;
    return -3;
  } else {
    RINFO << "Get Calibration Device ID Successed: " << device_id;
  }

  Tag_CString cstring;
  cstring.length = device_id.size();
  memcpy(cstring.data, device_id.data(), cstring.length);

  handler(&cstring);

  return 0;
}

EXPORT_API int
CopyCalibFileToConfigDirectory(const char *calibaration_file_path) {
  if (g_impl_ptr == nullptr) {
    RERROR << "g_impl_ptr Is Nullptr !";
    return -1;
  } else if (calibaration_file_path == nullptr) {
    RERROR << "calibaration_file_path Is Nullptr !";
    return -2;
  }

  const std::string &calib_file_path = std::string(calibaration_file_path);
  int ret = g_impl_ptr->CopyCalibFileToConfigDirectory(calib_file_path);
  if (ret != 0) {
    RERROR << "Copy Calibration File: " << calib_file_path
           << " To Config Directory Failed: ret = " << ret;
    return -3;
  } else {
    RINFO << "Copy Calibration File: " << calib_file_path
          << " To Config Directory Successed !";
  }

  return 0;
}

EXPORT_API int ExportCalibFileFromHardware(const char *uuid,
                                           const char *export_calib_dir_path) {
  if (g_impl_ptr == nullptr) {
    RERROR << "g_impl_ptr Is Nullptr !";
    return -1;
  } else if (uuid == nullptr || export_calib_dir_path == nullptr) {
    RERROR << "uuid or calibaration_file_path Is Nullptr !";
    return -2;
  }

  const std::string &device_uuid = std::string(uuid);
  const std::string &calib_export_dir_path = std::string(export_calib_dir_path);

  int ret = g_impl_ptr->ExportCalibFileFromHardware(device_uuid,
                                                    calib_export_dir_path);
  if (ret != 0) {
    AINFO << "device_uuid = " << device_uuid
          << ", Export Calibration File To Directory: " << calib_export_dir_path
          << " Failed: ret = " << ret;
    return -3;
  } else {
    AINFO << "device_uuid = " << device_uuid
          << ", Export Calibration File To Directory: " << calib_export_dir_path
          << " Successed !";
  }

  return 0;
}

EXPORT_API int WriteCalibFileToHardware(const char *uuid) {
  if (g_impl_ptr == nullptr) {
    RERROR << "g_impl_ptr Is Nullptr !";
    return -1;
  } else if (uuid == nullptr) {
    RERROR << "uuid Is Nullptr !";
    return -2;
  }

  const std::string &device_uuid = std::string(uuid);
  int ret = g_impl_ptr->WriteCalibFileToHardware(device_uuid);
  if (ret != 0) {
    RERROR << "device_uuid = " << device_uuid
           << " Write Calibration File To Hardware Failed: ret = " << ret;
    return -3;
  } else {
    RINFO << "device_uuid = " << device_uuid
          << " Write Calibration File To Hardware Successed !";
  }

  return 0;
}
