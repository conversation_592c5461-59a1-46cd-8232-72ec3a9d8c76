/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include <filesystem>

#include "cyber/init.h"
#include "hyper_vision/common/common.h"

namespace robosense
{
  void ConfigureManager::setConfigFile(const std::string& config_file)
  {
    std::filesystem::path config_path(config_file);

    if (!std::filesystem::exists(config_path))
    {
      RERROR << "Configuration file not found: " << config_file;
      throw std::runtime_error("Configuration file not found: " + config_file);
    }

    auto extension = config_path.extension();
    if (extension != ".yaml")
    {
      RERROR << "Configuration file must be a .yaml file. Got: " << config_file;
      return;
    }

    cfg_node_ = YAML::Null;
    Utils::loadFile(config_file, cfg_node_);
    cfg_path_ = config_path.parent_path().string();
    cfg_node_["root_dir"] = cfg_path_;
    catYAML(cfg_node_);
  }

  bool ConfigureManager::catYAML(YAML::Node &node)
  {
    if (node.Type() != YAML::NodeType::Map and node.Type() != YAML::NodeType::Sequence)
      return true;

    if (node.Type() == YAML::NodeType::Map)
    {
      std::string include_str;
      if (Utils::yamlRead(node, "include", include_str))
      {
        auto path = Utils::getAbsolutePath(cfg_path_, include_str);
        YAML::Node sub_node;
        if (Utils::loadFile(path, sub_node))
        {
          if (!catYAML(sub_node))
            return false;

          node = sub_node;
        }
      }
      else
      {
        for (auto it = node.begin(); it != node.end(); ++it)
        {
          YAML::Node &sub_node = it->second;
          if (!catYAML(sub_node))
            return false;
        }
      }
    }
    else
    {
      // process sequence
      for (auto it = node.begin(); it != node.end(); ++it)
      {
        YAML::Node sub_node = *it;
        catYAML(sub_node);
      }
    }
    return true;
  }

  void Utils::init(const std::string& config_file,
                   const std::string& app_runtime_dir_path)
  {
    // 将字符串转换为路径对象，并获取其扩展名
    if (std::filesystem::path(config_file).extension() != ".yaml")
    {
      RWARN << name() << "Initialization failed, only supports configuration files in YAML format. " << config_file;
      return;
    }

    ConfigureManager::getInstance().setConfigFile(config_file);
    YAML::Node cfg_node = ConfigureManager::getInstance().getCfgNode();
    YAML::Node general_node;
    yamlSubNode(cfg_node, "general", general_node);

    // logger
    {
      YAML::Node logger_node;
      yamlSubNode(general_node, "logger", logger_node);
      std::string log_level = "INFO";
      yamlRead(logger_node, "level", log_level);
      std::filesystem::path log_dir;
      if (!app_runtime_dir_path.empty())
      {
        log_dir = std::filesystem::path(app_runtime_dir_path) / "log";
        RINFO << name() << "create log directory path from " << app_runtime_dir_path;
      }
      else
      {
        log_dir = std::filesystem::current_path() / "log";
        RINFO << name() << "create log directory path from " << std::filesystem::current_path().string();
      }
      ensureDirectory(log_dir.string());
      FLAGS_log_dir = log_dir.string();
      FLAGS_logtostderr = false;     // 当此标志设置为 true 时，所有的日志消息将仅被发送到标准错误流（例如终端），而不会写入到默认的日志文件中
      FLAGS_alsologtostderr = true;  // 当此标志设置为 true 时，日志消息在写入到指定日志文件的同时，也会被复制一份发送到标准错误流
      FLAGS_minloglevel = 0;         // 0:INFO, 1:WARNING, 2:ERROR, 3:FATAL
      if (log_level == "INFO")
        FLAGS_minloglevel = 0;
      else if (log_level == "WARN")
        FLAGS_minloglevel = 1;
      else if (log_level == "ERROR")
        FLAGS_minloglevel = 2;

      apollo::cyber::Init("AcViewer_SDK");
    }
  }

  std::string Utils::getAbsolutePath(const std::string &prefix,
                                     const std::string &relative_path)
  {
    if (relative_path.empty())
      return prefix;

    // 将路径转换为 fs::path 对象以处理平台差异
    std::filesystem::path prefix_path(prefix);
    std::filesystem::path rel_path(relative_path);

    // 如果前缀为空 或 相对路径是绝对路径，直接返回相对路径
    if (prefix.empty() || rel_path.is_absolute())
    {
      return rel_path.string();
    }

    // 拼接路径（自动处理分隔符）
    std::filesystem::path combined_path = prefix_path / rel_path;

    // 返回规范化后的路径字符串（替换为平台分隔符）
    return combined_path.lexically_normal().string();
  }

  bool Utils::ensureDirectory(const std::string& path)
  {
    std::filesystem::path dir(path);
    std::error_code ec;

    // 如果路径不存在，则创建目录（包括所有父目录）
    if (!std::filesystem::exists(dir, ec))
    {
      if (!std::filesystem::create_directories(dir, ec))
      {
        RERROR << "ensureDirectory: create directory " << path << " failed!";
        return false;
      }
    } else if (!std::filesystem::is_directory(dir, ec))
    {
      RWARN << "ensureDirectory: " << path << " is not a directory!";
      return false;
    }
    return true;
  }
}
