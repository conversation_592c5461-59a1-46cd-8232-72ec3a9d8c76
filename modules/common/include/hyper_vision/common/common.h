/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef HYPER_VISION_COMMON_COMMON_H_
#define HYPER_VISION_COMMON_COMMON_H_

#include <yaml-cpp/yaml.h>

#include "hyper_vision/common/basic_type/basic_type.h"

namespace robosense
{
  class ConfigureManager
  {
  public:
    // 删除拷贝构造函数和赋值运算符，确保单例的唯一性
    ConfigureManager(const ConfigureManager&) = delete;
    ConfigureManager& operator=(const ConfigureManager&) = delete;

     // 获取 ConfigureManager 的单例。
     // 利用了C++11及以后版本的特性，即静态局部变量的初始化是线程安全的。
     // 当第一次调用时 instance 对象会被创建，并且这个过程由编译器和运行时库保证，即使在多线程环境下也只会执行一次。
    static ConfigureManager &getInstance() noexcept
    {
      static ConfigureManager instance;
      return instance;
    }

    void setConfigFile(const std::string& config_file);

    const std::string &getWorkRoot() const { return cfg_path_; }

    const YAML::Node& getCfgNode() const { return cfg_node_; }

  private:
    // 构造函数和析构函数设为私有，防止外部直接创建或销毁实例
    ConfigureManager() {}
    ~ConfigureManager() = default;

    bool catYAML(YAML::Node &node);

  private:
    YAML::Node cfg_node_;
    std::string cfg_path_;
  };

  class Utils
  {
  public:
    static void init(const std::string& config_file,
                     const std::string& app_runtime_dir_path);

    template<typename T>
    static bool yamlRead(const YAML::Node& node, const std::string& key, T& val)
    {
      try {
        val = node[key].as<T>();
        return true;
      }
      catch (std::exception& e) {
        return false;
      }
    }

    static bool yamlSubNode(const YAML::Node &node, const std::string &key, YAML::Node &ret)
    {
      try {
        ret = node[key];
        return true;
      }
      catch (std::exception &e) {
        return false;
      }
    }

    static bool loadFile(const std::string &yaml_file, YAML::Node &node)
    {
      try {
        node = YAML::LoadFile(yaml_file);
        return true;
      }
      catch (std::exception &e) {
        std::string error_msg(e.what());
        if (error_msg == "bad file")
        {
          RWARN << "yaml file do not exist! " << yaml_file;
        }
        else
        {
          RERROR << "-- YAML Load Error: ";
          RERROR << "-- In:\n\t" << yaml_file;
          RERROR << "-- What:\n\t" << error_msg;
        }
        return false;
      }
    }

    static std::string
    getAbsolutePath(const std::string &prefix, const std::string &relative_path);

    static bool
    ensureDirectory(const std::string& path);

  private:
    static std::string name() { return " Utils: "; }

  };
}  // namespace robosense

#endif  // HYPER_VISION_COMMON_COMMON_H_
