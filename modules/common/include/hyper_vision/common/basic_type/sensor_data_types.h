#ifndef SENSOR_DATA_TYPES_H
#define SENSOR_DATA_TYPES_H
#include <cstdint> // 包含 uint8_t, uint16_t 等类型
#include <functional>
#include <memory>
#include <pcl/point_types.h>

#ifdef _WIN32
#include <winsock2.h>
#else
// Linux/macOS-specific includes
#include <sys/time.h>
#endif

#ifdef __APPLE__
struct EIGEN_ALIGN16 RsPointXYZIRT {
  PCL_ADD_POINT4D;
  float intensity;
  uint16_t ring = 0;
  double timestamp = 0;
  EIGEN_MAKE_ALIGNED_OPERATOR_NEW
};

struct EIGEN_ALIGN16 RsPointXYZRGBIRT {
  PCL_ADD_POINT4D;
  float intensity;
  uint8_t r = 0;
  uint8_t g = 0;
  uint8_t b = 0;
  uint16_t ring = 0;
  double timestamp = 0;
  EIGEN_MAKE_ALIGNED_OPERATOR_NEW
};

struct EIGEN_ALIGN16 RsPointXYZRGBI8 {
  PCL_ADD_POINT4D;
  uint8_t intensity;
  uint8_t r = 0;
  uint8_t g = 0;
  uint8_t b = 0;
  EIGEN_MAKE_ALIGNED_OPERATOR_NEW
};
#else
struct RsPointXYZIRT {
  PCL_ADD_POINT4D;
  float intensity;
  uint16_t ring = 0;
  double timestamp = 0;
  EIGEN_MAKE_ALIGNED_OPERATOR_NEW
} EIGEN_ALIGN16;

struct RsPointXYZRGBIRT {
  PCL_ADD_POINT4D;
  float intensity;
  uint8_t r = 0;
  uint8_t g = 0;
  uint8_t b = 0;
  uint16_t ring = 0;
  double timestamp = 0;
  EIGEN_MAKE_ALIGNED_OPERATOR_NEW
} EIGEN_ALIGN16;

struct RsPointXYZRGBI8 {
  PCL_ADD_POINT4D;
  uint8_t intensity;
  uint8_t r = 0;
  uint8_t g = 0;
  uint8_t b = 0;
  EIGEN_MAKE_ALIGNED_OPERATOR_NEW
} EIGEN_ALIGN16;
#endif

POINT_CLOUD_REGISTER_POINT_STRUCT(
    RsPointXYZIRT,
    (float, x, x)(float, y, y)(float, z, z)(float, intensity, intensity)(
        std::uint16_t, ring, ring)(double, timestamp, timestamp))

POINT_CLOUD_REGISTER_POINT_STRUCT(
    RsPointXYZRGBIRT,
    (float, x, x)(float, y, y)(float, z, z)(float, intensity, intensity)(
        std::uint8_t, r, r)(std::uint8_t, g, g)(std::uint8_t, b, b)(
        std::uint16_t, ring, ring)(double, timestamp, timestamp))

POINT_CLOUD_REGISTER_POINT_STRUCT(
    RsPointXYZRGBI8,
    (float, x, x)(float, y, y)(float, z, z)(std::uint8_t, intensity, intensity)(
        std::uint8_t, r, r)(std::uint8_t, g, g)(std::uint8_t, b, b))

namespace robosense::common {

// AC Device Type:
typedef enum RS_AC_DEVICE_TYPE : int {
  RS_AC_DEVICE_UNKNOWN = 0,
  RS_AC_DEVICE_AC1 = 1, // AC1
  RS_AC_DEVICE_AC2 = 2, // AC2
} RS_AC_DEVICE_TYPE_t;

typedef enum ImageFrameFormat {
  FRAME_FORMAT_ANY = 0, /**< Any supported format */
  FRAME_FORMAT_H265,    /**< Compressed: h265 compressed format */
  FRAME_FORMAT_RGB24,   /**< 24-bit RGB format */
  FRAME_FORMAT_NV12,    /**< YUV420: NV12 format */
  FRAME_FORMAT_YUYV422, /**< YUV422: Y U Y V format */
  FRAME_FORMAT_YUV420P, /**< YUV420: YYYY UU VV format */
} ImageFrameFormat_t;

/**
 * @struct xyz
 * @brief Stores IMU (Inertial Measurement Unit) data for x, y, and z axes.
 */
typedef struct Xyz {
  float x; /**< IMU data for x-axis */
  float y; /**< IMU data for y-axis */
  float z; /**< IMU data for z-axis */
} Xyz_t;

/**
 * @struct xyz
 * @brief Stores IMU (Inertial Measurement Unit) data for x, y, and z axes.
 */
typedef struct Xyzw {
  float x; /**< IMU data for x-axis */
  float y; /**< IMU data for y-axis */
  float z; /**< IMU data for z-axis */
  float w;
} Xyzw_t;

/**
 * @struct motion_frame
 * @brief Contains motion data from the IMU, including acceleration, gyroscope,
 * and temperature data.
 */
typedef struct MotionFrame {
  using Ptr = std::shared_ptr<MotionFrame>;
  bool state;
  Xyz_t accel; /**< Accelerometer data (x, y, z) */
  Xyz_t gyro;  /**< Gyroscope data (x, y, z) */
  Xyzw_t orientation;
  float temperature;           /**< Temperature reading from the IMU */
  struct timeval capture_time; /**< Capture time of the motion data */
} MotionFrame_t;

/**
 * @struct cloud_point
 * @brief Represents a 3D point in a depth frame, including intensity
 * information.
 */
#if 0 
    typedef struct CloudPointXYZIRT
    {
        float x;                           /**< X-coordinate in 3D space */
        float y;                           /**< Y-coordinate in 3D space */
        float z;                           /**< Z-coordinate in 3D space */
        uint8_t intensity;                 /**< Intensity value at this point */
        uint16_t ring;                     /**< Intensity value at this point */
        double timestamp;                  /**< Time value at this point, the unit is seconds*/
    } CloudPointXYZIRT_t;
#else
using CloudPointXYZIRT = RsPointXYZIRT;
using CloudPointXYZIRT_t = CloudPointXYZIRT;
#endif

/**
 * @struct depth_frame
 * @brief Contains depth data, including an array of points and a timestamp.
 */
typedef struct DepthFrame {
  using Ptr = std::shared_ptr<DepthFrame>;
  std::shared_ptr<CloudPointXYZIRT_t>
      points;                  /**< Array of points in the depth data */
  uint16_t point_nums;         /**< Number of points in the depth data */
  struct timeval capture_time; /**< Capture time of the depth data */
} DepthFrame_t;

/**
 * @struct image_frame
 * @brief Represents a frame captured from a stream, including metadata and
 * image data.
 */
typedef struct ImageFrame {
  using Ptr = std::shared_ptr<ImageFrame>;
  bool state;
  std::shared_ptr<uint8_t> data; /**< Pointer to image data */
  size_t data_bytes;             /**< Size of the image data buffer in bytes */
  uint32_t width;                /**< Width of the image in pixels */
  uint32_t height;               /**< Height of the image in pixels */
  ImageFrameFormat_t frame_format; /**< Format of the pixel data */
  size_t step;       /**< Bytes per horizontal line (not defined for compressed
                        formats) */
  uint32_t sequence; /**< Frame sequence number */
  struct timeval capture_time; /**< Capture time of the image data */
} ImageFrame_t;

typedef enum DeviceEventType {
  DEVICE_EVENT_DETACH = 0,
  DEVICE_EVENT_ATTACH,

  DEVICE_EVENT_UNKNOWN = 255,
} DeviceEventType_t;

typedef struct DeviceEvent {
  DeviceEventType_t event_type;
  uint32_t uuid_size;
  char uuid[128];
  RS_AC_DEVICE_TYPE_t ac_device_type;
} DeviceEvent_t;

typedef enum DeviceOperatorType {
  DEVICE_OPERATOR_CLOSE = 0,
  DEVICE_OPERATOR_OPEN,
  DEVICE_OPERATOR_PAUSE,
  DEVICE_OPERATOR_PLAY,

  DEVICE_OPERATOR_UNKNOWN = 255,
} DeviceOperatorType_t;

typedef struct DeviceOperator {
  DeviceOperatorType_t operation_type;
  uint32_t uuid_size;
  char uuid[128];
} DeviceOperator_t;

typedef enum DeviceOtaOperatorType {
  DEVICE_OTA_OPERATOR_START = 0,
  DEVICE_OTA_OPERATOR_CANCLE,
  DEVICE_OTA_OPERATOR_FINISH,
  DEVICE_OTA_OPERATOR_CHECK_FINISH,
  DEVICE_OTA_OPERATOR_CHECK_SUCCESS,

  DEVICE_OTA_OPERATOR_UNKNOWN = 255,
} DeviceOtaOperatorType_t;

typedef struct DeviceOtaOperator {
  DeviceOtaOperatorType_t operation_type;
  uint32_t uuid_size;
  char uuid[128];
  uint32_t ota_file_path_size;
  char ota_file_path[1024];
} DeviceOtaOperator_t;

typedef enum SaveACDataOperatorType {
  SAVE_AC_DATA_IMAGE = 0x01,
  SAVE_AC_DATA_DEPTH = 0x02,
  SAVE_AC_DATA_IMU = 0x04,
} SaveACDataOperatorType_t;

// 在线/离线模式
enum class PlayMode {
  ONLINE = 1,
  OFFLINE = 2,
};

} // namespace robosense::common

#endif // SENSOR_DATA_TYPES_H