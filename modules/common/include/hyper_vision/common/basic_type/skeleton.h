/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef HYPER_VISION_SUPER_SENSOR_SKELETON_H
#define HYPER_VISION_SUPER_SENSOR_SKELETON_H

#include <cmath>
#include <vector>
#include <memory>

#include "hyper_vision/common/basic_type/header.h"
#include "hyper_vision/common/basic_type/DataType.h"

namespace robosense::common
{
  template<typename T>
  struct Point2
  {
    Point2() : x(0), y(0), score(0.f), valid(false)
    {};

    Point2(T x, T y) : x(x), y(y), score(0.f), valid(false)
    {};

    Point2(T x, T y, float score, bool valid) : x(x), y(y), score(score), valid(valid)
    {};

    bool is_valid() const { return valid; }

    float distance(const Point2& other) const
    {
      return std::sqrt((x - other.x) * (x - other.x) + (y - other.y) * (y - other.y));
    }

    T x, y;
    float score;
    bool valid;
  };

  template<typename T>
  struct Point3
  {
    using Ptr = std::shared_ptr<Point3>;

    Point3() : x(0), y(0), z(0), score(0.f), valid(false)
    {};

    Point3(T x, T y, T z) : x(x), y(y), z(z), score(0.f), valid(false)
    {};

    Point3(T x, T y, T z, float score, bool valid) : x(x), y(y), z(z), score(score), valid(valid)
    {};

    bool is_valid() const { return valid; }

    float distance(const Point3& other) const
    {
      return std::sqrt((x - other.x) * (x - other.x) + (y - other.y) * (y - other.y) + (z - other.z) * (z - other.z));
    }

    T x, y, z;
    float score;
    bool valid;
  };

  typedef Point2<double> Point2d;
  typedef Point3<double> Point3d;

  struct Skeleton
  {
    std::vector<Point3d> p3d;

    TagSkeletonPoint ToOutSkeletonPoint()
    {
      TagSkeletonPoint ret;

      if (not p3d.empty())
      {
        ret.point_cnt = std::min(static_cast<int>(p3d.size()), 32);  // 最多 32 个 3D 点

        for (int i = 0; i < ret.point_cnt; ++i)
        {
          ret.points[i * 3] = p3d[i].x;
          ret.points[i * 3 + 1] = p3d[i].y;
          ret.points[i * 3 + 2] = p3d[i].z;
        }
      }

      return ret;
    }
  };
}

#endif //HYPER_VISION_SUPER_SENSOR_SKELETON_H
