#ifndef DATAWRITERINTERFACE_H
#define DATAWRITERINTERFACE_H

#include <any>

#include "hyper_vision/dataiomanager/dataiocommon.h"

namespace robosense {
namespace io {

class DataWriterSingleItem {
public:
  using Ptr = std::shared_ptr<DataWriterSingleItem>;
  using ConstPtr = std::shared_ptr<const DataWriterSingleItem>;

public:
  DataWriterSingleItem() { reset(); }

  ~DataWriterSingleItem() = default;

public:
  void reset() {
    isReady = false;
    topicName.clear();
    writeTimestampNs = 0;
    message.reset();
  }

  template <typename MessageT>
  int update(const std::string &topic_name, const uint64_t write_time_stamp,
             const std::shared_ptr<MessageT> &msg) {
    topicName = topic_name;
    writeTimestampNs = write_time_stamp;
    message.reset(new std::any(msg));
    return 0;
  }

  template <typename PointT>
  int update(const std::string &topic_name, const uint64_t write_time_stamp,
             const typename pcl::PointCloud<PointT>::Ptr &msg) {
    topicName = topic_name;
    writeTimestampNs = write_time_stamp;
    message.reset(new std::any(msg));
    return 0;
  }

public:
  bool isReady;
  std::string topicName;
  uint64_t writeTimestampNs;
  std::shared_ptr<std::any> message;
};

class DataWriterH265Encoder {
public:
  using Ptr = std::shared_ptr<DataWriterH265Encoder>;
  using ConstPtr = std::shared_ptr<const DataWriterH265Encoder>;

public:
  using RS_DATA_WRITE_H265_CALLBACK =
      std::function<void(const DataWriterSingleItem::Ptr &)>;

  enum class RS_DATA_WRITE_H265_CODE_MODE : int {
    RS_DATA_WRITE_H265_CODE_UNKNOWN = 0,
    RS_DATA_WRITE_H265_CODE_SYNC,
    RS_DATA_WRITE_H265_CODE_ASYNC,
  };

public:
  DataWriterH265Encoder() {
    codeMode_ = RS_DATA_WRITE_H265_CODE_MODE::RS_DATA_WRITE_H265_CODE_UNKNOWN;
    h265CoderPtr_ = nullptr;
  }

  ~DataWriterH265Encoder() = default;

public:
  int init(const std::string &topicName,
           const robosense::h265::H265CodesConfig &h265CodesConfig,
           const RS_DATA_WRITE_H265_CALLBACK &callback) {
    if (topicName.empty()) {
      RERROR << "DataWriterH265Encoder: initial callback is nullptr or "
                "topicName is Empty !";
      return -1;
    }
    if (callback == nullptr) {
      codeMode_ = RS_DATA_WRITE_H265_CODE_MODE::RS_DATA_WRITE_H265_CODE_SYNC;
    } else {
      codeMode_ = RS_DATA_WRITE_H265_CODE_MODE::RS_DATA_WRITE_H265_CODE_ASYNC;
    }

    topicName_ = topicName;
    h265CodesConfig_ = h265CodesConfig;
    callback_ = callback;

    int ret = init();
    if (ret != 0) {
      RERROR << "DataWriterH265Encoder: initial failed: topicName = "
             << topicName_ << ", ret = " << ret;
      return -2;
    }

    return 0;
  }

  RS_DATA_WRITE_H265_CODE_MODE dataWriteH265CodeMode() const {
    return codeMode_;
  }

  int syncAddMessage(DataWriterSingleItem::Ptr &singleItemPtr,
                     const bool isRosFormat) {
    if (codeMode_ !=
        RS_DATA_WRITE_H265_CODE_MODE::RS_DATA_WRITE_H265_CODE_SYNC) {
      RERROR << "DataWriterH265Encoder: Initial is not SYNC Mode: topicName = "
             << topicName_;
      return -1;
    } else if (singleItemPtr == nullptr || singleItemPtr->message == nullptr) {
      RERROR
          << "DataWriterH265Encoder: Input H265 Encode Is Nullptr: topicName = "
          << topicName_;
      return -2;
    }

    auto imageFramePtr = std::any_cast<std::shared_ptr<robosense::common::ImageFrame>>(singleItemPtr->message);
    if (imageFramePtr == nullptr) {
      RERROR << "DataWriterH265Encoder: Any Cast To "
                "std::shared_ptr<robosense::common::ImageFrame> Failed: "
                "topicName = "
             << topicName_;
      return -3;
    } else if (imageFramePtr == nullptr) {
      RERROR << "DataWriterH265Encoder: Any Cast Is Nullptr: topicName = "
             << topicName_;
      return -4;
    }

    const auto &msgPtr = imageFramePtr;
    robosense::h265::H265Coder::CODER_BUFFER_PTR_VEC vecBufferPtr;
    std::vector<bool> iFrameFlags;
    int ret = h265CoderPtr_->encode(msgPtr->data.get(), msgPtr->data_bytes,
                                    vecBufferPtr, iFrameFlags);
    if (ret != 0) {
      RERROR << "DataWriterH265Encoder: H265 Encode Failed: topicName = "
             << topicName_ << ", ret = " << ret;
      return -5;
    }

    if (vecBufferPtr.size() != 1) {
      RERROR << "DataWriterH265Encoder: SYNC H265 Encode Failed: topicName = "
             << topicName_ << ", ret = " << ret;
      return -6;
    }

    if (isRosFormat) {
      ret = createH265Message(singleItemPtr, vecBufferPtr[0], iFrameFlags[0]);
    } else {
#if defined(ROS2_FOUND)
      ret =
          createH265MessageRos2(singleItemPtr, vecBufferPtr[0], iFrameFlags[0]);
#else
      RERROR << "DataWriterH265Encoder: ROS2 Not Support !";
      ret = -1;
#endif // defined(ROS2_FOUND)
    }
    if (ret != 0) {
      RERROR << "DataWriterH265Encoder:: SYNC Create H265 Encode Message "
                "Failed: topicName = "
             << topicName_ << ", ret = " << ret;
      return -7;
    }

    return 0;
  }

  // 异步调用
  int asyncAddMessage(const DataWriterSingleItem::Ptr &singleItemPtr,
                      const bool isRosFormat) {
    if (codeMode_ !=
        RS_DATA_WRITE_H265_CODE_MODE::RS_DATA_WRITE_H265_CODE_ASYNC) {
      RERROR << "DataWriterH265Encoder: Initial is not ASYNC Mode: topicName = "
             << topicName_;
      return -1;
    } else if (singleItemPtr == nullptr) {
      RERROR
          << "DataWriterH265Encoder: Input H265 Encode Is Nullptr: topicName = "
          << topicName_;
      return -2;
    }

    auto imageFramePtr = std::any_cast<std::shared_ptr<robosense::common::ImageFrame>>(singleItemPtr->message);
    if (imageFramePtr == nullptr) {
      RERROR << "DataWriterH265Encoder: Any Cast To "
                "std::shared_ptr<robosense::common::ImageFrame> Failed: "
                "topicName = "
             << topicName_;
      return -3;
    } else if (imageFramePtr == nullptr) {
      RERROR << "DataWriterH265Encoder: Any Cast Is Nullptr: topicName = "
             << topicName_;
      return -4;
    }

    const auto &msgPtr = imageFramePtr;
    robosense::h265::H265Coder::CODER_BUFFER_PTR_VEC vecBufferPtr;
    std::vector<bool> iFrameFlags;
    int ret = h265CoderPtr_->encode(msgPtr->data.get(), msgPtr->data_bytes,
                                    vecBufferPtr, iFrameFlags);
    if (ret != 0) {
      RERROR << "DataWriterH265Encoder: ASYNC H265 Encode Failed: topicName = "
             << topicName_ << ", ret = " << ret;
      return -5;
    }
    // 保存到队列中
    std::lock_guard<std::mutex> lg(bufferMsgsMtx_);
    bufferMsgs_.push_back(singleItemPtr);

    // 检查是否有输出
    for (size_t i = 0; i < vecBufferPtr.size(); ++i) {
      const auto &bufferPtr = vecBufferPtr[i];
      const bool iFrameFlag = iFrameFlags[i];
      auto &itemPtr = *(bufferMsgs_.begin());
      // 构造新的消息
      if (isRosFormat) {
        ret = createH265Message(itemPtr, bufferPtr, iFrameFlag);
      } else {
#if defined(ROS2_FOUND)
        ret = createH265MessageRos2(itemPtr, bufferPtr, iFrameFlag);
#else
        RERROR << "DataWriterH265Encoder: ROS2 Not Support !";
        ret = -1;
#endif // defined(ROS2_FOUND)
      }
      if (ret != 0) {
        RERROR
            << "DataWriterH265Encoder: Create H265 Message Failed: topicName = "
            << topicName_ << ", ret = " << ret;
        return -6;
      }
      if (callback_) {
        callback_(itemPtr);
      }
      bufferMsgs_.erase(bufferMsgs_.begin());
    }

    return 0;
  }

  //异步编码调用
  int asyncFlushMessage(const bool isRosFormat) {
    if (codeMode_ !=
        RS_DATA_WRITE_H265_CODE_MODE::RS_DATA_WRITE_H265_CODE_ASYNC) {
      RERROR << "DataWriterH265Encoder: Initial is not ASYNC Mode: topicName = "
             << topicName_;
      return -1;
    }

    robosense::h265::H265Coder::CODER_BUFFER_PTR_VEC vecBufferPtr;
    std::vector<bool> iFrameFlags;
    int ret = h265CoderPtr_->encodeFlush(vecBufferPtr, iFrameFlags);
    if (ret != 0) {
      RERROR << "DataWriterH265Encoder: H265 Encode Flush Failed: topicName = "
             << topicName_ << ", ret = " << ret;
      return -2;
    }

    std::lock_guard<std::mutex> lg(bufferMsgsMtx_);
    if (vecBufferPtr.size() != bufferMsgs_.size()) {
      RERROR << "DataWriterH265Encoder: H265 Encode Frame Count Not Matched: "
                "topicName = "
             << topicName_ << ", ret = " << ret;
      return -3;
    }

    for (size_t i = 0; i < vecBufferPtr.size(); ++i) {
      const auto &bufferPtr = vecBufferPtr[i];
      const bool iFrameFlag = iFrameFlags[i];
      // 构造新的消息
      auto &itemPtr = *(bufferMsgs_.begin());
      if (isRosFormat) {
        ret = createH265Message(itemPtr, bufferPtr, iFrameFlag);
      } else {
#if defined(ROS2_FOUND)
        ret = createH265MessageRos2(itemPtr, bufferPtr, iFrameFlag);
#else
        RERROR << "DataWriterH265Encoder: ROS2 Not Support !";
        ret = -1;
#endif // defined(ROS2_FOUND)
      }

      if (ret != 0) {
        RERROR
            << "DataWriterH265Encoder: Create H265 Message Failed: topicName = "
            << topicName_ << ", ret = " << ret;
        return -4;
      }
      if (callback_) {
        callback_(itemPtr);
      }
      bufferMsgs_.erase(bufferMsgs_.begin());
    }

    return 0;
  }

private:
  int init() {
    try {
      h265CoderPtr_.reset(new robosense::h265::H265Coder());
    } catch (...) {
      RERROR << "DataWriterH265Encoder: Malloc H265 Coder Failed !";
      return -1;
    }

    int ret = h265CoderPtr_->init(h265CodesConfig_);
    if (ret != 0) {
      RERROR << "DataWriterH265Encoder: H265 Coder Initial Failed: topicName = "
             << topicName_ << ", ret = " << ret;
      return -2;
    }

    return 0;
  }

  int createH265Message(
      DataWriterSingleItem::Ptr &signleItemPtr,
      const robosense::h265::H265Coder::CODER_BUFFER_PTR &bufferPtr,
      const bool iFrameFlag) {
    auto imageFramePtr = std::any_cast<std::shared_ptr<robosense::common::ImageFrame>>(signleItemPtr->message);
    if (imageFramePtr == nullptr) {
      RERROR << "DataWriterH265Encoder: Any Cast To "
                "std::shared_ptr<robosense::common::ImageFrame> Failed: "
                "topicName = "
             << topicName_;
      return -1;
    } else if (imageFramePtr == nullptr) {
      RERROR << "DataWriterH265Encoder: "
                "std::shared_ptr<robosense::common::ImageFrame> is nullptr: "
                "topicName = "
             << topicName_;
      return -2;
    }

    // 构造新的消息: RsCompressedImage
    rscamera_msg::RsCompressedImage::Ptr imagePtr(
        new rscamera_msg::RsCompressedImage());
    imagePtr->header.seq = imageFramePtr->sequence;
    imagePtr->header.stamp.fromSec(imageFramePtr->capture_time.tv_sec +
                                       imageFramePtr->capture_time.tv_usec *
                                       1e-6);
    imagePtr->type = iFrameFlag ? 1 : 2; // i帧为1, B帧为2
    imagePtr->data.resize(bufferPtr->size());
    memcpy(imagePtr->data.data(), bufferPtr->data(), bufferPtr->size());
    signleItemPtr->message.reset(new std::any(imagePtr));
    signleItemPtr->isReady = true;

    return 0;
  }

#if defined(ROS2_FOUND)
  int createH265MessageRos2(
      DataWriterSingleItem::Ptr &signleItemPtr,
      const robosense::h265::H265Coder::CODER_BUFFER_PTR &bufferPtr,
      const bool iFrameFlag) {
    std::shared_ptr<robosense::common::ImageFrame> *imageFramePtr =
        signleItemPtr->message
            ->AnyCast<std::shared_ptr<robosense::common::ImageFrame>>();
    if (imageFramePtr == nullptr) {
      RERROR << "DataWriterH265Encoder: Any Cast To "
                "std::shared_ptr<robosense::common::ImageFrame> Failed: "
                "topicName = "
             << topicName_;
      return -1;
    } else if (*imageFramePtr == nullptr) {
      RERROR << "DataWriterH265Encoder: "
                "std::shared_ptr<robosense::common::ImageFrame> is nullptr: "
                "topicName = "
             << topicName_;
      return -2;
    }

    // 构造新的消息: RsCompressedImage
    rscamera_msg::msg::RsCompressedImage::SharedPtr imagePtr(
        new rscamera_msg::msg::RsCompressedImage());
    // imagePtr->header.seq = (*imageFramePtr)->sequence;
    imagePtr->header.stamp =
        rclcpp::Time((*imageFramePtr)->capture_time.tv_sec,
                     (*imageFramePtr)->capture_time.tv_usec * 1000ull);
    imagePtr->type = iFrameFlag ? 1 : 2; // i帧为1, B帧为2
    imagePtr->data.resize(bufferPtr->size());
    memcpy(imagePtr->data.data(), bufferPtr->data(), bufferPtr->size());
    signleItemPtr->message.reset(new rally::Any(imagePtr));
    signleItemPtr->isReady = true;

    return 0;
  }
#endif // defined(ROS2_FOUND)

private:
  std::string topicName_;
  robosense::h265::H265CodesConfig h265CodesConfig_;
  RS_DATA_WRITE_H265_CALLBACK callback_;
  robosense::h265::H265Coder::Ptr h265CoderPtr_;

  std::mutex bufferMsgsMtx_;
  std::vector<DataWriterSingleItem::Ptr> bufferMsgs_;
  RS_DATA_WRITE_H265_CODE_MODE codeMode_;
};

class DataWriterInterface {
public:
  using Ptr = std::shared_ptr<DataWriterInterface>;
  using ConstPtr = std::shared_ptr<const DataWriterInterface>;

public:
  DataWriterInterface();
  virtual ~DataWriterInterface();

public:
  virtual int init(const DataWriterConfig &dataWriterConfig) = 0;
  virtual int start() = 0;
  virtual int stop() = 0;

  template <typename MessageT>
  int addMessage(const std::string &topicName,
                 const std::shared_ptr<MessageT> &msgPtr) {
    if (!data_io_config_.writerTopicNames.count(topicName) ||
        msgPtr == nullptr) {
      return 0;
    }

    DataWriterSingleItem::Ptr itemPtr(new DataWriterSingleItem());
    {
      std::lock_guard<std::mutex> lg(buffer_mtx_);
      const uint64_t writeTimestampNs = TIMESTAMP_NS;
      itemPtr->update<MessageT>(topicName, writeTimestampNs, msgPtr);
      buffer_.push(itemPtr);
      buffer_cond_.notify_one();
    }

    return 0;
  }

  template <typename PointT>
  int addMessage(const std::string &topicName,
                 const typename pcl::PointCloud<PointT>::Ptr &msgPtr) {
    if (!data_io_config_.writerTopicNames.count(topicName) ||
        msgPtr == nullptr || !is_buffer_running_) {
      return 0;
    }

    DataWriterSingleItem::Ptr itemPtr(new DataWriterSingleItem());
    {
      std::lock_guard<std::mutex> lg(buffer_mtx_);
      const uint64_t writeTimestampNs = TIMESTAMP_NS;
      itemPtr->update<PointT>(topicName, writeTimestampNs, msgPtr);
      buffer_.push(itemPtr);
      buffer_cond_.notify_one();
    }

    return 0;
  }

  int addMessage(const DataWriterSingleItem::Ptr &msgPtr) {
    if (is_buffer_running_ == true && msgPtr != nullptr) {
      std::lock_guard<std::mutex> lg(buffer_mtx_);
      buffer_.push(msgPtr);
      buffer_cond_.notify_one();
    }
    return 0;
  }

protected:
  virtual std::string name() const { return "DataWriterInterface"; }
  int initAddJpegCoder(const std::string &topicName);
  int initAddH265Coder(const std::string &topicName);
  void h265Callback(const DataWriterSingleItem::Ptr &msgPtr);
  int writeRegisterMessage(const DataWriterSingleItem::Ptr &msgPtr);
  int writeRegisterMessageRos2(const DataWriterSingleItem::Ptr &msgPtr);

protected:
  DataWriterConfig data_io_config_;
  bool is_buffer_running_;
  bool is_write_running_;

protected:
  std::mutex data_topic_infos_mtx_;
  std::map<std::string, DataTopicInfo> data_topic_infos_;

protected:
  // 消息缓冲队列
  std::mutex buffer_mtx_;
  std::condition_variable buffer_cond_;
  std::queue<DataWriterSingleItem::Ptr> buffer_;

  // 消息写队列
  std::mutex write_buffer_mtx_;
  std::condition_variable write_buffer_cond_;
  std::queue<DataWriterSingleItem::Ptr> write_buffer_;

protected:
  std::shared_ptr<rosbag::Bag> bag_ptr_;
  DataIoTimeFormatUtil data_io_time_util_;
#if defined(ROS2_FOUND)
  std::shared_ptr<rosbag2_cpp::Writer> ros2_writer_ptr_;
#endif // defined(ROS2_FOUND)
protected:
  std::map<std::string, robosense::jpeg::JpegCoder::Ptr> jpeg_coder_map_;
  std::map<std::string, DataWriterH265Encoder::Ptr> h265_coder_map_;
};

} // namespace io
} // namespace robosense

#endif // DATAWRITERINTERFACE_H