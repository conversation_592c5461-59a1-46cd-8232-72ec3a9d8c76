#ifndef RSDATAIOCOMMON_H
#define RSDATAIOCOMMON_H
// 用于ROS2的调试
// #define ROS2_FOUND (1)
#define ENABLE_USE_CUSTOM_ROS_MSG (0)
#include "custom_msgs/rs_compressed_image.hpp"
#include "hyper_vision/codec/h265coder.h"
#include "hyper_vision/codec/jpegcoder.h"
#include "hyper_vision/common/common.h"
// 包含后处理的消息
#include "hyper_vision/front_end/front_end.h"
// ROS自定义消息
#include "hyper_vision_msgs/DepthFrame.h"
#include "hyper_vision_msgs/ImageFrame.h"
#include "hyper_vision_msgs/MotionFrame.h"
#include "pcl_conversions/pcl_conversions.h"

#include "rosbag/bag.h"
#include "rosbag/view.h"
#include "sensor_msgs/CompressedImage.h"
#include "sensor_msgs/Image.h"
#include "sensor_msgs/Imu.h"
#include "sensor_msgs/PointCloud2.h"
#if defined(ROS2_FOUND)
#include "rosbag2_cpp/reader.hpp"
#include "rosbag2_cpp/writer.hpp"
#include "rosbag2_transport/reader_writer_factory.hpp"
// 自定义消息
#include "hyper_vision_msgs/msg/depth_frame.hpp"
#include "hyper_vision_msgs/msg/image_frame.hpp"
#include "hyper_vision_msgs/msg/motion_frame.hpp"
#include "pcl_conversions2/pcl_conversions2.h"
#include "rscamera_msg/msg/rs_compressed_image.hpp"
#include "sensor_msgs/msg/compressed_image.hpp"
#include "sensor_msgs/msg/image.hpp"
#include "sensor_msgs/msg/imu.hpp"
#include "sensor_msgs/msg/point_cloud2.hpp"
#endif // defined(ROS2_FOUND)
#include <cstdlib>
#include <filesystem>
#include <memory>
#include <mutex>
#include <numeric>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <queue>
#include <string>

#ifdef _WIN32
#include <stdlib.h> // for _putenv_s, _getenv_s
#endif

namespace robosense {
namespace io {

#define TIMESTAMP_NS                                                           \
  (std::chrono::time_point_cast<std::chrono::nanoseconds>(                     \
       std::chrono::system_clock::now())                                       \
       .time_since_epoch()                                                     \
       .count())
#define TIMESTAMP_US                                                           \
  (std::chrono::time_point_cast<std::chrono::microseconds>(                    \
       std::chrono::system_clock::now())                                       \
       .time_since_epoch()                                                     \
       .count())
#define TIMESTAMP_MS                                                           \
  (std::chrono::time_point_cast<std::chrono::milliseconds>(                    \
       std::chrono::system_clock::now())                                       \
       .time_since_epoch()                                                     \
       .count())
#define TIMESTAMP_S                                                            \
  (std::chrono::time_point_cast<std::chrono::seconds>(                         \
       std::chrono::system_clock::now())                                       \
       .time_since_epoch()                                                     \
       .count())

// 构造数据保存的时间戳的工具类
class DataIoTimeFormatUtil {
public:
  using Ptr = std::shared_ptr<DataIoTimeFormatUtil>;
  using ConstPtr = std::shared_ptr<const DataIoTimeFormatUtil>;

public:
  DataIoTimeFormatUtil() { time_format_ = "%Y-%m-%d %H:%M:%S"; }

  DataIoTimeFormatUtil(const std::string &format) { time_format_ = format; }
  ~DataIoTimeFormatUtil() = default;

public:
  int setTimeFormat(const std::string &format) {
    time_format_ = format;
    return 0;
  }

  std::string currentTimeString() {
    auto time_point = std::chrono::system_clock::now();
    std::time_t time_c = std::chrono::system_clock::to_time_t(time_point);
    std::ostringstream time_stream;
    time_stream << std::put_time(std::localtime(&time_c), time_format_.c_str());
    return time_stream.str();
  }

private:
  std::string time_format_;
};

// 支持的文件格式类型
enum class RS_DATA_FORMAT_TYPE : int {
  RS_DATA_FORMAT_ROS = 0,
  RS_DATA_FORMAT_ROS2,
  RS_DATA_FORMAT_UNKNOWN = 255,
};

// 支持的消息处理类型
enum RS_DATA_MESSAGE_PROCESS_TYPE : int {
  RS_DATA_MESSAGE_PROCESS_NOTHING = 0,
  RS_DATA_MESSAGE_PROCESS_JPEG,
  RS_DATA_MESSAGE_PROCESS_H265,
};

// 支持的话题索引
enum class RS_SUPPORT_TOPICNAME_INDEX : int {
  RS_DATA_TOPICNAME_MOTION_FRAME = 0,
  RS_DATA_TOPICNAME_IMAGE_FRAME,
  RS_DATA_TOPICNAME_DEPTH_FRAME,
  RS_DATA_TOPICNAME_LEFT_IMAGE_FRAME,
  RS_DATA_TOPICNAME_RIGHT_IMAGE_FRAME,
  RS_DATA_TOPICNAME_POINTCLOUD_XYZIRT,
  RS_DATA_TOPICNAME_POINTCLOUD_XYZRGBIRT,
};

// 支持的消息数据类型
enum class RS_SUPPORT_DATA_TYPE : int {
  RS_SUPERSENSOR_MOTION_FRAME = 0,
  RS_SUPERSENSOR_DEPTH_FRAME,
  RS_SUPERSENSOR_IMAGE_FRAME,
  RS_PCL_CLOUD_POINT_RSPOINTXYZIRT,
  RS_PCL_CLOUD_POINT_RSPOINTXYZIRGBIRT,
  RS_SUPPORT_DATATYPE_UNKNOWN = 255,
};

// Ros2环境配置工具类
class Ros2EnvironmentUtil {
public:
  using Ptr = std::shared_ptr<Ros2EnvironmentUtil>;
  using ConstPtr = std::shared_ptr<const Ros2EnvironmentUtil>;

public:
  static int initRos2EnvironmentParams(const std::string &ament_dir_path) {
    RS_IS_ROS2_ENV_SETTING = false;
    RS_ROS2_AMENT_DIR_PATH = ament_dir_path;
    RINFO << "RS_IS_ROS2_ENV_SETTING = " << RS_IS_ROS2_ENV_SETTING
          << ", RS_ROS2_AMENT_DIR_PATH = " << RS_ROS2_AMENT_DIR_PATH;
    return 0;
  }

  static int
  initAppSettingRuntimeDirPath(const std::string &app_runtime_dir_path) {
    RS_APP_SETTING_RUNTIME_DIR_PATH = app_runtime_dir_path;
    RINFO << "RS_APP_SETTING_RUNTIME_DIR_PATH = "
          << RS_APP_SETTING_RUNTIME_DIR_PATH;
    return 0;
  }

  static int setRos2Environment();

  static bool getRos2EnvironmentFlag() {
    return Ros2EnvironmentUtil::RS_IS_ROS2_ENV_SETTING;
  }

  static bool checkAppSettingRuntimeDirPathEmpty() {
    return Ros2EnvironmentUtil::RS_APP_SETTING_RUNTIME_DIR_PATH.empty();
  }

  static std::string getAppSettingRuntimeDirPath() {
    return Ros2EnvironmentUtil::RS_APP_SETTING_RUNTIME_DIR_PATH;
  }

public:
  static bool RS_IS_ROS2_ENV_SETTING;
  static std::string RS_APP_SETTING_RUNTIME_DIR_PATH;
  static std::string RS_ROS2_AMENT_DIR_PATH;
};

class DataTopicNameInfoItem {
public:
  using Ptr = std::shared_ptr<DataTopicNameInfoItem>;
  using ConstPtr = std::shared_ptr<const DataTopicNameInfoItem>;

public:
  DataTopicNameInfoItem() = default;
  ~DataTopicNameInfoItem() = default;

public:
  std::string topicName;
  std::string dataType;
  std::vector<std::string> processTypes;
};

// 支持的话题信息相关的工具类
class DataTopicNameSupportUtil {
public:
  using Ptr = std::shared_ptr<DataTopicNameSupportUtil>;
  using ConstPtr = std::shared_ptr<const DataTopicNameSupportUtil>;

public:
  DataTopicNameSupportUtil() = default;
  ~DataTopicNameSupportUtil() = default;

public:
  static int fromTopicNameToImageSize(const std::string &topicName,
                                      std::pair<uint32_t, uint32_t> &size) {
    auto iterMap = RS_TOPICNAME_IMAGESIZE_MAPPER.find(topicName);
    if (iterMap != RS_TOPICNAME_IMAGESIZE_MAPPER.end()) {
      size = iterMap->second;
      return 0;
    }
    return -1;
  }

  static void addTopicNameImageSize(const std::string &topicName,
                                    const uint32_t imageWidth,
                                    const uint32_t imageHeight) {
    auto iterMap = RS_TOPICNAME_IMAGESIZE_MAPPER.find(topicName);
    if (iterMap == RS_TOPICNAME_IMAGESIZE_MAPPER.end()) {
      RS_TOPICNAME_IMAGESIZE_MAPPER.insert(
          {topicName, {imageWidth, imageHeight}});
    } else {
      iterMap->second = {imageWidth, imageHeight};
    }
  }

public:
  static int fromTopicNameToDataType(const std::string &topicName,
                                     RS_SUPPORT_DATA_TYPE &type) {
    auto iterMap = RS_TOPICNAME_DATATYPE_MAPPER.find(topicName);
    if (iterMap != RS_TOPICNAME_DATATYPE_MAPPER.end()) {
      type = iterMap->second;
      return 0;
    }
    return -1;
  }

  static void addTopicNameDataType(const std::string &topicName,
                                   const RS_SUPPORT_DATA_TYPE pointType) {
    auto iterMap = RS_TOPICNAME_DATATYPE_MAPPER.find(topicName);
    if (iterMap == RS_TOPICNAME_DATATYPE_MAPPER.end()) {
      RS_TOPICNAME_DATATYPE_MAPPER.insert({topicName, pointType});
    } else {
      iterMap->second = pointType;
    }
  }

public:
  static int fromTopicNameToProcessTypes(
      const std::string &topicName,
      std::vector<RS_DATA_MESSAGE_PROCESS_TYPE> &processTypes) {
    auto iterMap = RS_TOPICNAME_PROCESSTYPE_MAPPER.find(topicName);
    if (iterMap != RS_TOPICNAME_PROCESSTYPE_MAPPER.end()) {
      processTypes = iterMap->second;
      return 0;
    }
    return -1;
  }

  static void addTopicNameProcessTypes(
      const std::string &topicName,
      const std::vector<RS_DATA_MESSAGE_PROCESS_TYPE> &processTypes) {

    auto iterMap = RS_TOPICNAME_PROCESSTYPE_MAPPER.find(topicName);
    if (iterMap == RS_TOPICNAME_PROCESSTYPE_MAPPER.end()) {
      RS_TOPICNAME_PROCESSTYPE_MAPPER.insert({topicName, processTypes});
    } else {
      iterMap->second = processTypes;
    }
  }

public:
  // 写数据支持的话题配置
  static void getAllSupportTopicNameInfos(
      std::map<std::string, DataTopicNameInfoItem> &items) {
    items.clear();
    for (auto iterMap = RS_INDEX_TOPICNAME_MAPPER.begin();
         iterMap != RS_INDEX_TOPICNAME_MAPPER.end(); ++iterMap) {
      const std::string &topicName = iterMap->second;
      DataTopicNameInfoItem item;
      item.topicName = topicName;
      item.dataType =
          fromDataTypeToString(RS_TOPICNAME_DATATYPE_MAPPER[topicName]);
      item.processTypes =
          fromProcessTypesToStrings(RS_TOPICNAME_PROCESSTYPE_MAPPER[topicName]);
      items.insert({topicName, item});
    }
  }

public:
  static std::string fromDataTypeToString(const RS_SUPPORT_DATA_TYPE dataType) {
    std::string str = "Unknown";
    switch (dataType) {
    case RS_SUPPORT_DATA_TYPE::RS_SUPERSENSOR_MOTION_FRAME: {
      str = "Imu";
      break;
    }
    case RS_SUPPORT_DATA_TYPE::RS_SUPERSENSOR_DEPTH_FRAME: {
      str = "Depth";
      break;
    }
    case RS_SUPPORT_DATA_TYPE::RS_SUPERSENSOR_IMAGE_FRAME: {
      str = "Image";
      break;
    }
    case RS_SUPPORT_DATA_TYPE::RS_PCL_CLOUD_POINT_RSPOINTXYZIRGBIRT: {
      str = "XYZRGBIRT";
      break;
    }
    case RS_SUPPORT_DATA_TYPE::RS_PCL_CLOUD_POINT_RSPOINTXYZIRT: {
      str = "XYZIRT";
      break;
    }
    default: {
      break;
    }
    }

    return str;
  }

  static RS_SUPPORT_DATA_TYPE
  fromStringToDataType(const std::string &sDataType) {
    if (sDataType == "Imu") {
      return RS_SUPPORT_DATA_TYPE::RS_SUPERSENSOR_MOTION_FRAME;
    } else if (sDataType == "Depth") {
      return RS_SUPPORT_DATA_TYPE::RS_SUPERSENSOR_DEPTH_FRAME;
    } else if (sDataType == "Image") {
      return RS_SUPPORT_DATA_TYPE::RS_SUPERSENSOR_IMAGE_FRAME;
    } else if (sDataType == "XYZRGBIRT") {
      return RS_SUPPORT_DATA_TYPE::RS_PCL_CLOUD_POINT_RSPOINTXYZIRGBIRT;
    } else if (sDataType == "XYZIRT") {
      return RS_SUPPORT_DATA_TYPE::RS_PCL_CLOUD_POINT_RSPOINTXYZIRT;
    } else {
      return RS_SUPPORT_DATA_TYPE::RS_SUPPORT_DATATYPE_UNKNOWN;
    }
  }

public:
  static std::string
  fromProcessTypeToString(const RS_DATA_MESSAGE_PROCESS_TYPE processType) {
    std::string str = "Nothing";
    switch (processType) {
    case RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_H265: {
      str = "H265";
      break;
    }
    case RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_JPEG: {
      str = "Jpeg";
      break;
    }
    default: {
      break;
    }
    }
    return str;
  }

  static RS_DATA_MESSAGE_PROCESS_TYPE
  fromStringToProcessType(const std::string &sProcessType) {
    if (sProcessType == "H265") {
      return RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_H265;
    } else if (sProcessType == "Jpeg") {
      return RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_JPEG;
    } else {
      return RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_NOTHING;
    }
  }

  static std::vector<std::string> fromProcessTypesToStrings(
      const std::vector<RS_DATA_MESSAGE_PROCESS_TYPE> &processTypes) {
    std::vector<std::string> strs;
    for (size_t i = 0; i < processTypes.size(); ++i) {
      strs.push_back(fromProcessTypeToString(processTypes[i]));
    }
    return strs;
  }

  static std::vector<RS_DATA_MESSAGE_PROCESS_TYPE>
  fromStringsToProcessTypes(const std::vector<std::string> &sProcessTypes) {
    std::vector<RS_DATA_MESSAGE_PROCESS_TYPE> processTypes;
    for (size_t i = 0; i < sProcessTypes.size(); ++i) {
      processTypes.push_back(fromStringToProcessType(sProcessTypes[i]));
    }
    return processTypes;
  }

public:
  // 话题索引和话题的关系: 固定
  static std::map<RS_SUPPORT_TOPICNAME_INDEX, std::string>
      RS_INDEX_TOPICNAME_MAPPER;

  // 话题和ImageSize的映射关系: 允许动态更新
  static std::map<std::string, std::pair<uint32_t, uint32_t>>
      RS_TOPICNAME_IMAGESIZE_MAPPER;

  // 话题和数据类型的映射关系: 允许动态更新
  static std::map<std::string, RS_SUPPORT_DATA_TYPE>
      RS_TOPICNAME_DATATYPE_MAPPER;

  // 话题和支持的数据操作的映射关系: 允许动态更新
  static std::map<std::string, std::vector<RS_DATA_MESSAGE_PROCESS_TYPE>>
      RS_TOPICNAME_PROCESSTYPE_MAPPER;
};

// 支持的数据包格式工具类
class DataFormatSupportUtil {
public:
  using Ptr = std::shared_ptr<DataFormatSupportUtil>;
  using ConstPtr = std::shared_ptr<const DataFormatSupportUtil>;

public:
  DataFormatSupportUtil() = default;
  ~DataFormatSupportUtil() = default;

public:
  static std::set<std::string> getSupportDataFormats() {
    std::set<std::string> formats;
    formats.insert(
        fromFormatTypeToString(RS_DATA_FORMAT_TYPE::RS_DATA_FORMAT_ROS));
#if defined(ROS2_FOUND)
    formats.insert(
        fromFormatTypeToString(RS_DATA_FORMAT_TYPE::RS_DATA_FORMAT_ROS2));
#endif // defined(ROS2_FOUND)
    return formats;
  }

public:
  static std::string
  fromFormatTypeToString(const RS_DATA_FORMAT_TYPE dataType) {
    std::string str = "Unknown";
    switch (dataType) {
    case RS_DATA_FORMAT_TYPE::RS_DATA_FORMAT_ROS: {
      str = ".bag";
      break;
    }
    case RS_DATA_FORMAT_TYPE::RS_DATA_FORMAT_ROS2: {
      str = ".db3";
      break;
    }
    default: {
      break;
    }
    }

    return str;
  }

  static RS_DATA_FORMAT_TYPE
  fromStringToFormatType(const std::string &sDataType) {
    if (sDataType == ".bag") {
      return RS_DATA_FORMAT_TYPE::RS_DATA_FORMAT_ROS;
    } else if (sDataType == ".db3") {
      return RS_DATA_FORMAT_TYPE::RS_DATA_FORMAT_ROS2;
    } else {
      return RS_DATA_FORMAT_TYPE::RS_DATA_FORMAT_UNKNOWN;
    }
  }

  static int getDataFormatTypeFromPath(const std::string &sourceDataFilePath,
                                       RS_DATA_FORMAT_TYPE &dataIoSourceType) {
    std::filesystem::path tmpSourceDataFilePath(sourceDataFilePath);

    // 规范化路径，去除末尾的斜杠
    tmpSourceDataFilePath = tmpSourceDataFilePath.lexically_normal();

    if (tmpSourceDataFilePath.empty()) {
      return -1;
    }

    RINFO << "tmpSourceDataFilePath = " << tmpSourceDataFilePath.string()
          << ", sourceDataFilePath = " << sourceDataFilePath;

    // 获取文件扩展名
    std::string extension = tmpSourceDataFilePath.extension().string();

    if (!extension.empty()) {
      if (extension == ".bag") {
        dataIoSourceType = RS_DATA_FORMAT_TYPE::RS_DATA_FORMAT_ROS;
      } else if (extension == ".db3") {
        dataIoSourceType = RS_DATA_FORMAT_TYPE::RS_DATA_FORMAT_ROS2;
      } else {
        dataIoSourceType = RS_DATA_FORMAT_TYPE::RS_DATA_FORMAT_UNKNOWN;
      }
    } else {
      // 如果没有扩展名，检查是否是目录并包含特定文件
      if (std::filesystem::is_directory(tmpSourceDataFilePath)) {
        std::filesystem::path metaFilePath =
            tmpSourceDataFilePath / "metadata.yaml";
        std::filesystem::path db3FilePath =
            tmpSourceDataFilePath /
            (tmpSourceDataFilePath.filename().string() + "_0.db3");

        RINFO << "metaFilePath = " << metaFilePath.string()
              << ", db3FilePath = " << db3FilePath.string();

        if (std::filesystem::exists(metaFilePath) &&
            std::filesystem::exists(db3FilePath)) {
          dataIoSourceType = RS_DATA_FORMAT_TYPE::RS_DATA_FORMAT_ROS2;
        } else {
          dataIoSourceType = RS_DATA_FORMAT_TYPE::RS_DATA_FORMAT_UNKNOWN;
        }
      } else {
        dataIoSourceType = RS_DATA_FORMAT_TYPE::RS_DATA_FORMAT_UNKNOWN;
      }
    }

    return 0;
  }

  static int
  searchRosbagFilePath(const std::string &searchFilePath,
                       const bool is_recursive,
                       std::vector<std::string> &rosbagBothFilePaths) {
    std::filesystem::path tmpSourceDataFilePath(searchFilePath);
    if (!std::filesystem::exists(tmpSourceDataFilePath)) {
      RERROR << "searchFilePath = " << searchFilePath << " Not Exist !";
      return -1;
    } else if (!std::filesystem::is_directory(tmpSourceDataFilePath)) {
      RERROR << "searchFilePath = " << searchFilePath << " Not Directory !";
      return -2;
    }

    RS_DATA_FORMAT_TYPE formatType;
    std::set<std::pair<std::string, RS_DATA_FORMAT_TYPE>> candidate_file_paths;
    for (const auto &entry :
         std::filesystem::directory_iterator(tmpSourceDataFilePath)) {
      const auto &sub_path = entry.path();
      if (is_recursive && std::filesystem::is_directory(sub_path)) {
        std::vector<std::string> subRosbagBothFilePaths;
        int ret = searchRosbagFilePath(sub_path.string(), is_recursive,
                                       subRosbagBothFilePaths);
        if (ret == 0 && !subRosbagBothFilePaths.empty()) {
          rosbagBothFilePaths.insert(rosbagBothFilePaths.end(),
                                     subRosbagBothFilePaths.begin(),
                                     subRosbagBothFilePaths.end());
        }
      } else if (std::filesystem::is_regular_file(sub_path)) {
        int ret = getDataFormatTypeFromPath(sub_path.string(), formatType);
        if (ret == 0 &&
            formatType != RS_DATA_FORMAT_TYPE::RS_DATA_FORMAT_UNKNOWN) {
          candidate_file_paths.insert({sub_path.string(), formatType});
        }
      }
    }

    // 再次进行过滤
    for (auto iterSet = candidate_file_paths.begin();
         iterSet != candidate_file_paths.end(); ++iterSet) {
      const auto &candidate_file_path = *iterSet;
      if (candidate_file_path.second ==
          RS_DATA_FORMAT_TYPE::RS_DATA_FORMAT_ROS) {
        rosbagBothFilePaths.push_back(candidate_file_path.first);
      } else if (candidate_file_path.second ==
                 RS_DATA_FORMAT_TYPE::RS_DATA_FORMAT_ROS2) {
        const std::string &first_db3_file_path = std::string("_0.db3");
        if (candidate_file_path.first.size() >= first_db3_file_path.size()) {
          const std::string &part_db3_file_path =
              candidate_file_path.first.substr(
                  candidate_file_path.first.size() -
                  first_db3_file_path.size());
          if (part_db3_file_path == first_db3_file_path) {
            rosbagBothFilePaths.push_back(candidate_file_path.first);
          }
          RINFO << "part_db3_file_path = " << part_db3_file_path
                << ", candidate_file_path.first = "
                << candidate_file_path.first;
        }
      }
    }

    RINFO << "Search Ros/Ros2 File Successed: searchFilePath " << searchFilePath
          << ", is_recursive = " << is_recursive;
    for (size_t i = 0; i < rosbagBothFilePaths.size(); ++i) {
      RINFO << "Search File Path = " << rosbagBothFilePaths[i];
    }

    return 0;
  }
};

// 数据包话题信息工具类
class DataTopicInfoUtil {
public:
  using Ptr = std::shared_ptr<DataTopicInfoUtil>;
  using ConstPtr = std::shared_ptr<const DataTopicInfoUtil>;

public:
  DataTopicInfoUtil() = default;
  ~DataTopicInfoUtil() = default;

public:
  static int getDataTopicInfo(const std::string &dataFilePath,
                              std::map<std::string, std::string> &topic_types,
                              std::pair<uint64_t, uint64_t> &dataTimepoints) {

    RS_DATA_FORMAT_TYPE dataFormatType;
    int ret = DataFormatSupportUtil::getDataFormatTypeFromPath(dataFilePath,
                                                               dataFormatType);
    if (ret != 0) {
      RERROR << "DataTopicInfoUtil: From Path = " << dataFilePath
             << " To Parse File Format Failed: ret = " << ret;
      return -1;
    }

    ret = 0;
    switch (dataFormatType) {
    case RS_DATA_FORMAT_TYPE::RS_DATA_FORMAT_ROS: {
      ret = getDataTopicInfoRos(dataFilePath, topic_types, dataTimepoints);
      break;
    }
    case RS_DATA_FORMAT_TYPE::RS_DATA_FORMAT_ROS2: {
      ret = getDataTopicInfoRos2(dataFilePath, topic_types, dataTimepoints);
      break;
    }
    default: {
      RERROR << "DataTopicInfoUtil: Not Support Data Format: "
             << static_cast<int>(dataFormatType);
      return -2;
    }
    }

    if (ret != 0) {
      RERROR << "DataTopicInfoUtil: Get Topic Info From File = " << dataFilePath
             << ", Data Format = " << static_cast<int>(dataFormatType)
             << " Failed: ret = " << ret;
      return -3;
    }

    return 0;
  }

  static int
  getDataTopicInfoRos(const std::string &rosbagFilePath,
                      std::map<std::string, std::string> &topic_types,
                      std::pair<uint64_t, uint64_t> &rosbagTimepoints) {
    std::shared_ptr<rosbag::Bag> bagPtr;
    try {
      bagPtr.reset(new rosbag::Bag(rosbagFilePath, rosbag::BagMode::Read));
    } catch (...) {
      RERROR << "DataTopicInfoUtil: Malloc Rosbag Failed: " << rosbagFilePath;
      return -1;
    }

    if (!bagPtr->isOpen()) {
      RERROR << "DataTopicInfoUtil: Rosbag File Open Failed: "
             << rosbagFilePath;
      return -2;
    }

    std::shared_ptr<rosbag::View> viewPtr;
    try {
      viewPtr.reset(new rosbag::View(*bagPtr));
    } catch (...) {
      RERROR << "DataTopicInfoUtil: Malloc Rosbag View Failed: "
             << rosbagFilePath;
      return -3;
    }

    auto rosbagConnects = viewPtr->getConnections();
    for (size_t i = 0; i < rosbagConnects.size(); ++i) {
      auto rosbagConnect = rosbagConnects[i];
      const std::string &topicName = rosbagConnect->topic;
      const std::string &dataType = rosbagConnect->datatype;

      topic_types.insert({topicName, dataType});
    }

    rosbagTimepoints.first = viewPtr->getBeginTime().toNSec();
    rosbagTimepoints.second = viewPtr->getEndTime().toNSec();

    return 0;
  }

  static int
  getDataTopicInfoRos2(const std::string &db3FileDirPath,
                       std::map<std::string, std::string> &topic_types,
                       std::pair<uint64_t, uint64_t> &db3Timepoints) {
#if defined(ROS2_FOUND)
    std::shared_ptr<rosbag2_cpp::Reader> readerPtr;
    try {
      readerPtr.reset(new rosbag2_cpp::Reader());
    } catch (...) {
      RERROR << "DataTopicInfoUtil: Malloc rosbag2_cpp::Reader Failed: "
             << db3FileDirPath;
      return -1;
    }

    try {
      readerPtr->open(db3FileDirPath);
    } catch (...) {
      RERROR << "DataTopicInfoUtil: Open Ros2 File To Read "
                "Failed: "
             << db3FileDirPath;
      return -2;
    }

    const rosbag2_storage::BagMetadata &bagMetadata = readerPtr->get_metadata();
    for (size_t i = 0; i < bagMetadata.topics_with_message_count.size(); ++i) {
      const std::string &topicName =
          bagMetadata.topics_with_message_count[i].topic_metadata.name;
      const std::string &dataType =
          bagMetadata.topics_with_message_count[i].topic_metadata.type;

      topic_types.insert({topicName, dataType});
    }

    uint64_t beginTimepoint =
        std::chrono::duration_cast<std::chrono::nanoseconds>(
            bagMetadata.starting_time.time_since_epoch())
            .count();
    uint64_t endTimepoint = beginTimepoint + bagMetadata.duration.count();

    // 获取文件的时间信息
    db3Timepoints = std::pair<uint64_t, uint64_t>{beginTimepoint, endTimepoint};
    return 0;
#else
    RERROR << "DataTopicInfoUtil: Not Support ROS2 !";
    return -1;
#endif // defined(ROS2_FOUND)
  }
};

// Data Reader Config
class DataReaderConfig {
public:
  using Ptr = std::shared_ptr<DataReaderConfig>;
  using ConstPtr = std::shared_ptr<DataReaderConfig>;

public:
  DataReaderConfig() { reset(); };
  ~DataReaderConfig() = default;

public:
  void reset() {
    sourceDataFilePath.clear();
    mainSyncTopicName.clear();
    preLoadMainSyncCnt = 11;
    readTopicNames.clear();
    readTopicTimeRange = std::pair<int64_t, int64_t>{-1, -1};
  }

public:
  // 从文件名称分离文件类型
  int getDataFormatType(RS_DATA_FORMAT_TYPE &dataIoSourceType);

public:
  std::string sourceDataFilePath;
  std::string mainSyncTopicName;
  uint32_t preLoadMainSyncCnt;
  std::set<std::string> readTopicNames;
  std::pair<int64_t, int64_t> readTopicTimeRange;
};

class DataWriterConfig {
public:
  using Ptr = std::shared_ptr<DataWriterConfig>;
  using ConstPtr = std::shared_ptr<const DataWriterConfig>;

public:
  DataWriterConfig() { reset(); }
  ~DataWriterConfig() = default;

public:
  void reset() {
    writerTopicNames.clear();
    dataDirPath.clear();
    dataIoSourceType = RS_DATA_FORMAT_TYPE::RS_DATA_FORMAT_UNKNOWN;
    topicProcessTypes.clear();
  }

public:
  std::set<std::string> writerTopicNames;
  std::string dataDirPath;
  RS_DATA_FORMAT_TYPE dataIoSourceType;
  std::map<std::string, RS_DATA_MESSAGE_PROCESS_TYPE> topicProcessTypes;
};

class DataTopicInfo {
public:
  using Ptr = std::shared_ptr<DataTopicInfo>;
  using ConstPtr = std::shared_ptr<const DataTopicInfo>;

public:
  DataTopicInfo() = default;
  ~DataTopicInfo() = default;

public:
  std::string topicName;
  uint32_t messageCnt;
  uint64_t beginTimestampNs;
  uint64_t endTimestampNs;
  uint64_t durationTimestampNs;
};

} // namespace io
} // namespace robosense

#endif // RSDATAIOCOMMON_H
