#ifndef DATAREADERINTERFACE_H
#define DATAREADERINTERFACE_H

#include <any>

#include "hyper_vision/dataiomanager/dataiocommon.h"
#include "sensor_msgs/CompressedImage.h"
#include "sensor_msgs/PointCloud2.h"

namespace robosense {
namespace io {

// Reader的回调函数类型
using DATA_DEPTH_FRAME_CALLBACK =
    std::function<void(const std::string &,
                       const std::shared_ptr<robosense::common::DepthFrame> &)>;
using DATA_MOTION_FRAME_CALLBACK = std::function<void(
    const std::string &,
    const std::shared_ptr<robosense::common::MotionFrame> &)>;
using DATA_IMAGE_FRAME_CALLBACK =
    std::function<void(const std::string &,
                       const std::shared_ptr<robosense::common::ImageFrame> &)>;
using DATA_POINTCLOUDXYZRGBIRT_CALLBACK = std::function<void(
    const std::string &, const pcl::PointCloud<RsPointXYZRGBIRT>::Ptr &)>;
using DATA_POINTCLOUDXYZIRT_CALLBACK = std::function<void(
    const std::string &, const pcl::PointCloud<RsPointXYZIRT>::Ptr &)>;

class DataReaderSingleItem {
public:
  using Ptr = std::shared_ptr<DataReaderSingleItem>;
  using ConstPtr = std::shared_ptr<const DataReaderSingleItem>;

public:
  DataReaderSingleItem() { reset(); }
  ~DataReaderSingleItem() = default;

public:
  void reset() {
    timestamp_ns_ = 0;
    topicname_id_ = -1;
    is_belong_ = true;
    is_ready_ = false;
    is_use_org_message_ = true;
    process_type_ =
        RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_NOTHING;
    org_message_ = nullptr;
    message_ = nullptr;
  }

public:
  uint64_t timestamp_ns_;
  int32_t topicname_id_;
  bool is_belong_;
  bool is_ready_;
  bool is_use_org_message_;
  RS_DATA_MESSAGE_PROCESS_TYPE process_type_;
  std::shared_ptr<std::any> org_message_;
  std::shared_ptr<std::any> message_;
};

class DataReaderH265Decoder {
public:
  using Ptr = std::shared_ptr<DataReaderH265Decoder>;
  using ConstPtr = std::shared_ptr<const DataReaderH265Decoder>;

public:
  using RS_DATA_READ_H265_CALLBACK =
      std::function<void(const DataReaderSingleItem::Ptr)>;

  enum class RS_DATA_READ_H265_CODE_MODE : int {
    RS_DATA_READ_H265_CODE_UNKNOWN = 0,
    RS_DATA_READ_H265_CODE_SYNC,
    RS_DATA_READ_H265_CODE_ASYNC,
  };

public:
  DataReaderH265Decoder() {
    codeMode_ = RS_DATA_READ_H265_CODE_MODE::RS_DATA_READ_H265_CODE_UNKNOWN;
    h265CoderPtr_ = nullptr;
  }

  ~DataReaderH265Decoder() = default;

public:
  int init(const std::string &topicName,
           const robosense::h265::H265CodesConfig &h265CodesConfig,
           const RS_DATA_READ_H265_CALLBACK &callback) {
    if (topicName.empty()) {
      RERROR << "DataReaderH265Decoder: topicName is Empty !";
      return -1;
    }

    if (callback == nullptr) {
      codeMode_ = RS_DATA_READ_H265_CODE_MODE::RS_DATA_READ_H265_CODE_SYNC;
    } else {
      codeMode_ = RS_DATA_READ_H265_CODE_MODE::RS_DATA_READ_H265_CODE_ASYNC;
    }

    topicName_ = topicName;
    h265CodesConfig_ = h265CodesConfig;
    callback_ = callback;

    int ret = init();
    if (ret != 0) {
      RERROR << "DataReaderH265Decoder: initial failed: topicName = "
             << topicName_ << ", ret = " << ret;
      return -2;
    }

    return 0;
  }

  // 重新进行初始化
  int resetInit() {
    int ret = init();
    if (ret != 0) {
      RERROR << "DataReaderH265Decoder: re-initial failed: topicName = "
             << topicName_ << ", ret = " << ret;
      return -1;
    }
    return 0;
  }

  RS_DATA_READ_H265_CODE_MODE dataWriteH265CodeMode() const {
    return codeMode_;
  }

  int syncAddMessage(DataReaderSingleItem::Ptr &singleItemPtr,
                     const bool isRosFormat) {
    if (codeMode_ != RS_DATA_READ_H265_CODE_MODE::RS_DATA_READ_H265_CODE_SYNC) {
      RERROR << "DataReaderH265Decoder: Initial Code Mode Is Not SYNC: "
                "topicName = "
             << topicName_;
      return -1;
    } else if (singleItemPtr == nullptr ||
               singleItemPtr->org_message_ == nullptr) {
      RERROR
          << "DataReaderH265Decoder: Input H265 Decode Is Nullptr: topicName = "
          << topicName_;
      return -2;
    }

    // RINFO << "RUN HERE +++: ";
    int ret = 0;
    robosense::h265::H265Coder::CODER_BUFFER_PTR_VEC vecBufferPtr;
    if (isRosFormat) {
      auto rosMsgPtr = std::any_cast<rscamera_msg::RsCompressedImage::Ptr>(singleItemPtr->org_message_);
      if (rosMsgPtr == nullptr) {
        RERROR << "DataReaderH265Decoder: AnyCast To "
                  "rscamera_msg::RsCompressedImage::Ptr Failed: topicName = "
               << topicName_;
        return -3;
      } else if (rosMsgPtr == nullptr) {
        RERROR
            << "DataReaderH265Decoder: AnyCast "
               "rscamera_msg::RsCompressedImage::Ptr is Nullptr: topicName = "
            << topicName_;
        return -4;
      }

      // 进行H265解码
      const auto &rosMsgPtr2 = rosMsgPtr;
      ret = h265CoderPtr_->decode(rosMsgPtr2->data.data(),
                                  rosMsgPtr2->data.size(), vecBufferPtr);
      if (ret != 0) {
        RERROR << "DataReaderH265Decoder: H265 Decode Failed: topicName = "
               << topicName_ << ", ret = " << ret;
        return -5;
      }

      if (vecBufferPtr.size() != 1) {
        RERROR << "DataReaderH265Decoder: SYNC H265 Decode Failed: topicName = "
               << topicName_ << ", ret = " << ret;
        return -6;
      }

      ret = createH265Message(singleItemPtr, vecBufferPtr[0]);
    } else {
#if defined(ROS2_FOUND)
      rscamera_msg::msg::RsCompressedImage::SharedPtr *rosMsgPtr =
          singleItemPtr->org_message_
              ->AnyCast<rscamera_msg::msg::RsCompressedImage::SharedPtr>();
      if (rosMsgPtr == nullptr) {
        RERROR << "DataReaderH265Decoder: AnyCast To "
                  "rscamera_msg::msg::RsCompressedImage::SharedPtr Failed: "
                  "topicName = "
               << topicName_;
        return -3;
      } else if (*rosMsgPtr == nullptr) {
        RERROR << "DataReaderH265Decoder: AnyCast "
                  "rscamera_msg::msg::RsCompressedImage::SharedPtr is Nullptr: "
                  "topicName = "
               << topicName_;
        return -4;
      }

      // 进行H265解码
      const auto &rosMsgPtr2 = *rosMsgPtr;
      ret = h265CoderPtr_->decode(rosMsgPtr2->data.data(),
                                  rosMsgPtr2->data.size(), vecBufferPtr);
      if (ret != 0) {
        RERROR << "DataReaderH265Decoder: H265 Decode Failed: topicName = "
               << topicName_ << ", ret = " << ret;
        return -5;
      }

      if (vecBufferPtr.size() != 1) {
        RERROR << "DataReaderH265Decoder: SYNC H265 Decode Failed: topicName = "
               << topicName_ << ", ret = " << ret;
        return -6;
      }

      ret = createH265MessageRos2(singleItemPtr, vecBufferPtr[0]);
#else
      RERROR << "DataReaderH265Decoder: ROS2 Not Support !";
      ret = -1;
#endif // defined(ROS2_FOUND)
    }
    if (ret != 0) {
      RERROR << "DataReaderH265Decoder: SYNC H265 Decode Create Message "
                "Failed: topicName = "
             << topicName_ << ", ret = " << ret;
      return -7;
    }

    return 0;
  }

  int asyncAddMessage(const DataReaderSingleItem::Ptr &singleItemPtr,
                      const bool isRosFormat) {
    if (codeMode_ !=
        RS_DATA_READ_H265_CODE_MODE::RS_DATA_READ_H265_CODE_ASYNC) {
      RERROR << "DataReaderH265Decoder: Initial Code Mode Is Not ASYNC: "
                "topicName = "
             << topicName_;
      return -1;
    } else if (singleItemPtr == nullptr ||
               singleItemPtr->org_message_ == nullptr) {
      RERROR << "DataReaderH265Decoder: addMessage is Nullptr: topicName = "
             << topicName_;
      return -2;
    }

    int ret = 0;
    robosense::h265::H265Coder::CODER_BUFFER_PTR_VEC vecBufferPtr;
    if (isRosFormat) {
      auto rosMsgPtr = std::any_cast<rscamera_msg::RsCompressedImage::Ptr>(singleItemPtr->org_message_);
      if (rosMsgPtr == nullptr) {
        RERROR << "DataReaderH265Decoder: AnyCast To "
                  "rscamera_msg::RsCompressedImage::Ptr Failed: topicName = "
               << topicName_;
        return -3;
      } else if (rosMsgPtr == nullptr) {
        RERROR
            << "DataReaderH265Decoder: AnyCast "
               "rscamera_msg::RsCompressedImage::Ptr is Nullptr: topicName = "
            << topicName_;
        return -4;
      }

      // 进行H265解码
      const auto &rosMsgPtr2 = rosMsgPtr;
      ret = h265CoderPtr_->decode(rosMsgPtr2->data.data(),
                                  rosMsgPtr2->data.size(), vecBufferPtr);
      if (ret != 0) {
        RERROR << "DataReaderH265Decoder: H265 Decode Failed: topicName = "
               << topicName_ << ", ret = " << ret;
        return -5;
      }
    } else {
#if defined(ROS2_FOUND)
      rscamera_msg::msg::RsCompressedImage::SharedPtr *rosMsgPtr =
          singleItemPtr->org_message_
              ->AnyCast<rscamera_msg::msg::RsCompressedImage::SharedPtr>();
      if (rosMsgPtr == nullptr) {
        RERROR << "DataReaderH265Decoder: AnyCast To "
                  "rscamera_msg::msg::RsCompressedImage::SharedPtr Failed: "
                  "topicName = "
               << topicName_;
        return -3;
      } else if (*rosMsgPtr == nullptr) {
        RERROR << "DataReaderH265Decoder: AnyCast "
                  "rscamera_msg::msg::RsCompressedImage::SharedPtr is Nullptr: "
                  "topicName = "
               << topicName_;
        return -4;
      }

      // 进行H265解码
      const auto &rosMsgPtr2 = *rosMsgPtr;
      ret = h265CoderPtr_->decode(rosMsgPtr2->data.data(),
                                  rosMsgPtr2->data.size(), vecBufferPtr);
      if (ret != 0) {
        RERROR << "DataReaderH265Decoder: H265 Decode Failed: topicName = "
               << topicName_ << ", ret = " << ret;
        return -5;
      }
#else
      RERROR << "DataReaderH265Decoder: ROS2 Not Support !";
      return -5;
#endif // defined(ROS2_FOUND)
    }

    // RINFO << "topicName = " << topicName_
    //       << ": wxh = " << h265CodesConfig_.imgWidth << "x"
    //       << h265CodesConfig_.imgHeight
    //       << ", is IFrame = " << (rosMsgPtr2->type == 1);

    // 保存到队列中
    std::lock_guard<std::mutex> lg(bufferMsgsMtx_);
    bufferMsgs_.push_back(singleItemPtr);

    for (size_t i = 0; i < vecBufferPtr.size(); ++i) {
      const auto &bufferPtr = vecBufferPtr[i];
      auto &itemPtr = *(bufferMsgs_.begin());
      if (isRosFormat) {
        ret = createH265Message(itemPtr, bufferPtr);
      } else {
#if defined(ROS2_FOUND)
        ret = createH265MessageRos2(itemPtr, bufferPtr);
#else
        RERROR << "DataReaderH265Decoder: ROS2 Not Support !";
        return -1;
#endif // defined(ROS2_FOUND)
      }
      if (ret != 0) {
        RERROR
            << "DataReaderH265Decoder: Create H265 Message Failed: topicName = "
            << topicName_ << ", ret = " << ret;
        return -6;
      }

      if (callback_) {
        callback_(itemPtr);
      }

      bufferMsgs_.erase(bufferMsgs_.begin());
    }

    return 0;
  }

  int asyncFlushMessage(const bool isRosFormat) {
    if (codeMode_ !=
        RS_DATA_READ_H265_CODE_MODE::RS_DATA_READ_H265_CODE_ASYNC) {
      RERROR << "DataReaderH265Decoder: Initial Code Mode Is Not ASYNC: "
                "topicName = "
             << topicName_;
      return -1;
    }

    robosense::h265::H265Coder::CODER_BUFFER_PTR_VEC vecBufferPtr;
    int ret = h265CoderPtr_->decodeFlush(vecBufferPtr);
    if (ret != 0) {
      RERROR << "DataReaderH265Decoder: H265 Decode Flush Failed: topicName = "
             << topicName_ << ", ret = " << ret;
      return -2;
    }

    std::lock_guard<std::mutex> lg(bufferMsgsMtx_);
    if (vecBufferPtr.size() != bufferMsgs_.size()) {
      RERROR << "DataReaderH265Decoder: H265 Decode Frame Count Not Match: "
                "topicName = "
             << topicName_;
      return -3;
    }

    for (size_t i = 0; i < vecBufferPtr.size(); ++i) {
      const auto &bufferPtr = vecBufferPtr[i];
      auto &itemPtr = *(bufferMsgs_.begin());

      if (isRosFormat) {
        ret = createH265Message(itemPtr, bufferPtr);
      } else {
#if defined(ROS2_FOUND)
        ret = createH265MessageRos2(itemPtr, bufferPtr);
#else
        RERROR << "DataReaderH265Decoder: ROS2 Not Support !";
        ret = -1;
#endif // defined(ROS2_FOUND)
      }
      if (ret != 0) {
        RERROR
            << "DataReaderH265Decoder: Create H265 Message Failed: topicName = "
            << topicName_ << ", ret = " << ret;
        return -4;
      }

      if (callback_) {
        callback_(itemPtr);
      }
      bufferMsgs_.erase(bufferMsgs_.begin());
    }

    return 0;
  }

private:
  int init() {
    try {
      h265CoderPtr_.reset(new robosense::h265::H265Coder());
    } catch (...) {
      RERROR << "DataReaderH265Decoder: Malloc H265 Coder Failed !";
      return -1;
    }

    int ret = h265CoderPtr_->init(h265CodesConfig_);
    if (ret != 0) {
      RERROR << "DataReaderH265Decoder: H265 Coder Initial Failed: topicName = "
             << topicName_ << ", ret = " << ret;
      return -2;
    }

    bufferMsgs_.clear();

    return 0;
  }

  int createH265Message(
      DataReaderSingleItem::Ptr &singleItemPtr,
      const robosense::h265::H265Coder::CODER_BUFFER_PTR &bufferPtr) {

    auto rosMsgPtr = std::any_cast<rscamera_msg::RsCompressedImage::Ptr>(singleItemPtr->org_message_);
    if (rosMsgPtr == nullptr) {
      RERROR << "DataReaderH265Decoder: Create H265 Decode Message AnyCast "
                "Failed: topicName = "
             << topicName_;
      return -1;
    } else if (rosMsgPtr == nullptr) {
      RERROR << "DataReaderH265Decoder: Create H265 Decode Message AnyCast Is "
                "Nullptr: topicName = "
             << topicName_;
      return -2;
    }

    const auto &rosMsgPtr2 = rosMsgPtr;
    const std_msgs::Header &header = rosMsgPtr2->header;

    // 构造新的消息
    std::shared_ptr<robosense::common::ImageFrame> imageFramePtr(
        new robosense::common::ImageFrame());

    imageFramePtr->capture_time.tv_sec = header.stamp.sec;
    imageFramePtr->capture_time.tv_usec = header.stamp.nsec / 1000;

    imageFramePtr->frame_format =
        robosense::common::ImageFrameFormat::FRAME_FORMAT_RGB24;
    imageFramePtr->width = h265CodesConfig_.imgWidth;
    imageFramePtr->height = h265CodesConfig_.imgHeight;
    imageFramePtr->sequence = header.seq;
    imageFramePtr->step = imageFramePtr->width * 3;
    imageFramePtr->data_bytes = bufferPtr->size();
    imageFramePtr->data =
        std::shared_ptr<uint8_t>(new uint8_t[imageFramePtr->data_bytes],
                                 std::default_delete<uint8_t[]>());
    memcpy(imageFramePtr->data.get(), bufferPtr->data(), bufferPtr->size());
    singleItemPtr->message_.reset(new std::any(imageFramePtr));

    return 0;
  }

#if defined(ROS2_FOUND)
  int createH265MessageRos2(
      DataReaderSingleItem::Ptr &singleItemPtr,
      const robosense::h265::H265Coder::CODER_BUFFER_PTR &bufferPtr) {

    rscamera_msg::msg::RsCompressedImage::SharedPtr *rosMsgPtr =
        singleItemPtr->org_message_
            ->AnyCast<rscamera_msg::msg::RsCompressedImage::SharedPtr>();
    if (rosMsgPtr == nullptr) {
      RERROR << "DataReaderH265Decoder: Create H265 Decode Message AnyCast "
                "Failed: topicName = "
             << topicName_;
      return -1;
    } else if (*rosMsgPtr == nullptr) {
      RERROR << "DataReaderH265Decoder: Create H265 Decode Message AnyCast Is "
                "Nullptr: topicName = "
             << topicName_;
      return -2;
    }

    const auto &rosMsgPtr2 = *rosMsgPtr;
    const std_msgs::msg::Header &header = rosMsgPtr2->header;

    // 构造新的消息
    std::shared_ptr<robosense::common::ImageFrame> imageFramePtr(
        new robosense::common::ImageFrame());

    imageFramePtr->capture_time.tv_sec = header.stamp.sec;
    imageFramePtr->capture_time.tv_usec = header.stamp.nanosec / 1000;

    imageFramePtr->frame_format =
        robosense::common::ImageFrameFormat::FRAME_FORMAT_RGB24;
    imageFramePtr->width = h265CodesConfig_.imgWidth;
    imageFramePtr->height = h265CodesConfig_.imgHeight;
    imageFramePtr->sequence = 0;
    imageFramePtr->step = imageFramePtr->width * 3;
    imageFramePtr->data_bytes = bufferPtr->size();
    imageFramePtr->data =
        std::shared_ptr<uint8_t>(new uint8_t[imageFramePtr->data_bytes],
                                 std::default_delete<uint8_t[]>());
    memcpy(imageFramePtr->data.get(), bufferPtr->data(), bufferPtr->size());
    singleItemPtr->message_.reset(new rally::Any(imageFramePtr));

    return 0;
  }
#endif // defined(ROS2_FOUND)

private:
  std::string topicName_;
  robosense::h265::H265CodesConfig h265CodesConfig_;
  RS_DATA_READ_H265_CALLBACK callback_;
  robosense::h265::H265Coder::Ptr h265CoderPtr_;

  std::mutex bufferMsgsMtx_;
  std::vector<DataReaderSingleItem::Ptr> bufferMsgs_;

  RS_DATA_READ_H265_CODE_MODE codeMode_;
};

enum class RS_DATA_READ_PLAY_STATUS : int {
  RS_DATA_READ_PLAY_PLAYING = 0,
  RS_DATA_READ_PLAY_SINGLE_PLAY,
  RS_DATA_READ_PLAY_PAUSE,
};

class DataReaderInterface {
public:
  using Ptr = std::shared_ptr<DataReaderInterface>;
  using ConstPtr = std::shared_ptr<const DataReaderInterface>;

public:
  DataReaderInterface();
  virtual ~DataReaderInterface();

protected:
  class RosbagMainIndexInfo {
  public:
    using Ptr = std::shared_ptr<RosbagMainIndexInfo>;
    using ConstPtr = std::shared_ptr<const RosbagMainIndexInfo>;

  public:
    RosbagMainIndexInfo() { reset(); }

    ~RosbagMainIndexInfo() {}

  public:
    void reset() {
      timestamp = 0;
      mainSyncIndex = -1;
      attachH265IFrameIndexs.clear();
      next_timestamp = 0;
    }

  public:
    uint64_t timestamp;
    int32_t mainSyncIndex;
    std::map<std::string, int32_t> attachH265IFrameIndexs;
    uint64_t next_timestamp;
  };

  class RosbagIndexInfo {
  public:
    using Ptr = std::shared_ptr<RosbagIndexInfo>;
    using ConstPtr = std::shared_ptr<const RosbagIndexInfo>;

  public:
    void reset() {
      timestamp = 0;
      topicname_id = 0;
    }

  public:
    uint64_t timestamp;
    uint32_t topicname_id;
  };

  enum class RS_ROSBAG_FRAME_READ_TYPE : int {
    RS_ROSBAG_FRAME_READ_UNKNOWN = 0,
    RS_ROSBAG_FRAME_READ_ORDER,
    RS_ROSBAG_FRAME_READ_SKIP,
  };

  enum class RS_ROSBAG_FRAME_CONSUME_TYPE : int {
    RS_ROSBAG_FRAME_CONSUME_UNKNOWN = 0,
    RS_ROSBAG_FRAME_CONSUME_PLAY,
    RS_ROSBAG_FRAME_CONSUME_PAUSE,
  };

  class RosbagFrameReadOp {
  public:
    using Ptr = std::shared_ptr<RosbagFrameReadOp>;
    using ConstPtr = std::shared_ptr<const RosbagFrameReadOp>;

  public:
    RosbagFrameReadOp() { reset(); }
    ~RosbagFrameReadOp() = default;

  public:
    void reset() {
      read_main_index_ = -1;
      read_type_ = RS_ROSBAG_FRAME_READ_TYPE::RS_ROSBAG_FRAME_READ_UNKNOWN;
    }

  public:
    int32_t read_main_index_;
    RS_ROSBAG_FRAME_READ_TYPE read_type_;
  };

  class RosbagFrameConsumeOp {
  public:
    using Ptr = std::shared_ptr<RosbagFrameConsumeOp>;
    using ConstPtr = std::shared_ptr<const RosbagFrameConsumeOp>;

  public:
    RosbagFrameConsumeOp() { reset(); }
    ~RosbagFrameConsumeOp() = default;

  public:
    void reset() {
      consume_main_index_ = -1;
      consume_type_ =
          RS_ROSBAG_FRAME_CONSUME_TYPE::RS_ROSBAG_FRAME_CONSUME_UNKNOWN;
    }

  public:
    int32_t consume_main_index_;
    RS_ROSBAG_FRAME_CONSUME_TYPE consume_type_;
  };

public:
  virtual int init(const DataReaderConfig &dataIoConfig) = 0;
  // 跳帧: 例如下一帧: skipFrame(1); 上一帧: skipFrame(-1);
  virtual int skipFrame(int skip) = 0;
  virtual int play() = 0;
  virtual int pause() = 0;
  virtual int stop() = 0;

public:
  int registerCallback(const DATA_DEPTH_FRAME_CALLBACK &callback);
  int registerCallback(const DATA_MOTION_FRAME_CALLBACK &callback);
  int registerCallback(const DATA_IMAGE_FRAME_CALLBACK &callback);
  int registerCallback(const DATA_POINTCLOUDXYZRGBIRT_CALLBACK &callback);
  int registerCallback(const DATA_POINTCLOUDXYZIRT_CALLBACK &callback);
  // 数据包的时长信息
  uint64_t getTotalDuration();
  uint64_t getBeginTimestamp();
  uint64_t getEndTimestamp();
  virtual uint64_t getCurrentTimestamp() = 0;
  // 从零开始编号
  virtual int32_t getCurrentFrameIndex() = 0;
  // 数据包总帧数
  uint32_t getTotalFrameCount();
  // 是否为连续播放状态
  bool checkIsPlaying();
  // 判断是否播放到最后一帧
  bool checkIsPlayFinish(); 
  uint32_t getInitProgress();

protected:
  virtual std::string name() const { return "DataReaderInterface"; }
  virtual std::pair<int32_t, int32_t> getIndexRange(const int current_index,
                                                    const int total_index) {
    int begin_index = current_index - preload_main_frame_cnt_ / 2;
    int end_index = current_index + preload_main_frame_cnt_ / 2;

    if (begin_index < 0 && end_index >= total_index) {
      return std::pair<int32_t, int32_t>{0, total_index - 1};
    } else if (begin_index < 0) {
      return std::pair<int32_t, int32_t>{
          0, std::min(total_index - 1,
                      current_index + preload_main_frame_cnt_ - 1)};
    } else if (end_index < 0) {
      return std::pair<int32_t, int32_t>{
          std::max(0, total_index - preload_main_frame_cnt_ - 1),
          total_index - 1};
    } else {
      return std::pair<int32_t, int32_t>{begin_index, end_index};
    }
  }

protected:
  int initAddJpegCoder(const std::string &topicName);
  int initAddH265Coder(const std::string &topicName);
  int reInitAddH265Coder();
  int sendRegisterMessage(const DataReaderSingleItem::Ptr &msgPtr);
  int sendRegisterMessage(const std::string &topicName,
                          const std::shared_ptr<std::any> &messagePtr);
  virtual RS_ROSBAG_FRAME_READ_TYPE
  checkRosbagFrameReadType(const int32_t readMainFrameIndex) = 0;

protected:
  DataReaderConfig data_io_config_;

protected:
  std::mutex play_status_mtx_;
  RS_DATA_READ_PLAY_STATUS play_status_;

protected:
  uint64_t total_duration_ns_;
  uint64_t begin_timestamp_ns_;
  uint64_t end_timestamp_ns_;

  std::map<std::string, robosense::jpeg::JpegCoder::Ptr> jpeg_coder_map_;

  std::mutex h265_coder_map_mtx_;
  std::map<std::string, DataReaderH265Decoder::Ptr> h265_coder_map_;

protected:
  DATA_DEPTH_FRAME_CALLBACK depth_frame_cb_;
  DATA_MOTION_FRAME_CALLBACK motion_frame_cb_;
  DATA_IMAGE_FRAME_CALLBACK image_frame_cb_;
  DATA_POINTCLOUDXYZRGBIRT_CALLBACK pointcloudxyzrgbirt_cb_;
  DATA_POINTCLOUDXYZIRT_CALLBACK pointcloudxyzirt_cb_;

  std::map<std::string, int32_t> topicname_id_mapper_;
  std::map<int32_t, std::string> id_topicname_mapper_;
  std::map<std::string, std::string> topicname_datatype_mapper_;
  
  // 过滤的话题
  std::set<std::string> topicname_filters_;

protected:
  int32_t total_frame_cnt_;
  uint64_t cur_timestamp_ns_;
  DataIoTimeFormatUtil data_io_time_util_;
  uint32_t init_progress_;

protected:
  int32_t preload_main_frame_cnt_ = 11;

protected:
  std::vector<RosbagMainIndexInfo> main_sync_index_;
  // 数据回调发送线程
  bool is_consume_running_;
  std::mutex processed_message_map_mtx_;
  std::condition_variable processed_message_map_cond_;
  std::queue<RosbagFrameConsumeOp> consume_queue_;
  std::map<int32_t, std::map<uint64_t, DataReaderSingleItem::Ptr>>
      processed_message_map_;
  int32_t cur_consume_sync_index_;
  std::shared_ptr<std::thread> consume_work_thread_ptr_;
  int32_t cur_consume_index_; 
};

} // namespace io
} // namespace robosense

#endif // DATAREADERINTERFACE_H