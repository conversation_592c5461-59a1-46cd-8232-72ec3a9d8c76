#include "hyper_vision/dataiomanager/dataconverterros.h"
#if defined(ROS2_FOUND)
#include "hyper_vision/dataiomanager/dataconverterros2.h"
#endif // defined(ROS2_FOUND)
#include "hyper_vision/dataiomanager/datawriterinterface.h"

namespace robosense {
namespace io {

DataWriterInterface::DataWriterInterface() {
  data_io_time_util_.setTimeFormat("%Y_%m_%d_%H_%M_%S");
  bag_ptr_ = nullptr;
}

DataWriterInterface::~DataWriterInterface() {}

int DataWriterInterface::initAddJpegCoder(const std::string &topicName) {
  if (jpeg_coder_map_.find(topicName) == jpeg_coder_map_.end()) {
    robosense::jpeg::JpegCoder::Ptr jpegCoderPtr;
    try {
      jpegCoderPtr.reset(new robosense::jpeg::JpegCoder());
    } catch (...) {
      RERROR << name() << ": Malloc Jpeg Coder Failed !";
      return -1;
    }

    std::pair<uint32_t, uint32_t> image_size;
    int ret = DataTopicNameSupportUtil::fromTopicNameToImageSize(topicName,
                                                                 image_size);
    if (ret != 0) {
      RERROR << name() << ": From TopicName = " << topicName
             << " To Image Size Failed !";
      return -2;
    }

    robosense::jpeg::JpegCodesConfig jpegConfig;
    jpegConfig.coderType =
        robosense::jpeg::JPEG_CODER_TYPE::RS_JPEG_CODER_ENCODE;
    jpegConfig.imageFrameFormat =
        robosense::common::ImageFrameFormat::FRAME_FORMAT_RGB24;
    jpegConfig.imageWidth = image_size.first;
    jpegConfig.imageHeight = image_size.second;
    jpegConfig.jpegQuality = 70;
    jpegConfig.sampleFactor = 1;
    jpegConfig.gpuDeviceId = 0;

    ret = jpegCoderPtr->init(jpegConfig);
    if (ret != 0) {
      RERROR << name()
             << ": Jpeg Coder Initial Failed: topicName = " << topicName
             << ", ret = " << ret;
      return -3;
    }

    jpeg_coder_map_.insert({topicName, jpegCoderPtr});
    RINFO << name() << ": Add Jpeg Coder Successed: topicName = " << topicName;
  }

  return 0;
}

int DataWriterInterface::initAddH265Coder(const std::string &topicName) {
  if (h265_coder_map_.find(topicName) == h265_coder_map_.end()) {
    DataWriterH265Encoder::Ptr h265CoderPtr;
    try {
      h265CoderPtr.reset(new DataWriterH265Encoder());
    } catch (...) {
      RERROR << name() << ": Malloc H265 Coder Failed !";
      return -1;
    }

    std::pair<uint32_t, uint32_t> image_size;
    int ret = DataTopicNameSupportUtil::fromTopicNameToImageSize(topicName,
                                                                 image_size);
    if (ret != 0) {
      RERROR << name() << ": From TopicName = " << topicName
             << " To Image Size Failed !";
      return -2;
    }

    robosense::h265::H265CodesConfig h265Config;
    h265Config.codeType =
        robosense::h265::H265_CODER_TYPE::RS_H265_CODER_ENCODE;
    h265Config.imageFrameFormat =
        robosense::common::ImageFrameFormat::FRAME_FORMAT_RGB24;
    h265Config.imgWidth = image_size.first;
    h265Config.imgHeight = image_size.second;
    h265Config.imgFreq = 30;
    h265Config.codesRateType =
        robosense::h265::H265_CODES_RATE_TYPE::RS_H265_CODES_CUSTOM;
    h265Config.imgCodeRate = 5 * 1024 * 1024;

#if 0
    // 异步编码模式
    const DataWriterH265Encoder::RS_DATA_WRITE_H265_CALLBACK &callback =
        std::bind(&DataWriterInterface::h265Callback, this,
                  std::placeholders::_1);
    ret = h265CoderPtr->init(topicName, h265Config, callback);
#else
    // 同步编码模式
    ret = h265CoderPtr->init(topicName, h265Config, nullptr);
#endif
    if (ret != 0) {
      RERROR << name()
             << ": H265 Coder Initial Failed: topicName = " << topicName
             << ", ret = " << ret;
      return -2;
    }

    h265_coder_map_.insert({topicName, h265CoderPtr});

    RINFO << name() << ": Add H265 Coder Successed: topicName = " << topicName;
  }

  return 0;
}

void DataWriterInterface::h265Callback(
    const DataWriterSingleItem::Ptr &msgPtr) {
  if (msgPtr != nullptr) {
    // 这里更新状态即可
    std::lock_guard<std::mutex> lg(write_buffer_mtx_);
    msgPtr->isReady = true;
    write_buffer_cond_.notify_one();
    RINFO << name()
          << ": H265 Encode Successed: topicName = " << msgPtr->topicName;
  }
}

int DataWriterInterface::writeRegisterMessage(
    const DataWriterSingleItem::Ptr &msgPtr) {
  if (msgPtr == nullptr || msgPtr->message == nullptr) {
    return 0;
  }
  const static std::type_info &imuMessageTypeInfo =
      typeid(std::shared_ptr<robosense::common::MotionFrame>);
  const static std::type_info &imageMessageTypeInfo =
      typeid(std::shared_ptr<robosense::common::ImageFrame>);
  const static std::type_info &depthMessageTypeInfo =
      typeid(std::shared_ptr<robosense::common::DepthFrame>);
  const static std::type_info &imuMessageTypeInfo2 =
      typeid(std::shared_ptr<sensor_msgs::Imu>);
  const static std::type_info &imageMessageTypeInfo2 =
      typeid(std::shared_ptr<sensor_msgs::Image>);
  const static std::type_info &compressedImageTypeInfo =
      typeid(sensor_msgs::CompressedImage::Ptr);
  const static std::type_info &pclPointCloudTypeInfo =
      typeid(typename pcl::PointCloud<RsPointXYZRGBIRT>::Ptr);
  const static std::type_info &pclPointCloudTypeInfo2 =
      typeid(typename pcl::PointCloud<RsPointXYZIRT>::Ptr);
  const static std::type_info &rscompressedImageTypeInfo =
      typeid(rscamera_msg::RsCompressedImage::Ptr);

  const std::type_info &typeInfo = msgPtr->message->type();
  if (typeInfo == imuMessageTypeInfo) {
    auto ptr = std::any_cast<std::shared_ptr<robosense::common::MotionFrame>>(msgPtr->message);
    if (ptr == nullptr) {
      RERROR << name() << ": AnyCast Failed: topicName = " << msgPtr->topicName;
      return -1;
    } else if (ptr == nullptr) {
      RWARN << name() << ": topicName = " << msgPtr->topicName
            << " To Write Is Nullptr !";
      return 0;
    }
#if ENABLE_USE_CUSTOM_ROS_MSG
    hyper_vision_msgs::MotionFrame::Ptr motionFrameMsgPtr(
        new hyper_vision_msgs::MotionFrame());
    int ret = DataConverterRos::fromMsgToRosMsg(**ptr, *motionFrameMsgPtr);
    if (ret != 0) {
      RERROR << name() << ": topicName = " << msgPtr->topicName
             << " From Msg To Custom Ros Msg Failed: ret = " << ret;
      return -2;
    }
#else
    sensor_msgs::Imu::Ptr motionFrameMsgPtr(new sensor_msgs::Imu());
    int ret = DataConverterRos::fromMsgToRosMsg(*ptr, *motionFrameMsgPtr);
    if (ret != 0) {
      RERROR << name() << ": topicName = " << msgPtr->topicName
             << " From Msg To Ros Msg Failed: ret = " << ret;
      return -2;
    }
#endif // ENABLE_USE_CUSTOM_ROS_MSG
    if (bag_ptr_ != nullptr) {
      ros::Time time;
      time.fromNSec(msgPtr->writeTimestampNs);
      bag_ptr_->write(msgPtr->topicName, time, motionFrameMsgPtr);
    }
  } else if (typeInfo == imageMessageTypeInfo) {
    auto ptr = std::any_cast<std::shared_ptr<robosense::common::ImageFrame>>(msgPtr->message);
    if (ptr == nullptr) {
      RERROR << name() << ": AnyCast Failed: topicName = " << msgPtr->topicName;
      return -3;
    } else if (ptr == nullptr) {
      RWARN << name() << ": topicName = " << msgPtr->topicName
            << " To Write Is Nullptr !";
      return 0;
    }
#if ENABLE_USE_CUSTOM_ROS_MSG
    hyper_vision_msgs::ImageFrame::Ptr imageFrameMsgPtr(
        new hyper_vision_msgs::ImageFrame());
    int ret = DataConverterRos::fromMsgToRosMsg(**ptr, *imageFrameMsgPtr);
    if (ret != 0) {
      RERROR << name() << ": topicName = " << msgPtr->topicName
             << " From Msg To Custom Ros Msg Failed: ret = " << ret;
      return -4;
    }
#else
    sensor_msgs::Image::Ptr imageFrameMsgPtr(new sensor_msgs::Image());
    int ret = DataConverterRos::fromMsgToRosMsg(*ptr, *imageFrameMsgPtr);
    if (ret != 0) {
      RERROR << name() << ": topicName = " << msgPtr->topicName
             << " From Msg To Ros Msg Failed: ret = " << ret;
      return -4;
    }
#endif // ENABLE_USE_CUSTOM_ROS_MSG
    if (bag_ptr_ != nullptr) {
      ros::Time time;
      time.fromNSec(msgPtr->writeTimestampNs);
      bag_ptr_->write(msgPtr->topicName, time, imageFrameMsgPtr);
    }
  } else if (typeInfo == depthMessageTypeInfo) {
    auto ptr = std::any_cast<std::shared_ptr<robosense::common::DepthFrame>>(msgPtr->message);
    if (ptr == nullptr) {
      RERROR << name() << ": AnyCast Failed: topicName = " << msgPtr->topicName;
      return -5;
    } else if (ptr == nullptr) {
      RWARN << name() << ": topicName = " << msgPtr->topicName
            << " To Write Is Nullptr !";
      return 0;
    }
#if ENABLE_USE_CUSTOM_ROS_MSG
    hyper_vision_msgs::DepthFrame::Ptr depthFrameMsgPtr(
        new hyper_vision_msgs::DepthFrame());
    int ret = DataConverterRos::fromMsgToRosMsg(**ptr, *depthFrameMsgPtr);
    if (ret != 0) {
      RERROR << name() << ": topicName = " << msgPtr->topicName
             << " From Msg To Custom Ros Msg Failed: ret = " << ret;
      return -6;
    }
#else
    sensor_msgs::PointCloud2::Ptr depthFrameMsgPtr(
        new sensor_msgs::PointCloud2());
    int ret = DataConverterRos::fromMsgToRosMsg(*ptr, *depthFrameMsgPtr);
    if (ret != 0) {
      RERROR << name() << ": topicName = " << msgPtr->topicName
             << " From Msg To Ros Msg Failed: ret = " << ret;
      return -6;
    }
#endif // ENABLE_USE_CUSTOM_ROS_MSG
    if (bag_ptr_ != nullptr) {
      ros::Time time;
      time.fromNSec(msgPtr->writeTimestampNs);
      bag_ptr_->write(msgPtr->topicName, time, depthFrameMsgPtr);
    }
  } else if (typeInfo == compressedImageTypeInfo) {
    auto ptr = std::any_cast<sensor_msgs::CompressedImage::Ptr>(msgPtr->message);
    if (ptr == nullptr) {
      RERROR << name() << ": AnyCast Failed: topicName = " << msgPtr->topicName;
      return -7;
    } else if (ptr == nullptr) {
      RWARN << name() << ": topicName = " << msgPtr->topicName
            << " To Write Is Nullptr !";
      return 0;
    }
    if (bag_ptr_ != nullptr) {
      ros::Time time;
      time.fromNSec(msgPtr->writeTimestampNs);
      bag_ptr_->write(msgPtr->topicName, time, *ptr);
    }
  } else if (typeInfo == rscompressedImageTypeInfo) {
    auto ptr = std::any_cast<rscamera_msg::RsCompressedImage::Ptr>(msgPtr->message);
    if (ptr == nullptr) {
      RERROR << name() << ": AnyCast Failed: topicName = " << msgPtr->topicName;
      return -8;
    } else if (ptr == nullptr) {
      RWARN << name() << ": topicName = " << msgPtr->topicName
            << " To Write Is Nullptr !";
      return 0;
    }
    if (bag_ptr_ != nullptr) {
      ros::Time time;
      time.fromNSec(msgPtr->writeTimestampNs);
      bag_ptr_->write(msgPtr->topicName, time, *ptr);
    }
  } else if (typeInfo == pclPointCloudTypeInfo) {
    auto ptr = std::any_cast<pcl::PointCloud<RsPointXYZRGBIRT>::Ptr>(msgPtr->message);
    if (ptr == nullptr) {
      RERROR << name() << ": AnyCast Failed: topicName = " << msgPtr->topicName;
      return -9;
    } else if (ptr == nullptr) {
      RWARN << name() << ": topicName = " << msgPtr->topicName
            << " To Write Is Nullptr !";
      return 0;
    }
    sensor_msgs::PointCloud2::Ptr rosMsgPtr(new sensor_msgs::PointCloud2());
    int ret =
        DataConverterRos::fromMsgToRosMsg<RsPointXYZRGBIRT>(*ptr, *rosMsgPtr);
    if (ret != 0) {
      RERROR << name() << ": topicName = " << msgPtr->topicName
             << " From Msg(RsPointXYZRGBIRT) To Ros Msg Failed: ret = " << ret;
      return -10;
    }
    if (bag_ptr_ != nullptr) {
      ros::Time time;
      time.fromNSec(msgPtr->writeTimestampNs);
      bag_ptr_->write(msgPtr->topicName, time, rosMsgPtr);
    }
  } else if (typeInfo == pclPointCloudTypeInfo2) {
    auto ptr = std::any_cast<pcl::PointCloud<RsPointXYZIRT>::Ptr>(msgPtr->message);
    if (ptr == nullptr) {
      RERROR << name() << ": AnyCast Failed: topicName = " << msgPtr->topicName;
      return -11;
    } else if (ptr == nullptr) {
      RWARN << name() << ": topicName = " << msgPtr->topicName
            << " To Write Is Nullptr !";
      return 0;
    }
    sensor_msgs::PointCloud2::Ptr rosMsgPtr(new sensor_msgs::PointCloud2());
    int ret =
        DataConverterRos::fromMsgToRosMsg<RsPointXYZIRT>(*ptr, *rosMsgPtr);
    if (ret != 0) {
      RERROR << name() << ": topicName = " << msgPtr->topicName
             << " From Msg(RsPointXYZIRT) To Ros Msg Failed: ret = " << ret;
      return -12;
    }
    if (bag_ptr_ != nullptr) {
      ros::Time time;
      time.fromNSec(msgPtr->writeTimestampNs);
      bag_ptr_->write(msgPtr->topicName, time, rosMsgPtr);
    }
  } else {
    RWARN << name() << ": Not Support Message Type To Write Rosbag !";
  }

  return 0;
}

int DataWriterInterface::writeRegisterMessageRos2(
    const DataWriterSingleItem::Ptr &msgPtr) {
#if defined(ROS2_FOUND)
  if (msgPtr == nullptr || msgPtr->message == nullptr) {
    return 0;
  }
  const static std::type_info &imuMessageTypeInfo =
      typeid(std::shared_ptr<robosense::common::MotionFrame>);
  const static std::type_info &imageMessageTypeInfo =
      typeid(std::shared_ptr<robosense::common::ImageFrame>);
  const static std::type_info &depthMessageTypeInfo =
      typeid(std::shared_ptr<robosense::common::DepthFrame>);
  const static std::type_info &imuMessageTypeInfo2 =
      typeid(sensor_msgs::msg::Imu::SharedPtr);
  const static std::type_info &imageMessageTypeInfo2 =
      typeid(sensor_msgs::msg::Image::SharedPtr);
  const static std::type_info &compressedImageTypeInfo =
      typeid(sensor_msgs::msg::CompressedImage::SharedPtr);
  const static std::type_info &pclPointCloudTypeInfo =
      typeid(typename pcl::PointCloud<RsPointXYZRGBIRT>::Ptr);
  const static std::type_info &pclPointCloudTypeInfo2 =
      typeid(typename pcl::PointCloud<RsPointXYZIRT>::Ptr);
  const static std::type_info &rscompressedImageTypeInfo =
      typeid(rscamera_msg::msg::RsCompressedImage::SharedPtr);
  RINFO << "ROS2 Write TopicName = " << msgPtr->topicName;
  const std::type_info &typeInfo = msgPtr->message->type_info();
  if (typeInfo == imuMessageTypeInfo) {
    std::shared_ptr<robosense::common::MotionFrame> *ptr =
        msgPtr->message
            ->AnyCast<std::shared_ptr<robosense::common::MotionFrame>>();
    if (ptr == nullptr) {
      RERROR << name() << ": AnyCast Failed: topicName = " << msgPtr->topicName;
      return -1;
    } else if (*ptr == nullptr) {
      RWARN << name() << ": topicName = " << msgPtr->topicName
            << " To Write Is Nullptr !";
      return 0;
    }
#if ENABLE_USE_CUSTOM_ROS_MSG
    hyper_vision_msgs::msg::MotionFrame::SharedPtr motionFrameMsgPtr(
        new hyper_vision_msgs::msg::MotionFrame());
    int ret = DataConverterRos2::fromMsgToRosMsg(**ptr, *motionFrameMsgPtr);
    if (ret != 0) {
      RERROR << name() << ": topicName = " << msgPtr->topicName
             << " From Msg To Custom Ros Msg Failed: ret = " << ret;
      return -2;
    }
    if (ros2_writer_ptr_ != nullptr) {
      ros2_writer_ptr_->write<hyper_vision_msgs::msg::MotionFrame>(
          *motionFrameMsgPtr, msgPtr->topicName,
          rclcpp::Time(msgPtr->writeTimestampNs));
    }
#else
    sensor_msgs::msg::Imu::SharedPtr motionFrameMsgPtr(
        new sensor_msgs::msg::Imu());
    int ret = DataConverterRos2::fromMsgToRosMsg(**ptr, *motionFrameMsgPtr);
    if (ret != 0) {
      RERROR << name() << ": topicName = " << msgPtr->topicName
             << " From Msg To Ros Msg Failed: ret = " << ret;
      return -2;
    }
    if (ros2_writer_ptr_ != nullptr) {
      ros2_writer_ptr_->write<sensor_msgs::msg::Imu>(
          *motionFrameMsgPtr, msgPtr->topicName,
          rclcpp::Time(msgPtr->writeTimestampNs));
    }
#endif // ENABLE_USE_CUSTOM_ROS_MSG
  } else if (typeInfo == imageMessageTypeInfo) {
    std::shared_ptr<robosense::common::ImageFrame> *ptr =
        msgPtr->message
            ->AnyCast<std::shared_ptr<robosense::common::ImageFrame>>();
    if (ptr == nullptr) {
      RERROR << name() << ": AnyCast Failed: topicName = " << msgPtr->topicName;
      return -3;
    } else if (*ptr == nullptr) {
      RWARN << name() << ": topicName = " << msgPtr->topicName
            << " To Write Is Nullptr !";
      return 0;
    }
#if ENABLE_USE_CUSTOM_ROS_MSG
    hyper_vision_msgs::msg::ImageFrame::SharedPtr imageFrameMsgPtr(
        new hyper_vision_msgs::msg::ImageFrame());
    int ret = DataConverterRos2::fromMsgToRosMsg(**ptr, *imageFrameMsgPtr);
    if (ret != 0) {
      RERROR << name() << ": topicName = " << msgPtr->topicName
             << " From Msg To Custom Ros Msg Failed: ret = " << ret;
      return -4;
    }
    if (ros2_writer_ptr_ != nullptr) {
      ros2_writer_ptr_->write<hyper_vision_msgs::msg::ImageFrame>(
          *imageFrameMsgPtr, msgPtr->topicName,
          rclcpp::Time(msgPtr->writeTimestampNs));
    }
#else
    sensor_msgs::msg::Image::SharedPtr imageFrameMsgPtr(
        new sensor_msgs::msg::Image());
    int ret = DataConverterRos2::fromMsgToRosMsg(**ptr, *imageFrameMsgPtr);
    if (ret != 0) {
      RERROR << name() << ": topicName = " << msgPtr->topicName
             << " From Msg To Ros Msg Failed: ret = " << ret;
      return -4;
    }
    if (ros2_writer_ptr_ != nullptr) {
      ros2_writer_ptr_->write<sensor_msgs::msg::Image>(
          *imageFrameMsgPtr, msgPtr->topicName,
          rclcpp::Time(msgPtr->writeTimestampNs));
    }
#endif // ENABLE_USE_CUSTOM_ROS_MSG
  } else if (typeInfo == depthMessageTypeInfo) {
    std::shared_ptr<robosense::common::DepthFrame> *ptr =
        msgPtr->message
            ->AnyCast<std::shared_ptr<robosense::common::DepthFrame>>();
    if (ptr == nullptr) {
      RERROR << name() << ": AnyCast Failed: topicName = " << msgPtr->topicName;
      return -5;
    } else if (*ptr == nullptr) {
      RWARN << name() << ": topicName = " << msgPtr->topicName
            << " To Write Is Nullptr !";
      return 0;
    }
#if ENABLE_USE_CUSTOM_ROS_MSG
    hyper_vision_msgs::msg::DepthFrame::SharedPtr depthFrameMsgPtr(
        new hyper_vision_msgs::msg::DepthFrame());
    int ret = DataConverterRos2::fromMsgToRosMsg(**ptr, *depthFrameMsgPtr);
    if (ret != 0) {
      RERROR << name() << ": topicName = " << msgPtr->topicName
             << " From Msg To Custom Ros Msg Failed: ret = " << ret;
      return -6;
    }
    if (ros2_writer_ptr_ != nullptr) {
      ros2_writer_ptr_->write<hyper_vision_msgs::msg::DepthFrame>(
          *depthFrameMsgPtr, msgPtr->topicName,
          rclcpp::Time(msgPtr->writeTimestampNs));
    }
#else
    sensor_msgs::msg::PointCloud2::SharedPtr depthFrameMsgPtr(
        new sensor_msgs::msg::PointCloud2());
    int ret = DataConverterRos2::fromMsgToRosMsg(**ptr, *depthFrameMsgPtr);
    if (ret != 0) {
      RERROR << name() << ": topicName = " << msgPtr->topicName
             << " From Msg To Ros Msg Failed: ret = " << ret;
      return -6;
    }
    if (ros2_writer_ptr_ != nullptr) {
      ros2_writer_ptr_->write<sensor_msgs::msg::PointCloud2>(
          *depthFrameMsgPtr, msgPtr->topicName,
          rclcpp::Time(msgPtr->writeTimestampNs));
    }
#endif // ENABLE_USE_CUSTOM_ROS_MSG
  } else if (typeInfo == compressedImageTypeInfo) {
    sensor_msgs::msg::CompressedImage::SharedPtr *ptr =
        msgPtr->message
            ->AnyCast<sensor_msgs::msg::CompressedImage::SharedPtr>();
    if (ptr == nullptr) {
      RERROR << name() << ": AnyCast Failed: topicName = " << msgPtr->topicName;
      return -7;
    } else if (*ptr == nullptr) {
      RWARN << name() << ": topicName = " << msgPtr->topicName
            << " To Write Is Nullptr !";
      return 0;
    }
    if (ros2_writer_ptr_ != nullptr) {
      ros2_writer_ptr_->write<sensor_msgs::msg::CompressedImage>(
          **ptr, msgPtr->topicName, rclcpp::Time(msgPtr->writeTimestampNs));
    }
  } else if (typeInfo == rscompressedImageTypeInfo) {
    rscamera_msg::msg::RsCompressedImage::SharedPtr *ptr =
        msgPtr->message
            ->AnyCast<rscamera_msg::msg::RsCompressedImage::SharedPtr>();
    if (ptr == nullptr) {
      RERROR << name() << ": AnyCast Failed: topicName = " << msgPtr->topicName;
      return -8;
    } else if (*ptr == nullptr) {
      RWARN << name() << ": topicName = " << msgPtr->topicName
            << " To Write Is Nullptr !";
      return 0;
    }
    if (ros2_writer_ptr_ != nullptr) {
      ros2_writer_ptr_->write<rscamera_msg::msg::RsCompressedImage>(
          **ptr, msgPtr->topicName, rclcpp::Time(msgPtr->writeTimestampNs));
    }
  } else if (typeInfo == pclPointCloudTypeInfo) {
    pcl::PointCloud<RsPointXYZRGBIRT>::Ptr *ptr =
        msgPtr->message->AnyCast<pcl::PointCloud<RsPointXYZRGBIRT>::Ptr>();
    if (ptr == nullptr) {
      RERROR << name() << ": AnyCast Failed: topicName = " << msgPtr->topicName;
      return -9;
    } else if (*ptr == nullptr) {
      RWARN << name() << ": topicName = " << msgPtr->topicName
            << " To Write Is Nullptr !";
      return 0;
    }
    sensor_msgs::msg::PointCloud2::SharedPtr rosMsgPtr(
        new sensor_msgs::msg::PointCloud2());
    int ret =
        DataConverterRos2::fromMsgToRosMsg<RsPointXYZRGBIRT>(**ptr, *rosMsgPtr);
    if (ret != 0) {
      RERROR << name() << ": topicName = " << msgPtr->topicName
             << " From Msg(RsPointXYZRGBIRT) To Ros Msg Failed: ret = " << ret;
      return -10;
    }
    if (ros2_writer_ptr_ != nullptr) {
      ros2_writer_ptr_->write<sensor_msgs::msg::PointCloud2>(
          *rosMsgPtr, msgPtr->topicName,
          rclcpp::Time(msgPtr->writeTimestampNs));
    }
  } else if (typeInfo == pclPointCloudTypeInfo2) {
    pcl::PointCloud<RsPointXYZIRT>::Ptr *ptr =
        msgPtr->message->AnyCast<pcl::PointCloud<RsPointXYZIRT>::Ptr>();
    if (ptr == nullptr) {
      RERROR << name() << ": AnyCast Failed: topicName = " << msgPtr->topicName;
      return -11;
    } else if (*ptr == nullptr) {
      RWARN << name() << ": topicName = " << msgPtr->topicName
            << " To Write Is Nullptr !";
      return 0;
    }
    sensor_msgs::msg::PointCloud2::SharedPtr rosMsgPtr(
        new sensor_msgs::msg::PointCloud2());
    int ret =
        DataConverterRos2::fromMsgToRosMsg<RsPointXYZIRT>(**ptr, *rosMsgPtr);
    if (ret != 0) {
      RERROR << name() << ": topicName = " << msgPtr->topicName
             << " From Msg(RsPointXYZIRT) To Ros Msg Failed: ret = " << ret;
      return -12;
    }
    if (ros2_writer_ptr_ != nullptr) {
      ros2_writer_ptr_->write<sensor_msgs::msg::PointCloud2>(
          *rosMsgPtr, msgPtr->topicName,
          rclcpp::Time(msgPtr->writeTimestampNs));
    }
  } else {
    RWARN << name() << ": Not Support Message Type To Write Rosbag !";
  }

  return 0;
#else
  RERROR << name() << ": Not Support Ros2 !";
  return -1;
#endif // defined(ROS2_FOUND)
}

} // namespace io
} // namespace robosense