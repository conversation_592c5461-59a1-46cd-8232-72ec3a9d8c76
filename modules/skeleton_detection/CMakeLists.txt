cmake_minimum_required(VERSION 3.5)

project(skeleton_detection)

# 启用位置无关代码（对动态库或跨平台静态库有利）
set(CMAKE_POSITION_INDEPENDENT_CODE ON)

# 平台特定设置
if(WIN32)
    add_compile_options(/W4)  # 启用第 4 级警告
    set(CUSTOM_FLANN_DIR  "C:\\Program Files (x86)\\flann\\include")
elseif(UNIX AND NOT APPLE)
    add_compile_options(-Wall -Wextra -pedantic)  # 启用常见警告
endif()

set(SRCS "")
set(SUB_DIR "interface/include" "interface/src")

foreach (dir ${SUB_DIR})
    file(GLOB_RECURSE tmp_srcs ${dir}/*.cpp ${dir}/*.h)
    list(APPEND SRCS ${tmp_srcs})
endforeach ()

add_library(skeleton_detection STATIC ${SRCS})

target_include_directories(skeleton_detection PUBLIC
        interface/include
        ${PCL_INCLUDE_DIRS}
        ${OpenCV_INCLUDE_DIRS}
        ${YAML_CPP_LIBRARY_DIR}
)

target_link_libraries(skeleton_detection PUBLIC
        common
        ${PCL_LIBRARIES}
        ${OpenCV_LIBS}
        ${YAML_CPP_LIBRARIES}
)
