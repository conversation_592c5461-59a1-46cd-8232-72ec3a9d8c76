/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef HYPER_VISION_SKELETON_DETECTION_SKELETON_DETECTION_H_
#define HYPER_VISION_SKELETON_DETECTION_SKELETON_DETECTION_H_

#include <mutex>

#include <yaml-cpp/yaml.h>

#include "hyper_vision/skeleton_detection/message/skeleton_output_msg.h"

namespace robosense::skeleton
{

  struct DataState
  {
    bool has_left_image = false;
    bool has_right_image = false;
    bool has_depth = false;

    bool IsReady() const { return has_left_image and has_right_image and has_depth; }

    void Reset() { has_left_image = has_right_image = has_depth = false; }
  };

  class SkeletonDetection
  {
  public:
    using Ptr = std::shared_ptr<SkeletonDetection>;

    SkeletonDetection() = default;

    ~SkeletonDetection() { Stop(); }

    int Init(const YAML::Node& cfg_node);

    int Start();

    void Stop();

    void AddDataImageLeft(const std::shared_ptr<robosense::common::ImageFrame> &msg_ptr);

    void AddDataImageRight(const std::shared_ptr<robosense::common::ImageFrame> &msg_ptr);

    void AddData(const std::shared_ptr<robosense::common::DepthFrame>& msg_ptr);

    void SetCallback(const std::function<void(const SkeletonOutputMsg::Ptr& msg_ptr)>& callback);

  private:
    static std::string name() { return "SkeletonDetection: "; }

    void TryProcess();

  private:
    DataState data_state_;
    std::mutex data_state_mutex_;

    std::mutex output_msg_mtx_;
    std::function<void(const SkeletonOutputMsg::Ptr& msg_ptr)> output_msg_callback_;

  };

} // namespace robosense::skeleton_detection

#endif  // HYPER_VISION_SKELETON_DETECTION_SKELETON_DETECTION_H_