/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef HYPER_VISION_IMAGEDEPTH_IMAGEDEPTH_H_
#define HYPER_VISION_IMAGEDEPTH_IMAGEDEPTH_H_

#include <mutex>

#include <yaml-cpp/yaml.h>

#include "depth_estimation_interface.h"

#include "hyper_vision/imagedepth/message/imagedepth_output_msg.h"

namespace robosense::imagedepth
{

  struct DataState
  {
    bool has_left_image = false;
    bool has_right_image = false;
    bool has_depth = false;

    bool is_ready() const { return has_left_image and has_right_image and has_depth; }

    void reset() { has_left_image = has_right_image = has_depth = false; }
  };

  class ImageDepth
  {
  public:
    using Ptr = std::shared_ptr<ImageDepth>;

  public:
    ImageDepth() = default;

    ~ImageDepth() { Stop(); }

    int Init(const YAML::Node& cfg_node);

    int AddDataImageLeft(const std::shared_ptr<robosense::common::ImageFrame>& msg_ptr);

    int AddDataImageRight(const std::shared_ptr<robosense::common::ImageFrame>& msg_ptr);

    int AddData(const std::shared_ptr<robosense::common::DepthFrame>& msg_ptr);

    int Start();

    void Stop();

    void SetCallback(const std::function<void(const ImageDepthOutputMsg::Ptr& msg_ptr)>& callback);

  public:
    static std::string name() { return " ImageDepth: "; }

    void TryProcess();

    void OnDepthEstimationResults(const std::shared_ptr<listereo::DepthEstimationResults>& results);

    static listereo::LidarData
    ConvertDepthFrameToLidarData(const std::shared_ptr<common::DepthFrame>& depth_frame);

    static listereo::StereoImage
    ConvertImageFramesToStereoImage(const std::shared_ptr<common::ImageFrame>& left_frame,
                                   const std::shared_ptr<common::ImageFrame>& right_frame);

    static common::DepthImage
    ConvertListereoDepthImageToCommonDepthImage(const listereo::DepthImage& listereo_depth_image);

    static common::PointCloud
    ConvertListereoColorPointCloudToCommonPointCloud(const listereo::ColorPointCloud& listereo_point_cloud);

  private:
    DataState data_state_;
    std::mutex data_state_mtx_;

    std::mutex output_msg_mtx_;
    std::function<void(const ImageDepthOutputMsg::Ptr& msg_ptr)> output_msg_cb_;

    std::unique_ptr<listereo::DepthEstimationInterface> impl_;

    // 数据存储
    std::shared_ptr<robosense::common::ImageFrame> left_image_ptr_;
    std::shared_ptr<robosense::common::ImageFrame> right_image_ptr_;
    std::shared_ptr<robosense::common::DepthFrame> depth_frame_ptr_;
  };

} // namespace robosense::imagedepth


#endif // HYPER_VISION_IMAGEDEPTH_IMAGEDEPTH_H_
