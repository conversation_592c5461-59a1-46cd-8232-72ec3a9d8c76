cmake_minimum_required(VERSION 3.5)

project(image_depth)

# 启用位置无关代码（对动态库或跨平台静态库有利）
set(CMAKE_POSITION_INDEPENDENT_CODE ON)

# 平台特定设置
if(WIN32)
    add_compile_options(/W4)  # 启用第 4 级警告
    set(CUSTOM_FLANN_DIR  "C:\\Program Files (x86)\\flann\\include")
elseif(UNIX AND NOT APPLE)
    add_compile_options(-Wall -Wextra -pedantic)  # 启用常见警告
endif()

find_package(CUDA REQUIRED)

set(SRCS "")
set(SUB_DIR "interface/include" "interface/src" "lidar_stereo_matching/include" "lidar_stereo_matching/src")

foreach (dir ${SUB_DIR})
    file(GLOB_RECURSE tmp_srcs ${dir}/*.cpp ${dir}/*.h ${dir}/*.hpp)
    list(APPEND SRCS ${tmp_srcs})
endforeach ()

add_library(image_depth STATIC ${SRCS})

target_include_directories(image_depth PUBLIC
        interface/include
        lidar_stereo_matching/include
        ${PCL_INCLUDE_DIRS}
        ${CUSTOM_FLANN_DIR}
        ${CUDA_INCLUDE_DIRS}
        ${YAML_CPP_LIBRARY_DIR}
        )

target_link_libraries(image_depth PUBLIC
        common
        ${PCL_LIBRARIES}
        ${OpenCV_LIBS}
        ${CUDA_LIBRARIES}
        ${YAML_CPP_LIBRARIES}
)
