#!/bin/bash
# ===============================================================================================
# 脚本名称：deploy.sh
# 使用前提：在 Ubuntu20.04 源码编译 ROS2 humble
# 功能描述：
#   1. 编译 Super-Sensor-SDK（cmake + make）
#   2. 使用 linuxdeployqt 为 front_end_app 生成 AppImage
#   3. 将生成的核心库（libcyber.so / libinterface.so）复制到 AcViewer 的插件目录
#   4. 清理插件目录中的旧文件，仅保留白名单文件
# 作者    ：hardy.zhong
# 日期    ：2025-07-18
# 使用方式：./deploy.sh
# ===============================================================================================

clear

start_time=$SECONDS

SDK_DIR="/home/<USER>/Gitlab/super_sensor_sdk_v1"
APP_DIR="/home/<USER>/Gitlab/AcViewer2/AcViewer_Linux_x86_64_v2.0.0_2025-08-29-16-05"
APP_PLUGIN_DIR="$APP_DIR/AcViewer_Data/Plugins"
APP_CONFIG_DIR="$APP_DIR/AcViewer_Data/StreamingAssets/Config"

print_step() {
    echo "----------------------------------------------------------------------------------------"
    echo "[$(date '+%F %T')] $1"
    echo "----------------------------------------------------------------------------------------"
}

print_step "Activate ROS2 environment ..."
. ~/ros2_humble/install/local_setup.bash
#printenv | grep -i ROS

print_step "Start compiling Super-Sensor-SDK ..."
cd "$SDK_DIR"
#rm -rf build && mkdir build
cd build
#cmake ..
make -j8

print_step "Prepare AppImage ..."
mkdir -p application/lib
cp -v cyber/libcyber.so application/lib/
cp -v modules/interface/libinterface.so application/lib/

print_step "Generate AppImage using linuxdeployqt ..."
cd application
export LD_LIBRARY_PATH="$SDK_DIR/third_party/librsros/lib/linux/x86":$LD_LIBRARY_PATH
export LD_LIBRARY_PATH="$SDK_DIR/third_party/libros2/rscamera_msg/lib":$LD_LIBRARY_PATH
export LD_LIBRARY_PATH="$SDK_DIR/third_party/libros2/hyper_vision_msgs/lib":$LD_LIBRARY_PATH
linuxdeployqt front_end_app -appimage -bundle-non-qt-libs

print_step "Update AcViewer plugin and config directory ..."
cd $APP_PLUGIN_DIR
find . -mindepth 1 -maxdepth 1 -type f -not \( -name "libStandaloneFileBrowser.so" -o -name "lib_burst_generated.so" \) -exec rm -f {} \;
cd "$SDK_DIR/build/application/lib" && cp ./* $APP_PLUGIN_DIR
[ -d "$APP_PLUGIN_DIR/third_party" ] || mkdir -p "$APP_PLUGIN_DIR/third_party"
cp -r "$SDK_DIR/third_party/libros2" "$APP_PLUGIN_DIR/third_party/"
cd $APP_CONFIG_DIR && rm -rf ./*
cd "$SDK_DIR/config" && cp ./* $APP_CONFIG_DIR
cd $APP_DIR && chmod +x AcViewer.x86_64

end_time=$SECONDS
elapsed_time=$((end_time - start_time))
print_step "Script completed in $elapsed_time seconds"

