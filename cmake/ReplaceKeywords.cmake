cmake_minimum_required(VERSION 3.5)

# 调用Python脚本
message("======Python script executed to process slam project.")
message("${CMAKE_SOURCE_DIR}")
message("${PYTHON_EXECUTABLE}")
if(WIN32)
execute_process(
  COMMAND python ${CMAKE_SOURCE_DIR}/modules/slam/script/replace_project_key_words.py ${CMAKE_SOURCE_DIR}
  WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
  RESULT_VARIABLE result
  OUTPUT_VARIABLE output
  ERROR_VARIABLE error
  OUTPUT_FILE  ${CMAKE_SOURCE_DIR}/build/replace_out.log # /dev/null  # 屏蔽标准输出
)
else() 
execute_process(
  COMMAND ${PYTHON_EXECUTABLE} modules/slam/script/replace_project_key_words.py ${CMAKE_SOURCE_DIR}
  WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
  RESULT_VARIABLE result
  OUTPUT_VARIABLE output
  ERROR_VARIABLE error
  OUTPUT_FILE  ${CMAKE_SOURCE_DIR}/build/replace_out.log # /dev/null  # 屏蔽标准输出
)
endif() 

# 检查Python脚本是否成功执行
if(result)
  message(FATAL_ERROR "======Python script execution failed.")
  message(error)
else()
  message(STATUS "======Python script executed successfully.")
endif()

# 调用 replace_include.py 脚本
#message("======Executing replace_include.py script to replace include statements.")
#if(WIN32)
#execute_process(
#  COMMAND python ${CMAKE_SOURCE_DIR}/modules/slam/script/replace_include.py
#  WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
#  RESULT_VARIABLE replace_result
#  OUTPUT_VARIABLE replace_output
#  ERROR_VARIABLE replace_error
#  OUTPUT_FILE  ${CMAKE_SOURCE_DIR}/build/replace_include_out.log
#)
#else()
#execute_process(
#  COMMAND ${PYTHON_EXECUTABLE} modules/slam/script/replace_include.py
#  WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
#  RESULT_VARIABLE replace_result
#  OUTPUT_VARIABLE replace_output
#  ERROR_VARIABLE replace_error
#  OUTPUT_FILE  ${CMAKE_SOURCE_DIR}/build/replace_include_out.log
#)
#endif()

# 检查 replace_include.py 脚本是否成功执行
#if(replace_result)
#  message(FATAL_ERROR "======replace_include.py script execution failed.")
#  message(${replace_error})
#else()
#  message(STATUS "======replace_include.py script executed successfully.")
#endif()