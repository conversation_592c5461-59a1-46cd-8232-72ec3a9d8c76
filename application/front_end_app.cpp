/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "hyper_vision/common/basic_type/basic_type.h"
#include "hyper_vision/interface/interface.h"

#include "../modules/imagedepth/lidar_stereo_matching/include/depth_estimation_interface.h"
#include "hyper_vision/imagedepth/imagedepth.h"
#include <csignal>
#include <filesystem>
#include <fstream>
#include <iomanip>
#include <sstream>

#include <pcl/io/pcd_io.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <opencv2/opencv.hpp>
#include <opencv2/imgcodecs.hpp>
#include <thread>

#ifdef _WIN32
#include <chrono>
#include <thread>
#endif

bool start_ = true;
std::string select_uuid;

std::filesystem::path g_output_path;

/**
 * @brief  signal handler
 * @note   will be called if receive ctrl+c signal from keyboard during the
 * progress (all the threads in progress will be stopped and the progress end)
 * @param  sig: the input signal
 * @retval None
 */
static void sigHandler(int sig) { start_ = false; }

void getDevices() {
  bool closed = true;
  while (start_) {
    // RINFO << "GetDeviceInfo()...";
    auto handle = [](void *devices, unsigned int size) {
      for (unsigned int i = 0; i < size; i++) {
        TagDeviceEvent device_event = static_cast<TagDeviceEvent *>(devices)[i];
        auto str_size = device_event.uuid_size;
        RINFO << "str size: " << str_size;
        std::string uuid = std::string(device_event.uuid, str_size);
        RINFO << "device uuid: " << uuid;
        if (select_uuid.empty()) {
          select_uuid = uuid;
        }
      }
    };
    GetDeviceInfo(handle);
    if (select_uuid.empty()) {
      RERROR << "no device found!";
      closed = true;
      std::this_thread::sleep_for(std::chrono::seconds(1));
      continue;
    }
    if (closed) {
      auto operator_type =
          robosense::common::DeviceOperatorType_t::DEVICE_OPERATOR_OPEN;
      RWARN << "open device uuid: " << select_uuid;
      if (0 == OperatorDevice(select_uuid.c_str(), operator_type)) {
        RINFO << "device is opened";
        closed = false;
      } else {
        RERROR << "operator device failed";
      }
    }
    select_uuid.clear();
    std::this_thread::sleep_for(std::chrono::seconds(2));
  }
}

/**
 * @brief 读取point3d.txt文件并转换为TagSkeletonPoint格式
 * @note 每三个数字组成一个3D点，每行是一帧的结果
 */
void readAndProcessPoint3DData()
{
  std::string point3d_file = std::filesystem::path(PROJECT_PATH) / "application" / "point3d.txt";
  std::ifstream file(point3d_file);

  if (not file.is_open())
  {
    RERROR << "无法打开文件: " << point3d_file;
    return;
  }

  std::string line;
  int frame_idx = 0;

  while (std::getline(file, line) && !line.empty())
  {
    // 跳过空行
    if (line.empty() or line.find_first_not_of(" \t\n\r") == std::string::npos)
      continue;

    TagSkeletonPoint skeleton_point;
    std::istringstream iss(line);
    std::vector<float> values;
    float value;

    // 读取行中的所有数字
    while (iss >> value)
    {
      values.push_back(value);
    }

    // 确保数字个数是3的倍数（每个3D点需要x,y,z三个坐标）
    if (values.size() % 3 != 0)
    {
      RWARN << "第" << frame_idx << "帧数据点数不是3的倍数，跳过该帧";
      ++frame_idx;
      continue;
    }

    int point_count = static_cast<int>(values.size()) / 3;
    if (point_count > 32)
    {
      RWARN << "第" << frame_idx << "帧包含" << point_count << "个点，超过最大限制32个，只保留前32个";
      point_count = 32;
    }

    skeleton_point.point_cnt = point_count;

    // 填充points数组
    for (int i = 0; i < point_count; i++)
    {
      skeleton_point.points[i * 3]     = values[i * 3];     // x
      skeleton_point.points[i * 3 + 1] = values[i * 3 + 1]; // y
      skeleton_point.points[i * 3 + 2] = values[i * 3 + 2]; // z
    }

    // 生成文件名
    std::stringstream ss;
    ss << std::setfill('0') << std::setw(6) << frame_idx;
    std::string frame_str = ss.str();

    // 保存为二进制格式
    std::string bin_filename = (g_output_path / ("skeleton_" + frame_str + ".bin")).string();
    std::ofstream bin_file(bin_filename, std::ios::binary);
    if (bin_file.is_open())
    {
      bin_file.write(reinterpret_cast<const char*>(&skeleton_point), sizeof(TagSkeletonPoint));
      bin_file.close();
      RINFO << "保存骨架数据(二进制)到: " << bin_filename << " (点数: " << skeleton_point.point_cnt << ")";
    }
    else
      RERROR << "无法创建二进制文件: " << bin_filename;

    // 保存为文本格式
    std::string txt_filename = (g_output_path / ("skeleton_" + frame_str + ".txt")).string();
    std::ofstream txt_file(txt_filename);
    if (txt_file.is_open())
    {
      txt_file << "Frame: " << frame_idx << std::endl;
      txt_file << "Point Count: " << skeleton_point.point_cnt << std::endl;
      txt_file << "Points:" << std::endl;

      for (int i = 0; i < skeleton_point.point_cnt; i++)
      {
        txt_file << "Point " << i << ": ("
                 << skeleton_point.points[i * 3] << ", "
                 << skeleton_point.points[i * 3 + 1] << ", "
                 << skeleton_point.points[i * 3 + 2] << ")" << std::endl;
      }
      txt_file.close();
      RINFO << "保存骨架数据(文本)到: " << txt_filename << " (点数: " << skeleton_point.point_cnt << ")";
    }
    else
      RERROR << "无法创建文本文件: " << txt_filename;

    ++frame_idx;
  }

  file.close();
  RINFO << "处理完成，共处理了 " << frame_idx << " 帧数据";
}

/**
 * @brief 读取point2d.txt文件并在图像上绘制2D点
 * @note 每三个数字取前两个作为2D坐标(x,y)，忽略z值，每行是一帧的结果
 */
void readAndProcessPoint2DData()
{
  std::string point2d_file = std::filesystem::path(PROJECT_PATH) / "application" / "point2d.txt";
  std::ifstream file(point2d_file);

  if (not file.is_open())
  {
    RERROR << "无法打开文件: " << point2d_file;
    return;
  }

  // 读取左右背景图像
  std::string left_image_path = std::filesystem::path(PROJECT_PATH) / "application" / "left.jpg";
  std::string right_image_path = std::filesystem::path(PROJECT_PATH) / "application" / "right.jpg";

  cv::Mat left_background = cv::imread(left_image_path);
  cv::Mat right_background = cv::imread(right_image_path);

  if (left_background.empty())
  {
    RERROR << "无法读取左背景图像: " << left_image_path;
    file.close();
    return;
  }

  if (right_background.empty())
  {
    RERROR << "无法读取右背景图像: " << right_image_path;
    file.close();
    return;
  }

  // 获取图像尺寸
  const int left_width = left_background.cols;
  const int left_height = left_background.rows;
  const int right_width = right_background.cols;
  const int right_height = right_background.rows;

  RINFO << "左图像尺寸: " << left_width << "x" << left_height;
  RINFO << "右图像尺寸: " << right_width << "x" << right_height;

  std::string line;
  int frame_idx = 0;

  while (std::getline(file, line) && !line.empty())
  {
    // 跳过空行
    if (line.empty() or line.find_first_not_of(" \t\n\r") == std::string::npos)
      continue;

    // 复制背景图像（每帧都重新开始）
    cv::Mat left_image = left_background.clone();
    cv::Mat right_image = right_background.clone();

    std::istringstream iss(line);
    std::vector<double> values;
    double value;

    // 读取行中的所有数字
    while (iss >> value)
    {
      values.push_back(value);
    }

    // 确保数字个数是3的倍数
    if (values.size() % 3 != 0)
    {
      RWARN << "第" << frame_idx << "帧数据点数不是3的倍数，跳过该帧";
      ++frame_idx;
      continue;
    }

    int point_count = static_cast<int>(values.size()) / 3;
    RINFO << "第" << frame_idx << "帧包含" << point_count << "个2D点";

    // 检查是否有足够的点
    if (point_count < 14)
    {
      RWARN << "第" << frame_idx << "帧点数不足14个，实际有" << point_count << "个点";
    }

    // 存储有效的点坐标，用于后续连线
    std::vector<cv::Point> left_valid_points;
    std::vector<cv::Point> right_valid_points;

    // 处理前7个点（绘制到左图）
    int left_points_to_process = std::min(7, point_count);
    for (int i = 0; i < left_points_to_process; i++)
    {
      double x = values[i * 3];     // x坐标
      double y = values[i * 3 + 1]; // y坐标
      // 忽略 values[i * 3 + 2] (z值)

      // 确保坐标在左图像范围内
      int px = static_cast<int>(std::round(x));
      int py = static_cast<int>(std::round(y));

      if (px >= 0 and px < left_width and py >= 0 and py < left_height)
      {
        // 存储有效点用于连线
        left_valid_points.emplace_back(px, py);

        // 绘制一个小圆点
        cv::circle(left_image, cv::Point(px, py), 5, cv::Scalar(0, 0, 255), -1);

        // 在圆点旁边绘制索引号
        std::string index_text = std::to_string(i);
        cv::Point text_position(px - 2, py + 20);

        // 确保文本位置在图像范围内
        text_position.x = std::max(0, std::min(text_position.x, left_width - 30));
        text_position.y = std::max(15, std::min(text_position.y, left_height - 5));

        // 使用黄色文字
        cv::putText(left_image, index_text, text_position, cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 255, 255), 2, cv::LINE_AA);
      }
    }

    // 处理后7个点（绘制到右图）
    int right_points_to_process = std::min(7, point_count - 7);
    for (int i = 0; i < right_points_to_process; i++)
    {
      int original_index = i + 7; // 原始索引
      double x = values[original_index * 3];     // x坐标
      double y = values[original_index * 3 + 1]; // y坐标
      // 忽略 values[original_index * 3 + 2] (z值)

      // 确保坐标在右图像范围内
      int px = static_cast<int>(std::round(x));
      int py = static_cast<int>(std::round(y));

      if (px >= 0 and px < right_width and py >= 0 and py < right_height)
      {
        // 存储有效点用于连线
        right_valid_points.emplace_back(px, py);

        // 绘制一个小圆点
        cv::circle(right_image, cv::Point(px, py), 5, cv::Scalar(0, 0, 255), -1);

        // 在圆点旁边绘制索引号（显示相对索引0-6）
        std::string index_text = std::to_string(i);
        cv::Point text_position(px - 2, py + 20);

        // 确保文本位置在图像范围内
        text_position.x = std::max(0, std::min(text_position.x, right_width - 30));
        text_position.y = std::max(15, std::min(text_position.y, right_height - 5));

        // 使用黄色文字
        cv::putText(right_image, index_text, text_position, cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 255, 255), 2, cv::LINE_AA);
      }
    }

    // 左图连线：第0、1、2、3个点按照索引顺序连线 (0→1→2→3)
    if (left_valid_points.size() >= 4)
    {
      cv::line(left_image, left_valid_points[0], left_valid_points[1], cv::Scalar(0, 255, 0), 2, cv::LINE_AA);
      cv::line(left_image, left_valid_points[1], left_valid_points[2], cv::Scalar(0, 255, 0), 2, cv::LINE_AA);
      cv::line(left_image, left_valid_points[2], left_valid_points[3], cv::Scalar(0, 255, 0), 2, cv::LINE_AA);
    }

    // 左图连线：第0、4、5、6个点按照索引顺序连线 (0→4→5→6)
    if (left_valid_points.size() >= 7)
    {
      cv::line(left_image, left_valid_points[0], left_valid_points[4], cv::Scalar(0, 255, 0), 2, cv::LINE_AA);
      cv::line(left_image, left_valid_points[4], left_valid_points[5], cv::Scalar(0, 255, 0), 2, cv::LINE_AA);
      cv::line(left_image, left_valid_points[5], left_valid_points[6], cv::Scalar(0, 255, 0), 2, cv::LINE_AA);
    }

    // 右图连线：第0、1、2、3个点按照索引顺序连线 (0→1→2→3)
    if (right_valid_points.size() >= 4)
    {
      cv::line(right_image, right_valid_points[0], right_valid_points[1], cv::Scalar(0, 255, 0), 2, cv::LINE_AA);
      cv::line(right_image, right_valid_points[1], right_valid_points[2], cv::Scalar(0, 255, 0), 2, cv::LINE_AA);
      cv::line(right_image, right_valid_points[2], right_valid_points[3], cv::Scalar(0, 255, 0), 2, cv::LINE_AA);
    }

    // 右图连线：第0、4、5、6个点按照索引顺序连线 (0→4→5→6)
    if (right_valid_points.size() >= 7)
    {
      cv::line(right_image, right_valid_points[0], right_valid_points[4], cv::Scalar(0, 255, 0), 2, cv::LINE_AA);
      cv::line(right_image, right_valid_points[4], right_valid_points[5], cv::Scalar(0, 255, 0), 2, cv::LINE_AA);
      cv::line(right_image, right_valid_points[5], right_valid_points[6], cv::Scalar(0, 255, 0), 2, cv::LINE_AA);
    }

    RINFO << "左图绘制了 " << left_valid_points.size() << " 个点，右图绘制了 " << right_valid_points.size() << " 个点";

    // 生成文件名
    std::stringstream ss;
    ss << std::setfill('0') << std::setw(6) << frame_idx;
    std::string frame_str = ss.str();

    // 保存左图为PNG格式
    std::string left_png_filename = (g_output_path / ("point2d_left_" + frame_str + ".png")).string();
    if (cv::imwrite(left_png_filename, left_image))
      RINFO << "保存左图(PNG)到: " << left_png_filename;
    else
      RERROR << "保存左图PNG文件失败: " << left_png_filename;

    // 保存右图为PNG格式
    std::string right_png_filename = (g_output_path / ("point2d_right_" + frame_str + ".png")).string();
    if (cv::imwrite(right_png_filename, right_image))
      RINFO << "保存右图(PNG)到: " << right_png_filename;
    else
      RERROR << "保存右图PNG文件失败: " << right_png_filename;

    // 保存左图为二进制格式
    {
      TagRgbImage rgb_image;
      rgb_image.width = left_width;
      rgb_image.height = left_height;
      rgb_image.timeStamp = std::chrono::duration_cast<std::chrono::nanoseconds>(std::chrono::system_clock::now().time_since_epoch()).count();

      unsigned int data_size = left_width * left_height * 3;
      rgb_image.data = new unsigned char[data_size];

      cv::Mat rgb_mat;
      cv::cvtColor(left_image, rgb_mat, cv::COLOR_BGR2RGB);
      std::memcpy(rgb_image.data, rgb_mat.data, data_size);

      std::string left_bin_filename = (g_output_path / ("point2d_left_" + frame_str + ".bin")).string();
      std::ofstream bin_file(left_bin_filename, std::ios::binary);
      if (bin_file.is_open())
      {
        bin_file.write(reinterpret_cast<const char*>(&rgb_image.width), sizeof(rgb_image.width));
        bin_file.write(reinterpret_cast<const char*>(&rgb_image.height), sizeof(rgb_image.height));
        bin_file.write(reinterpret_cast<const char*>(&rgb_image.timeStamp), sizeof(rgb_image.timeStamp));
        bin_file.write(reinterpret_cast<const char*>(rgb_image.data), data_size);
        bin_file.close();
        RINFO << "保存左图(二进制)到: " << left_bin_filename;
      }
      else
        RERROR << "无法创建左图二进制文件: " << left_bin_filename;

      delete[] rgb_image.data;
    }

    // 保存右图为二进制格式
    {
      TagRgbImage rgb_image;
      rgb_image.width = right_width;
      rgb_image.height = right_height;
      rgb_image.timeStamp = std::chrono::duration_cast<std::chrono::nanoseconds>(std::chrono::system_clock::now().time_since_epoch()).count();

      unsigned int data_size = right_width * right_height * 3;
      rgb_image.data = new unsigned char[data_size];

      cv::Mat rgb_mat;
      cv::cvtColor(right_image, rgb_mat, cv::COLOR_BGR2RGB);
      std::memcpy(rgb_image.data, rgb_mat.data, data_size);

      std::string right_bin_filename = (g_output_path / ("point2d_right_" + frame_str + ".bin")).string();
      std::ofstream bin_file(right_bin_filename, std::ios::binary);
      if (bin_file.is_open())
      {
        bin_file.write(reinterpret_cast<const char*>(&rgb_image.width), sizeof(rgb_image.width));
        bin_file.write(reinterpret_cast<const char*>(&rgb_image.height), sizeof(rgb_image.height));
        bin_file.write(reinterpret_cast<const char*>(&rgb_image.timeStamp), sizeof(rgb_image.timeStamp));
        bin_file.write(reinterpret_cast<const char*>(rgb_image.data), data_size);
        bin_file.close();
        RINFO << "保存右图(二进制)到: " << right_bin_filename;
      }
      else
        RERROR << "无法创建右图二进制文件: " << right_bin_filename;

      delete[] rgb_image.data;
    }

    ++frame_idx;
  }

  file.close();
  RINFO << "2D点处理完成，共处理了 " << frame_idx << " 帧数据";
}

/**
 * @brief 读取 color_point_cloud.bin 文件并转换为 TagPointCloud 格式
 * @note 读取 listereo::ColorPointCloud 格式的二进制文件，转换为通用点云格式，并保存
 */
void readAndProcessColorPointCloudData()
{
  std::string input_file = std::filesystem::path(PROJECT_PATH) / "application" / "test_data" / "color_point_cloud.bin";
  std::ifstream file(input_file, std::ios::binary);

  if (not file.is_open())
  {
    RERROR << "无法打开文件: " << input_file;
    return;
  }

  // 根据 offline_sample.cpp 第102-116行的保存格式读取数据
  // 文件格式：point_num, point_step, timestamp, buffer_size, 实际数据

  unsigned int point_num = 0;
  unsigned int point_step = 0;
  unsigned long long timestamp = 0;
  size_t buffer_size = 0;

  // 读取元数据
  file.read(reinterpret_cast<char*>(&point_num), sizeof(point_num));
  file.read(reinterpret_cast<char*>(&point_step), sizeof(point_step));
  file.read(reinterpret_cast<char*>(&timestamp), sizeof(timestamp));
  file.read(reinterpret_cast<char*>(&buffer_size), sizeof(buffer_size));

  if (file.fail())
  {
    RERROR << "读取文件元数据失败: " << input_file;
    file.close();
    return;
  }

  RINFO << "读取点云元数据 - 点数: " << point_num << ", 每点字节数: " << point_step 
        << ", 时间戳: " << timestamp << "ns, 缓冲区大小: " << buffer_size << " bytes";

  // 验证数据一致性
  size_t expected_size = static_cast<size_t>(point_num) * static_cast<size_t>(point_step);
  if (buffer_size != expected_size)
  {
    RWARN << "缓冲区大小不匹配，期望: " << expected_size << " bytes, 实际: " << buffer_size << " bytes";
  }

  if (point_num == 0 or buffer_size == 0)
  {
    RWARN << "点云数据为空，跳过处理";
    file.close();
    return;
  }

  // 构造 listereo::ColorPointCloud 对象
  robosense::listereo::ColorPointCloud listereo_point_cloud;
  listereo_point_cloud.point_num = point_num;
  listereo_point_cloud.point_step = point_step;
  listereo_point_cloud.timestamp = timestamp;
  listereo_point_cloud.point_cloud_buffer = std::make_shared<std::vector<unsigned char>>(buffer_size);

  // 读取实际的点云数据
  file.read(reinterpret_cast<char*>(listereo_point_cloud.point_cloud_buffer->data()), buffer_size);
  
  if (file.fail())
  {
    RERROR << "读取点云数据失败: " << input_file;
    file.close();
    return;
  }
  
  file.close();
  RINFO << "成功读取点云数据，共 " << point_num << " 个点";

  // 转换为 common::PointCloud 格式（基于 ImageDepth::ConvertListereoColorPointCloudToCommonPointCloud 的实现）
  robosense::common::PointCloud common_point_cloud;

  // 检查缓冲区大小
  size_t expected_buffer_size = static_cast<size_t>(listereo_point_cloud.point_num) * 
                               static_cast<size_t>(listereo_point_cloud.point_step);
  
  if (listereo_point_cloud.point_cloud_buffer->size() < expected_buffer_size)
  {
    RWARN << "缓冲区大小不足: 期望 " << expected_buffer_size 
          << " bytes, 实际 " << listereo_point_cloud.point_cloud_buffer->size() << " bytes";
    return;
  }

  // 预分配输出点云数据
  common_point_cloud.points_vec.resize(listereo_point_cloud.point_num);

  const unsigned char* buffer_ptr = listereo_point_cloud.point_cloud_buffer->data();

  // 解析数据：支持不同的点结构格式
  // ColorPoint3D 结构: X(4字节), Y(4字节), Z(4字节), R(1字节), G(1字节), B(1字节), A(1字节)
  
  for (unsigned int i = 0; i < listereo_point_cloud.point_num; ++i)
  {
    const unsigned char* point_ptr = buffer_ptr + i * listereo_point_cloud.point_step;
    TagPoint& tag_point = common_point_cloud.points_vec[i];

    // 根据 point_step 的大小来判断数据格式
    if (listereo_point_cloud.point_step == sizeof(robosense::listereo::ColorPoint3D))
    {
      // 直接按 ColorPoint3D 结构解析
      const auto* color_point = reinterpret_cast<const robosense::listereo::ColorPoint3D*>(point_ptr);
      
      tag_point.x = color_point->x;
      tag_point.y = color_point->y;
      tag_point.z = color_point->z;
      tag_point.r = color_point->r;
      tag_point.g = color_point->g;
      tag_point.b = color_point->b;
      tag_point.a = color_point->a;
    }
    else if (listereo_point_cloud.point_step >= 12)
    {
      // 通用解析方式：假设前12字节是XYZ坐标
      std::memcpy(&tag_point.x, point_ptr + 0, sizeof(float));
      std::memcpy(&tag_point.y, point_ptr + 4, sizeof(float));
      std::memcpy(&tag_point.z, point_ptr + 8, sizeof(float));

      // 解析颜色信息（如果存在）
      if (listereo_point_cloud.point_step >= 15)
      {
        tag_point.r = *(point_ptr + 12);
        tag_point.g = *(point_ptr + 13);
        tag_point.b = *(point_ptr + 14);
      }
      else
      {
        tag_point.r = tag_point.g = tag_point.b = 255; // 默认白色
      }

      // 解析Alpha通道
      if (listereo_point_cloud.point_step >= 16)
      {
        tag_point.a = *(point_ptr + 15);
      }
      else
      {
        tag_point.a = 255; // 默认不透明
      }
    }
    else
    {
      RWARN << "点步长太小: " << listereo_point_cloud.point_step 
            << ", 期望至少12字节用于XYZ坐标";
      tag_point.x = tag_point.y = tag_point.z = 0.0f;
      tag_point.r = tag_point.g = tag_point.b = tag_point.a = 255;
    }

    // 设置其他字段为默认值
    tag_point.nx = tag_point.ny = tag_point.nz = 0.0f; // 法向量
    tag_point.intensity = 0.0f; // 强度
    tag_point.label = 0; // 标签
    tag_point.timeStamp = listereo_point_cloud.timestamp; // 时间戳
  }

  RINFO << "转换 listereo::ColorPointCloud 到 common::PointCloud: "
        << listereo_point_cloud.point_num << " 个点, "
        << listereo_point_cloud.point_step << " bytes per point, "
        << "timestamp: " << listereo_point_cloud.timestamp << "ns";

  // 调用 ToOutPointCloud 生成 TagPointCloud 结构
  TagPointCloud tag_point_cloud = common_point_cloud.ToOutPointCloud();
  
  RINFO << "生成 TagPointCloud 结构，包含 " << tag_point_cloud.size << " 个点";

  // 保存 TagPointCloud 到新的二进制文件
  std::string output_file = (g_output_path / "converted_point_cloud.bin").string();
  std::ofstream out_file(output_file, std::ios::binary);
  
  if (not out_file.is_open())
  {
    RERROR << "无法创建输出文件: " << output_file;
    return;
  }

  // 保存 TagPointCloud 结构
  // 格式：size, 然后是所有点的数据
  out_file.write(reinterpret_cast<const char*>(&tag_point_cloud.size), sizeof(tag_point_cloud.size));
  
  if (tag_point_cloud.size > 0 and tag_point_cloud.data)
  {
    size_t data_size = static_cast<size_t>(tag_point_cloud.size) * sizeof(TagPoint);
    out_file.write(reinterpret_cast<const char*>(tag_point_cloud.data), data_size);
  }
  
  out_file.close();
  
  if (out_file.good())
  {
    RINFO << "成功保存转换后的点云到: " << output_file 
          << " (包含 " << tag_point_cloud.size << " 个点，总大小: " 
          << (sizeof(unsigned int) + static_cast<size_t>(tag_point_cloud.size) * sizeof(TagPoint)) 
          << " bytes)";
  }
  else
  {
    RERROR << "保存点云文件时出错: " << output_file;
  }

  // 保存 TagPointCloud 为 PCL 的 PCD 文件
  if (tag_point_cloud.size > 0 and tag_point_cloud.data)
  {
    // 创建 PCL 点云对象
    pcl::PointCloud<pcl::PointXYZRGBA>::Ptr pcl_cloud(new pcl::PointCloud<pcl::PointXYZRGBA>);
    pcl_cloud->width = tag_point_cloud.size;
    pcl_cloud->height = 1;
    pcl_cloud->is_dense = false;
    pcl_cloud->points.resize(pcl_cloud->width * pcl_cloud->height);

    // 转换 TagPoint 数据到 PCL 格式
    for (unsigned int i = 0; i < tag_point_cloud.size; ++i)
    {
      const TagPoint& tag_point = tag_point_cloud.data[i];
      pcl::PointXYZRGBA& pcl_point = pcl_cloud->points[i];
      
      // 复制坐标
      pcl_point.x = tag_point.x;
      pcl_point.y = tag_point.y;
      pcl_point.z = tag_point.z;
      
      // 复制颜色信息
      pcl_point.r = tag_point.r;
      pcl_point.g = tag_point.g;
      pcl_point.b = tag_point.b;
      pcl_point.a = tag_point.a;
    }

    // 保存为 ASCII 格式的 PCD 文件
    std::string pcd_filename = (g_output_path / "converted_point_cloud.pcd").string();
    if (pcl::io::savePCDFileASCII(pcd_filename, *pcl_cloud) == 0)
    {
      RINFO << "成功保存 PCD 文件到: " << pcd_filename 
            << " (包含 " << tag_point_cloud.size << " 个点)";
    }
    else
    {
      RERROR << "保存 PCD 文件失败: " << pcd_filename;
    }

    // 另外保存为二进制格式的 PCD 文件（文件更小）
    std::string pcd_binary_filename = (g_output_path / "converted_point_cloud_binary.pcd").string();
    if (pcl::io::savePCDFileBinary(pcd_binary_filename, *pcl_cloud) == 0)
    {
      RINFO << "成功保存二进制 PCD 文件到: " << pcd_binary_filename 
            << " (包含 " << tag_point_cloud.size << " 个点)";
    }
    else
    {
      RERROR << "保存二进制 PCD 文件失败: " << pcd_binary_filename;
    }
  }
  else
  {
    RWARN << "TagPointCloud 数据为空，跳过 PCD 文件保存";
  }
}

void readAndProcessDepthFloatData()
{
  // 深度图像的固定尺寸（基于offline_sample.cpp中的信息）
  const unsigned int depth_width = 1600;
  const unsigned int depth_height = 1200;
  const unsigned int depth_bits_size = 32; // 32位float
  const size_t pixel_count = static_cast<size_t>(depth_width) * static_cast<size_t>(depth_height);
  const size_t expected_file_size = pixel_count * sizeof(float);

  std::string input_file = std::filesystem::path(PROJECT_PATH) / "application" / "test_data" / "depth_float.bin";
  std::ifstream file(input_file, std::ios::binary);

  if (not file.is_open())
  {
    RERROR << "无法打开深度文件: " << input_file;
    return;
  }

  // 获取文件大小
  file.seekg(0, std::ios::end);
  size_t file_size = file.tellg();
  file.seekg(0, std::ios::beg);

  if (file_size != expected_file_size)
  {
    RWARN << "深度文件大小异常，期望: " << expected_file_size << " bytes, 实际: " << file_size << " bytes";
  }

  if (file_size == 0)
  {
    RERROR << "深度文件为空";
    file.close();
    return;
  }

  // 读取深度数据
  std::vector<unsigned char> depth_buffer(file_size);
  file.read(reinterpret_cast<char*>(depth_buffer.data()), file_size);
  
  if (file.fail())
  {
    RERROR << "读取深度文件失败: " << input_file;
    file.close();
    return;
  }
  
  file.close();
  RINFO << "成功读取深度数据，文件大小: " << file_size << " bytes";

  // 创建 listereo::DepthImage 对象
  robosense::listereo::DepthImage listereo_depth_image;
  listereo_depth_image.img_width = depth_width;
  listereo_depth_image.img_height = depth_height;
  listereo_depth_image.bits_size = depth_bits_size;
  listereo_depth_image.timestamp = std::chrono::duration_cast<std::chrono::nanoseconds>(
      std::chrono::system_clock::now().time_since_epoch()).count();
  listereo_depth_image.depth_buffer = std::make_shared<std::vector<unsigned char>>(std::move(depth_buffer));

  RINFO << "创建 listereo::DepthImage: " << listereo_depth_image.img_width << "x" 
        << listereo_depth_image.img_height << ", bits_size: " << listereo_depth_image.bits_size
        << ", timestamp: " << listereo_depth_image.timestamp << "ns";

  // 转换为 common::DepthImage
  robosense::common::DepthImage common_depth_image = 
      robosense::imagedepth::ImageDepth::ConvertListereoDepthImageToCommonDepthImage(listereo_depth_image);

  if (common_depth_image.data_vec.empty())
  {
    RERROR << "深度图转换失败，输出数据为空";
    return;
  }

  RINFO << "转换为 common::DepthImage: " << common_depth_image.width << "x" 
        << common_depth_image.height << ", 包含 " << common_depth_image.data_vec.size() << " 个像素";

  // 转换为 TagDepthImage
  TagDepthImage tag_depth_image = common_depth_image.ToOutDepthImage();

  if (tag_depth_image.data == nullptr)
  {
    RERROR << "TagDepthImage 转换失败，数据指针为空";
    return;
  }

  RINFO << "转换为 TagDepthImage: " << tag_depth_image.width << "x" 
        << tag_depth_image.height << ", timestamp: " << tag_depth_image.timeStamp << "ns";

  // 保存 TagDepthImage 到二进制文件
  std::string output_file = (g_output_path / "converted_depth_image.bin").string();
  std::ofstream out_file(output_file, std::ios::binary);
  
  if (not out_file.is_open())
  {
    RERROR << "无法创建输出深度文件: " << output_file;
    return;
  }

  // 保存 TagDepthImage 结构
  // 格式：width, height, timestamp, 深度数据
  out_file.write(reinterpret_cast<const char*>(&tag_depth_image.width), sizeof(tag_depth_image.width));
  out_file.write(reinterpret_cast<const char*>(&tag_depth_image.height), sizeof(tag_depth_image.height));
  out_file.write(reinterpret_cast<const char*>(&tag_depth_image.timeStamp), sizeof(tag_depth_image.timeStamp));
  
  if (tag_depth_image.data)
  {
    size_t data_size = static_cast<size_t>(tag_depth_image.width) * 
                      static_cast<size_t>(tag_depth_image.height) * sizeof(float);
    out_file.write(reinterpret_cast<const char*>(tag_depth_image.data), data_size);
  }
  
  out_file.close();
  
  if (out_file.good())
  {
    size_t total_output_size = sizeof(tag_depth_image.width) + sizeof(tag_depth_image.height) + 
                              sizeof(tag_depth_image.timeStamp) + 
                              (static_cast<size_t>(tag_depth_image.width) * 
                               static_cast<size_t>(tag_depth_image.height) * sizeof(float));
    
    RINFO << "成功保存转换后的深度图到: " << output_file 
          << " (尺寸: " << tag_depth_image.width << "x" << tag_depth_image.height 
          << ", 总大小: " << total_output_size << " bytes)";
  }
  else
  {
    RERROR << "保存深度图文件时出错: " << output_file;
  }

  // 另外保存一个纯深度数据文件（兼容原始格式）
  std::string raw_output_file = (g_output_path / "converted_depth_raw.bin").string();
  std::ofstream raw_out_file(raw_output_file, std::ios::binary);
  
  if (raw_out_file.is_open() && tag_depth_image.data)
  {
    size_t raw_data_size = static_cast<size_t>(tag_depth_image.width) * 
                          static_cast<size_t>(tag_depth_image.height) * sizeof(float);
    raw_out_file.write(reinterpret_cast<const char*>(tag_depth_image.data), raw_data_size);
    raw_out_file.close();
    
    RINFO << "保存原始深度数据到: " << raw_output_file << " (大小: " << raw_data_size << " bytes)";
  }
  else
  {
    RERROR << "无法保存原始深度数据文件: " << raw_output_file;
  }
}

int main(int argc, char **argv) {
  signal(SIGINT, sigHandler);

  auto config_file_fs =
      std::filesystem::path(PROJECT_PATH) / "config" / "usr_config.yaml";
  if (argc == 2) {
    std::string folderPath = argv[1];
    config_file_fs =
        std::filesystem::path(folderPath) / "Config" / "usr_config.yaml";
  }

  SetPlayMode(1);

  g_output_path = std::filesystem::path(PROJECT_PATH) / "build" / "saved_data";
  if (!std::filesystem::exists(g_output_path))
  {
    std::filesystem::create_directories(g_output_path);
    RINFO << "创建输出文件夹: " << g_output_path.string();
  }

  std::string config_file = config_file_fs.string();
  OnInitialize(config_file.data(), false);

//  auto th1 = std::thread(getDevices);

//  readAndProcessPoint3DData();
//  readAndProcessPoint2DData();
//  readAndProcessColorPointCloudData();
  readAndProcessDepthFloatData();

  std::exit(0);
#if 0
  auto func = [](void *cloud_msg, void *rgb_msg, void *depth_msg) {
    auto pc_ptr = static_cast<TagPointCloud *>(cloud_msg);
    auto rgb_image_ptr = static_cast<TagRgbImage *>(rgb_msg);
    auto depth_image_ptr = static_cast<TagDepthImage *>(depth_msg);
    static int frame_cc{0};
    RINFO << "receive frame idx " << frame_cc;

    // 生成文件名
    std::stringstream ss;
    ss << std::setfill('0') << std::setw(6) << frame_cc;
    std::string frame_str = ss.str();

    // 保存点云数据
    if (pc_ptr and pc_ptr->data and pc_ptr->size > 0)
    {
      // 创建PCL点云对象
      pcl::PointCloud<pcl::PointXYZRGB>::Ptr pcl_cloud(new pcl::PointCloud<pcl::PointXYZRGB>);
      pcl_cloud->width = pc_ptr->size;
      pcl_cloud->height = 1;
      pcl_cloud->is_dense = false;
      pcl_cloud->points.resize(pcl_cloud->width * pcl_cloud->height);

      // 转换数据格式
      for (unsigned int i = 0; i < pc_ptr->size; ++i)
      {
        pcl_cloud->points[i].x = pc_ptr->data[i].x;
        pcl_cloud->points[i].y = pc_ptr->data[i].y;
        pcl_cloud->points[i].z = pc_ptr->data[i].z;
        pcl_cloud->points[i].r = pc_ptr->data[i].r;
        pcl_cloud->points[i].g = pc_ptr->data[i].g;
        pcl_cloud->points[i].b = pc_ptr->data[i].b;
      }

      std::string pcd_filename = (g_output_path / ("point_cloud_" + frame_str + ".pcd")).string();
      if (pcl::io::savePCDFileASCII(pcd_filename, *pcl_cloud) == 0)
      {
        RINFO << "保存点云数据到: " << pcd_filename << " (点数: " << pc_ptr->size << ")";
      }
      else
      {
        RERROR << "保存PCD文件失败: " << pcd_filename;
      }

      std::string pc_filename = (g_output_path / ("point_cloud_" + frame_str + ".bin")).string();
      std::ofstream pc_file(pc_filename, std::ios::binary);
      if (pc_file.is_open())
      {
        // 先写入点云大小
        pc_file.write(reinterpret_cast<const char*>(&pc_ptr->size), sizeof(pc_ptr->size));
        // 写入点云数据
        pc_file.write(reinterpret_cast<const char*>(pc_ptr->data), pc_ptr->size * sizeof(TagPoint));
        pc_file.close();
        RINFO << "保存点云数据到: " << pc_filename << " (点数: " << pc_ptr->size << ")";
      }
      else
      {
        RERROR << "无法创建点云文件: " << pc_filename;
      }

    }

    // 保存RGB图像数据
    if (rgb_image_ptr and rgb_image_ptr->data and
        rgb_image_ptr->width > 0 and rgb_image_ptr->height > 0)
    {
      // 创建OpenCV Mat对象 (注意：TagRgbImage是RGB格式，OpenCV默认使用BGR)
      cv::Mat rgb_image(rgb_image_ptr->height, rgb_image_ptr->width, CV_8UC3, rgb_image_ptr->data);
      cv::Mat bgr_image;
      cv::cvtColor(rgb_image, bgr_image, cv::COLOR_RGB2BGR);

      std::string jpg_filename = (g_output_path / ("rgb_image_" + frame_str + ".jpg")).string();
      
      // 设置JPEG压缩质量 (0-100, 100为最高质量)
      std::vector<int> compression_params;
      compression_params.push_back(cv::IMWRITE_JPEG_QUALITY);
      compression_params.push_back(80);

      if (cv::imwrite(jpg_filename, bgr_image, compression_params))
      {
        RINFO << "保存RGB图像到: " << jpg_filename << " (尺寸: " << rgb_image_ptr->width
              << "x" << rgb_image_ptr->height << ")";
      }
      else
      {
        RERROR << "保存JPG文件失败: " << jpg_filename;
      }

      std::string rgb_filename = (g_output_path / ("rgb_image_" + frame_str + ".bin")).string();
      std::ofstream rgb_file(rgb_filename, std::ios::binary);
      if (rgb_file.is_open())
      {
        // 写入图像元数据
        rgb_file.write(reinterpret_cast<const char*>(&rgb_image_ptr->width), sizeof(rgb_image_ptr->width));
        rgb_file.write(reinterpret_cast<const char*>(&rgb_image_ptr->height), sizeof(rgb_image_ptr->height));
        rgb_file.write(reinterpret_cast<const char*>(&rgb_image_ptr->timeStamp), sizeof(rgb_image_ptr->timeStamp));
        // 写入图像数据 (RGB 24位，每像素3字节)
        unsigned int data_size = rgb_image_ptr->width * rgb_image_ptr->height * 3;
        rgb_file.write(reinterpret_cast<const char*>(rgb_image_ptr->data), data_size);
        rgb_file.close();
        RINFO << "保存RGB图像到: " << rgb_filename << " (尺寸: " << rgb_image_ptr->width
              << "x" << rgb_image_ptr->height << ")";
      }
      else
      {
        RERROR << "无法创建RGB图像文件: " << rgb_filename;
      }

    }

    RINFO << "========================================";
    frame_cc++;
  };

  SetPostProcessMsgHandler(func);

  OnStart();

  while (start_) {
#ifdef _WIN32
    std::this_thread::sleep_for(std::chrono::seconds(1));
#else
    sleep(1);
#endif
  }
  OnDestroy();

  if (th1.joinable()) {
    th1.join();
  }
#endif
  return 0;
}
