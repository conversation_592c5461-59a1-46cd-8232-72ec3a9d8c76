if(APPLE)
    # 启用 RPATH
    set(CMAKE_INSTALL_RPATH "@executable_path/../Frameworks")
    set(CMAKE_BUILD_WITH_INSTALL_RPATH TRUE)
    set(CMAKE_INSTALL_RPATH_USE_LINK_PATH TRUE)

    set(CUR_NAME front_end_app)
    # 创建应用程序 bundle
    add_executable(${CUR_NAME} MACOSX_BUNDLE
            front_end_app.cpp)

    # 设置 bundle 属性
    set_target_properties(${CUR_NAME} PROPERTIES
        MACOSX_BUNDLE TRUE
        MACOSX_BUNDLE_GUI_IDENTIFIER "com.distant1219.${CUR_NAME_NAME}"
        MACOSX_BUNDLE_BUNDLE_NAME ${CUR_NAME}
        MACOSX_BUNDLE_BUNDLE_VERSION ${PROJECT_VERSION}
        MACOSX_BUNDLE_SHORT_VERSION_STRING ${PROJECT_VERSION}
        MACOSX_BUNDLE_COPYRIGHT "Copyright © 2025 distant1219"
        # MACOSX_BUNDLE_INFO_PLIST "${CMAKE_CURRENT_SOURCE_DIR}/Info.plist.in"
    )

    # 链接动态库
    target_link_libraries(${CUR_NAME}
    interface
    )
else()
    # # linux support ros 
    #  if(UNIX AND NOT APPLE)
    #      find_package(catkin QUIET COMPONENTS roscpp)
    #      if(catkin_FOUND AND roscpp_FOUND)
    #         add_subdirectory(ros_app)
    #      endif()
    #  endif()
    
    add_executable(front_end_app
            front_end_app.cpp
            )
    
    if(WIN32)
        target_link_libraries(front_end_app libinterface)
    else()
        target_link_libraries(front_end_app interface)
    endif()
    
    # socket interface 
#    add_executable(front_end_socket
#    front_end_socket.cpp
#    )

#    if(WIN32)
#        target_link_libraries(front_end_socket libinterface)
#    else()
#        target_link_libraries(front_end_socket interface)
#    endif()
endif()
